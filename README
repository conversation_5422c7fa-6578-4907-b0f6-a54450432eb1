## TODO
bp为3不生效的问题
这段时间的所有笔记,是不是都可以放在这里,或者sublime上面

## 代码流程
* 入口文件是access.lua和bypass.lua
* access中获取service_name,有一定的回退
* access.lua做一些基本检查,然后upstream到flow.lua?

## 基础信息
更新服务所用到的key:
get p-3ufc-beijing-service-pcs-pss-muiproxy
get p-3ufc-beijing-service-pcs-pss-muiproxy-mtime

backup集群vip:***********:8240
tc-proxy-platform00.tc
tc-proxy-platform01.tc
m1-pcs-mongo-arbiter1.m1
m1-pcs-mongo-meta1.m1
cq01-proxy-platform00.cq01
cq01-proxy-platform01.cq01
cq02-pcs-ufc-bc01.cq02  // 少一台00的机器
cq02-pcs-ufc-bc03.cq02
cq02-pcs-ufc-bc04.cq02
cq02-pcs-ufc-bc05.cq02
cq02-pcs-ufc-bc06.cq02
cq02-pcs-ufc-bc07.cq02
cq02-bae-pssui08.cq02

ufc-sdk:http://icode.baidu.com/repos/baidu/netdisk/pcs-ufc-sdk/tree/master

service头部:x-ufc-service-name
hash-key  只有bp为7才生效
小流量头部:x-http-flow-control
logid头部:http-x-isis-logid    TODO 这个可能要换
callid头部:http-x-isis-callid


## 上线流程
### ufc各机房BNS  BAIDU_MCO_CLOUD_cloud-storage_ufc-split
* 各个机房的机器数是否ok
    * gz需要重新挂载机器，跨产品线借用,找慧坤过单，很给力
* 将ufc-split里面的bns，拆成几个上线单
    * bj\gz\hz
    * nb
    * nj01
    * nj02    nj02-04和qd01-04需要换一下
    * qd01
    * sh01\small\yq01
* 注意要上线backup,但是有2点不同
    * redis方式是写死的
    * worker数要多一些, 目前是5

* 上线单BNS分组: # 目的是单词上线bns数在一定范围内,并且总机器数也在一定范围内.否则noah会卡住
    ufc-bj-01-4.cloud-storage.bj ufc-gz-01.cloud-storage.gz ufc-hz01-01.cloud-storage.hz01
    ufc-nb01-01-6.cloud-storage.nb01
    ufc-nj01-01-5.cloud-storage.nj01
    ufc-nj02-01-6.cloud-storage.nj02
    ufc-qd01-01-6.cloud-storage.qd01
    ufc-sh01-01.cloud-storage.sh01 ufc-small-01.cloud-storage.small ufc-yq01-01-4.cloud-storage.yq01

netdisk的bns列表:
    group.front.netdisk.all
    group.backendall.netdisk.all
    c-module.netdisk.all
    group.logctrl.netdisk.all
    group.traces.netdisk.all
    group.update.netdisk.all
    jx-xiaoliuliang-inner.netdisk.jx
    tc-xiaoliuliang-inner.netdisk.tc
    disasterRecovery.netdisk.jx
    disasterRecovery.netdisk.tc
    jx-manager.netdisk.jx



## 以诚的ccproxy
* druid地址:***********:7000   cp01-pss-mongo-ssd00.cp01  目录还不确定



------分割线--------
curl -v '127.0.0.1:8240/bypass?cmd=debug&key=static_cache^services^pcs-pss-muiproxy'
curl -v '127.0.0.1:8240/bypass?cmd=debug&key=dynamic_cache^pcs-pss-muiproxy'

#### 静态配置
各种bp策略(_round_robin_select)
_is_backend_ok
_is_backend_can_connect
_unforbid_backend

curl -v "localhost:8240/bypass?cmd=debug&key=static_cache^bnss^bj-ct-proxy-platform.cloud-storage.all"
```
{
    "mtime": "1502867009",
    "bns_nodes": [
        {
            "host": "************",
            "port": 8083
        }
        XXX
    ]
}
```

curl -v "localhost:8240/bypass?cmd=debug&key=static_cache^services^pcs-idcproxy-bj-ct"
```
{
    "max_global_running_cnt": 3,
    "mtime": "1502367838",
    "4xx_to_406_percent": 0,
    "product": "pcs",
    "request_reset_interval": 10,
    "bns_list": {
        "pcs-online-pcs-network-idcproxy2cu.orp.tc": 10,
        "bj-ct-proxy-platform.cloud-storage.all": 90
    },
    "illegal_headers": {

    },
    "send_timeout": 3000,
    "limit_qps_all": 0,
    "5xx_to_507_percent": 0,
    "max_forbid_percent": 25,
    "max_fail": 1,
    "connect_timeout": 1000,
    "forbid_timeout": 120,
    "read_timeout": 30000,
    "black_list": {

    },
    "worker_timeout": 3600,
    "qps_drop_rate": 0,
    "vpn_percent": 100,
    "backends_max_tries": 3,
    "service_name": "pcs-idcproxy-bj-ct",
    "bp": 1
}
```
#### 动态配置
curl -v "localhost:8240/bypass?cmd=debug&key=dynamic_cache^pcs-idcproxy-bj-ct"
```
{
    "current_index": {
        "pcs-online-pcs-network-idcproxy2cu.orp.tc": 4,
        "bj-ct-proxy-platform.cloud-storage.all": 30
    },
    "service": {
        "forbidden_cnt": 7,
        "request_cnt": 8,
        "ctime": 1503567742,
        "success_cnt": 0
    },
    "backend": {
        "************:8083": {
            "ftime": 1503567721,
            "forbidden": false,
            "response_times": 0,
            "ctime": 1503567706,
            "fail_cnt": 2,
            "request_cnt": 0,
            "success_cnt": 0
        },
        "************:8083": {
            "ftime": 1503567745,
            "forbidden": true,
            "response_times": 0,
            "ctime": 1503567745,
            "fail_cnt": 1,
            "request_cnt": 0,
            "success_cnt": 0
        }
    }
}
```



累积升级:
2017-11
ufc封禁检查逻辑:
第一部分（callback封禁）：
1.1、如果list还是空的，那么就init一个节点，并让last=list
1.2、如果list已经有元素了，那么就在last后面添加一个节点，并让last向后走一步。不操作list

第二部分（异步检查）：
2.1、反查动态配置，已经解封的，直接从list中删除
2.2、不能解封，last后添加一个节点，list向后走一步
2.3、需要解封，list向后走一步，释放当前节点