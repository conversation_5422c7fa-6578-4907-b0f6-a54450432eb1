#!/bin/sh
stop() {
    for pid in $(ps -ef | grep './pvcp-ebpf-agent' | grep -vE 'grep|stop' | awk '{print $2}'); do
        kill -9 $pid
    done

    sleep 0.5

    pid_num=`ps -ef | grep './pvcp-ebpf-agent' | grep -v grep | wc -l`
    if [ $pid_num -gt 0 ]
    then
        for pid in $(ps -ef | grep './pvcp-ebpf-agent' | grep -vE 'grep|stop' | awk '{print $2}'); do
            kill -9 $pid
            sleep 0.5
        done
    fi
}

start() {
    cd /home/<USER>/nginx_ufc/ebpf
    mkdir -p log
    ./pvcp-ebpf-agent &
    sleep 1
    chown -R bae:bae log/
}

restart() {
    stop
    sleep 0.5
    start
}

case "$1" in
    restart)
        restart
    ;;
    stop)
        stop
    ;;
    start)
        restart
    ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
    ;;
esac