#! /bin/bash

cd /home/<USER>/nginx_ufc
if [ ! -f "./ufc/meta_cache/conf_cache_file" ]; then
    cp  ./ufc/meta_cache/conf_cache_file_back ./ufc/meta_cache/conf_cache_file
    echo "copy conf done"
    if [[ $? -ne 0 ]]; then   # 复制失败
        echo "cp conf failed, maybe disk full"
        exit 2
    fi
fi

mkdir -p logs
ufc_exist=`find . -name ufc|wc -l`
if [ $ufc_exist -eq 0 ]
then
    echo "no ufc path found, give up start it"
    exit 2
fi

pid_file_exist=`find logs -name nginx.pid|wc -l`
if [ $pid_file_exist -ne 0 ]
then
    pid=`cat logs/nginx.pid`
    process=`ps -ef|awk '{print $2,$11}'|grep $pid|awk '{print $2}'|awk -F "/" '{print $3}'`

    if [ "x"$process == "xnginx_ufc" ]
    then
        echo "nginx_ufc is still runing, give up start it"
        exit 2
    fi
fi

zk_so_path=`pwd`/lualib
luajit_so_path=`pwd`/luajit/lib
ld_library_path=`echo $LD_LIBRARY_PATH`
old_ifs=$IFS
IFS=":"
arr=($ld_library_path) 
IFS=$old_ifs

need_add_path=1
for s in ${arr[@]}
do 
    if [ x$s == x$zk_so_path ]
    then
        need_add_path=0
    fi
done
if [ $need_add_path -eq 1 ]
then
    export LD_LIBRARY_PATH=$zk_so_path:$ld_library_path
fi

ld_library_path=`echo $LD_LIBRARY_PATH`
old_ifs=$IFS
IFS=":"
arr=($ld_library_path) 
IFS=$old_ifs
need_add_path=1
for s in ${arr[@]}
do 
    if [ x$s == x$luajit_so_path ]
    then
        need_add_path=0
    fi
done
if [ $need_add_path -eq 1 ]
then
    export LD_LIBRARY_PATH=$luajit_so_path:$ld_library_path
fi

# for worker nums
hostname=`hostname`

if [[ $hostname == *"ufc"* || $hostname == *"tc-proxy-platform0"* || $hostname == *"cq01-proxy-platform0"* || $hostname == *"mongo-arbiter1.m1" || $hostname == *"mongo-meta1"* || $hostname == *"bae-pssui08"* ]]; then
    cp -arf conf/nginx.conf_8worker conf/nginx.conf
else
    cp -arf conf/nginx.conf_6worker conf/nginx.conf
fi

#判断地域是否合法
if [ ! -f "./ufc/area.txt" ] 
then
    #地域文件不存在
    echo "area.txt is not exit"
    exit 2
fi

area_info=($(awk '{print $1}' ./ufc/area.txt))
if [ ${#area_info[@]} -ne 3 ]
then 
    echo "area.txt is not correct"
    exit 2
fi

idc_tmp=`echo $hostname|awk -F '.' '{print $2}'`
old_ifs=$IFS
IFS=";"
idcs=${area_info[1]} 

if [ ${area_info[0]} == inland ]
then
    #内地是黑名单
    for idc in ${idcs[@]}
    do
        if [ $idc_tmp == $idc ]
        then
            echo "this is an wrong area"
            exit 2
        fi
    done
else
    #海外是白名单
    res=0
    for idc in ${idcs[@]}
    do
        if [ $idc_tmp == $idc ]
        then
            res=1
            break
        fi
    done

    if [ $res -ne 1 ]
    then
            echo "this is an wrong area"
            exit 2
    fi
fi

IFS=$old_ifs
rm -rf ufc/meta_cache/conf_cache_file.*

./sbin/nginx_ufc -c conf/nginx.conf -p .
if [[ $? -ne 0 ]]; then
    echo "start nginx_ufc fail"
    exit 2
fi


echo "start nginx_ufc success"

if [[ -f "/home/<USER>/nginx_ufc/watchdog/start.sh" ]]; then
    sh /home/<USER>/nginx_ufc/watchdog/start.sh
    if [[ $? -ne 0 ]]; then
        echo "start watchdog fail"
        exit 2
    fi
    echo "start watchdog success"
fi 

exit 0

