<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<html>

<head>
<title>Lua 5.1 Reference Manual</title>
<link rel="stylesheet" href="lua.css">
</head>

<body bgcolor="#FFFFFF">

<hr></hr>
<h1>
<a href="http://www.lua.org/home.html"><img src="logo.gif" alt="[Lua logo]" border="0"></img></a>
Lua 5.1 Reference Manual
</h1>

by <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
<p>
<small>
<a href="http://www.lua.org/copyright.html">Copyright</a>
&copy; 2006 Lua.org, PUC-Rio.  All rights reserved.
</small>
<hr></hr>

<p>
<p>
<!-- ====================================================================== -->


<a name="1"></a><h1>1 - Introduction</h1>

<p>Lua is an extension programming language designed to support
general procedural programming with data description
facilities.
It also offers good support for object-oriented programming,
functional programming, and data-driven programming.
Lua is intended to be used as a powerful, light-weight
scripting language for any program that needs one.
Lua is implemented as a library, written in <em>clean</em> C
(that is, in the common subset of ANSI C and C++).

<p>Being an extension language, Lua has no notion of a "main" program:
it only works <em>embedded</em> in a host client,
called the <em>embedding program</em> or simply the <em>host</em>.
This host program can invoke functions to execute a piece of Lua code,
can write and read Lua variables,
and can register C functions to be called by Lua code.
Through the use of C functions, Lua can be augmented to cope with
a wide range of different domains,
thus creating customized programming languages sharing a syntactical framework.
The Lua distribution includes a sample host program called <code>lua</code>,
which uses the Lua library to offer a complete, stand-alone Lua interpreter.

<p>Lua is free software,
and is provided as usual with no guarantees,
as stated in its license.
The implementation described in this manual is available
at Lua's official web site, <code>www.lua.org</code>.

<p>Like any other reference manual,
this document is dry in places.
For a discussion of the decisions behind the design of Lua,
see the technical papers available at Lua's web site.
For a detailed introduction to programming in Lua,
see Roberto's book, <em>Programming in Lua</em>.

<p>
<a name="language"></a><a name="2"></a><h1>2 - The Language</h1>

<p>This section describes the lexis, the syntax, and the semantics of Lua.
In other words,
this section describes
which tokens are valid,
how they can be combined,
and what their combinations mean.

<p>The language constructs will be explained using the usual extended BNF notation,
in which
{<em>a</em>} means 0 or more <em>a</em>'s, and
[<em>a</em>] means an optional <em>a</em>.
Non-terminals are shown in <em>italics</em>,
keywords are shown in <b>bold</b>,
and other terminal symbols are shown in <code>typewriter</code> font,
enclosed in single quotes.
The complete syntax of Lua can be found at the end of this manual.

<p><a name="lexical"></a><a name="2.1"></a><h2>2.1 - Lexical Conventions</h2>

<p><em>Names</em>
(also called <em>identifiers</em>)
in Lua can be any string of letters,
digits, and underscores,
not beginning with a digit.
This coincides with the definition of names in most languages.
(The definition of letter depends on the current locale:
any character considered alphabetic by the current locale
can be used in an identifier.)
Identifiers are used to name variables and table fields.

<p>The following <em>keywords</em> are reserved
and cannot be used as names:

<pre>
       and       break     do        else      elseif
       end       false     for       function  if
       in        local     nil       not       or
       repeat    return    then      true      until     while
</pre>

<p>Lua is a case-sensitive language:
<code>and</code> is a reserved word, but <code>And</code> and <code>AND</code>
are two different, valid names.
As a convention, names starting with an underscore followed by
uppercase letters (such as <a href="#pdf-_VERSION"><code>_VERSION</code></a>)
are reserved for internal global variables used by Lua.

<p>The following strings denote other tokens:
<pre>
       +     -     *     /     %     ^     #
       ==    ~=    &#060;=    >=    &#060;     >     =
       (     )     {     }     [     ]
       ;     :     ,     .     ..    ...
</pre>

<p><em>Literal strings</em>
can be delimited by matching single or double quotes,
and can contain the following C-like escape sequences:
<ul>
<li><b><code>\a</code></b> --- bell
<li><b><code>\b</code></b> --- backspace
<li><b><code>\f</code></b> --- form feed
<li><b><code>\n</code></b> --- newline
<li><b><code>\r</code></b> --- carriage return
<li><b><code>\t</code></b> --- horizontal tab
<li><b><code>\v</code></b> --- vertical tab
<li><b><code>\\</code></b> --- backslash
<li><b><code>\"</code></b> --- quotation mark (double quote)
<li><b><code>\'</code></b> --- apostrophe (single quote)
</ul>
Moreover, a `<code>\</code><em>newline</em>&acute;
(that is, a backslash followed by a real newline)
results in a newline in the string.
A character in a string may also be specified by its numerical value
using the escape sequence `<code>\</code><em>ddd</em>&acute;,
where <em>ddd</em> is a sequence of up to three decimal digits.
(Note that if a numerical escape is to be followed by a digit,
it must be expressed using exactly three digits.)
Strings in Lua may contain any 8-bit value, including embedded zeros,
which can be specified as `<code>\0</code>&acute;.

<p>To put a double (single) quote, a newline, a backslash,
or an embedded zero
inside a literal string enclosed by double (single) quotes
you must use an escape sequence.
Any other character may be directly inserted into the literal.
(Some control characters may cause problems for the file system,
but Lua has no problem with them.)

<p>Literal strings can also be defined using a long format
enclosed by <em>long brackets</em>.
We define an <em>opening long bracket of level <em>n</em></em> as an opening
square bracket followed by <em>n</em> equal signs followed by another
opening square bracket.
So, an opening long bracket of level 0 is written as <code>[[</code>,
an opening long bracket of level 1 is written as <code>[=[</code>,
and so on.
A <em>closing long bracket</em> is defined similarly;
for instance, a closing long bracket of level 4 is written as <code>]====]</code>.
A long string starts with an opening long bracket of any level and
ends at the first closing long bracket of the same level.
Literals in this bracketed form may run for several lines,
do not interpret any escape sequences,
and ignore long brackets of any other level.
They may contain anything except a closing bracket of the proper level
or embedded zeros.

<p>For convenience,
when the opening long bracket is immediately followed by a newline,
the newline is not included in the string.
As an example, in a system using ASCII
(in which `<code>a</code>&acute; is coded as 97,
newline is coded as 10, and `<code>1</code>&acute; is coded as 49),
the four literals below denote the same string:
<pre>
      (1)   'alo\n123"'
      (2)   "alo\n123\""
      (3)   '\97lo\10\04923"'
      (4)   [[alo
            123"]]
      (5)   [==[
            alo
            123"]==]
</pre>

<p><em>Numerical constants</em> may be written with an optional decimal part
and an optional decimal exponent.
Lua also accepts integer hexadecimal constants,
by prefixing them with <code>0x</code>.
Examples of valid numerical constants are
<pre>
       3       3.0     3.1416  314.16e-2   0.31416E1  0xff  0x56
</pre>

<p><em>Comments</em> start with a double hyphen (<code>--</code>)
anywhere outside a string.
If the text immediately after <code>--</code> is not an opening long bracket,
the comment is a <em>short comment</em>,
which runs until the end of the line.
Otherwise, it is a <em>long comment</em>,
which runs until the corresponding closing long bracket.
Long comments are frequently used to disable code temporarily.

<p><a name="TypesSec"></a><a name="2.2"></a><h2>2.2 - Values and Types</h2>

<p>Lua is a <em>dynamically typed language</em>.
This means that
variables do not have types; only values do.
There are no type definitions in the language.
All values carry their own type.

<p>All values in Lua are <em>first-class values</em>.
This means that all values can be stored in variables,
passed as arguments to other functions, and returned as results.

<p>There are eight basic types in Lua:
<em>nil</em>, <em>boolean</em>, <em>number</em>,
<em>string</em>, <em>function</em>, <em>userdata</em>,
<em>thread</em>, and <em>table</em>.
<em>Nil</em> is the type of the value <b>nil</b>,
whose main property is to be different from any other value;
it usually represents the absence of a useful value.
<em>Boolean</em> is the type of the values <b>false</b> and <b>true</b>.
Both <b>nil</b> and <b>false</b> make a condition false;
any other value makes it true.
<em>Number</em> represents real (double-precision floating-point) numbers.
(It is easy to build Lua interpreters that use other
internal representations for numbers,
such as single-precision float or long integers.
See file <code>luaconf.h</code>.)
<em>String</em> represents arrays of characters.

Lua is 8-bit clean:
Strings may contain any 8-bit character,
including embedded zeros (`<code>\0</code>&acute;) (see <a href="#lexical">2.1</a>).

<p>Lua can call (and manipulate) functions written in Lua and
functions written in C
(see <a href="#functioncall">2.5.8</a>).

<p>The type <em>userdata</em> is provided to allow arbitrary C data to
be stored in Lua variables.
This type corresponds to a block of raw memory
and has no pre-defined operations in Lua,
except assignment and identity test.
However, by using <em>metatables</em>,
the programmer can define operations for userdata values
(see <a href="#metatable">2.8</a>).
Userdata values cannot be created or modified in Lua,
only through the C API.
This guarantees the integrity of data owned by the host program.

<p>The type <em>thread</em> represents independent threads of execution
and it is used to implement coroutines (see <a href="#coroutine">2.11</a>).
Do not confuse Lua threads with operating-system threads.
Lua supports coroutines on all systems,
even those that do not support threads.

<p>The type <em>table</em> implements associative arrays,
that is, arrays that can be indexed not only with numbers,
but with any value (except <b>nil</b>).
Tables can be <em>heterogeneous</em>;
that is, they can contain values of all types (except <b>nil</b>).
Tables are the sole data structuring mechanism in Lua;
they may be used to represent ordinary arrays,
symbol tables, sets, records, graphs, trees, etc.
To represent records, Lua uses the field name as an index.
The language supports this representation by
providing <code>a.name</code> as syntactic sugar for <code>a["name"]</code>.
There are several convenient ways to create tables in Lua
(see <a href="#tableconstructor">2.5.7</a>).

<p>Like indices,
the value of a table field can be of any type (except <b>nil</b>).
In particular,
because functions are first-class values,
table fields may contain functions.
Thus tables may also carry <em>methods</em> (see <a href="#func-def">2.5.9</a>).

<p>Tables, functions, threads, and (full) userdata values are <em>objects</em>:
variables do not actually <em>contain</em> these values,
only <em>references</em> to them.
Assignment, parameter passing, and function returns
always manipulate references to such values;
these operations do not imply any kind of copy.

<p>The library function <a href="#pdf-type"><code>type</code></a> returns a string describing the type
of a given value.

<p><a name="coercion"></a><a name="2.2.1"></a><h3>2.2.1 - Coercion</h3>

<p>Lua provides automatic conversion between
string and number values at run time.
Any arithmetic operation applied to a string tries to convert
this string to a number, following the usual conversion rules.
Conversely, whenever a number is used where a string is expected,
the number is converted to a string, in a reasonable format.
For complete control over how numbers are converted to strings,
use the <code>format</code> function from the string library
(see <a href="#pdf-string.format"><code>string.format</code></a>).

<p><a name="variables"></a><a name="2.3"></a><h2>2.3 - Variables</h2>

<p>Variables are places that store values.

There are three kinds of variables in Lua:
global variables, local variables, and table fields.

<p>A single name can denote a global variable or a local variable
(or a function's formal parameter,
which is a particular kind of local variable):
<pre>
	var ::= Name
</pre>
Name denotes identifiers, as defined in (see <a href="#lexical">2.1</a>).

<p>Variables are assumed to be global unless explicitly declared local
(see <a href="#localvar">2.4.7</a>).
Local variables are <em>lexically scoped</em>:
Local variables can be freely accessed by functions
defined inside their scope (see <a href="#visibility">2.6</a>).

<p>Before the first assignment to a variable, its value is <b>nil</b>.

<p>Square brackets are used to index a table:
<pre>
	var ::= prefixexp `<b>[</b>&acute; exp `<b>]</b>&acute;
</pre>
The first expression (<em>prefixexp</em>) should result in a table value;
the second expression (<em>exp</em>)
identifies a specific entry in this table.
The expression denoting the table to be indexed has a restricted syntax;
see <a href="#expressions">2.5</a> for details.

<p>The syntax <code>var.Name</code> is just syntactic sugar for
<code>var["Name"]</code> and is used to denote table fields:
<pre>
	var ::= prefixexp `<b>.</b>&acute; Name
</pre>

<p>The meaning of accesses to global variables 
and table fields can be changed via metatables.
An access to an indexed variable <code>t[i]</code> is equivalent to
a call <code>gettable_event(t,i)</code>.
(See <a href="#metatable">2.8</a> for a complete description of the
<code>gettable_event</code> function.
This function is not defined or callable in Lua.
We use it here only for explanatory purposes.)

<p>All global variables live as fields in ordinary Lua tables,
called <em>environment tables</em> or simply
<em>environments</em> (see <a href="#environ">2.9</a>).
Each function has its own reference to an environment,
so that all global variables in this function
will refer to this environment table.
When a function is created,
it inherits the environment from the function that created it.
To get the environment table of a Lua function,
you call <a href="#pdf-getfenv"><code>getfenv</code></a>.
To replace it,
you call <a href="#pdf-setfenv"><code>setfenv</code></a>.
(You can only manipulate the environment of C functions
through the debug library; (see <a href="#libdebug">5.9</a>).)

<p>An access to a global variable <code>x</code>
is equivalent to <code>_env.x</code>,
which in turn is equivalent to
<pre>
       gettable_event(_env, "x")
</pre>
where <code>_env</code> is the environment of the running function.
(See <a href="#metatable">2.8</a> for a complete description of the
<code>gettable_event</code> function.
This function is not defined or callable in Lua.
Similarly, the <code>_env</code> variable is not defined in Lua.
We use them here only for explanatory purposes.)

<p><a name="stats"></a><a name="2.4"></a><h2>2.4 - Statements</h2>

<p>Lua supports an almost conventional set of statements,
similar to those in Pascal or C.
This set includes
assignment, control structures, function calls,
table constructors, and variable declarations.

<p><a name="chunks"></a><a name="2.4.1"></a><h3>2.4.1 - Chunks</h3>

<p>The unit of execution of Lua is called a <em>chunk</em>.
A chunk is simply a sequence of statements,
which are executed sequentially.
Each statement can be optionally followed by a semicolon:
<pre>
	chunk ::= {stat [`<b>;</b>&acute;]}
</pre>
There are no empty statements and thus `<code>;;</code>&acute; is not legal.

<p>Lua handles a chunk as the body of an anonymous function 
with a variable number of arguments
(see <a href="#func-def">2.5.9</a>).
As such, chunks can define local variables,
receive arguments, and return values.

<p>A chunk may be stored in a file or in a string inside the host program.
When a chunk is executed, first it is pre-compiled into instructions for
a virtual machine,
and then the compiled code is executed
by an interpreter for the virtual machine.

<p>Chunks may also be pre-compiled into binary form;
see program <code>luac</code> for details.
Programs in source and compiled forms are interchangeable;
Lua automatically detects the file type and acts accordingly.


<p><a name="2.4.2"></a><h3>2.4.2 - Blocks</h3>
A block is a list of statements;
syntactically, a block is the same as a chunk:
<pre>
	block ::= chunk
</pre>

<p>A block may be explicitly delimited to produce a single statement:
<pre>
	stat ::= <b>do</b> block <b>end</b>
</pre>
Explicit blocks are useful
to control the scope of variable declarations.
Explicit blocks are also sometimes used to
add a <b>return</b> or <b>break</b> statement in the middle
of another block (see <a href="#control">2.4.4</a>).


<p><a name="assignment"></a><a name="2.4.3"></a><h3>2.4.3 - Assignment</h3>

<p>Lua allows multiple assignment.
Therefore, the syntax for assignment
defines a list of variables on the left side
and a list of expressions on the right side.
The elements in both lists are separated by commas:
<pre>
	stat ::= varlist1 `<b>=</b>&acute; explist1
	varlist1 ::= var {`<b>,</b>&acute; var}
	explist1 ::= exp {`<b>,</b>&acute; exp}
</pre>
Expressions are discussed in <a href="#expressions">2.5</a>.

<p>Before the assignment,
the list of values is <em>adjusted</em> to the length of
the list of variables.
If there are more values than needed,
the excess values are thrown away.
If there are fewer values than needed,
the list is extended with as many  <b>nil</b>'s as needed.
If the list of expressions ends with a function call,
then all values returned by this call enter in the list of values,
before the adjustment
(except when the call is enclosed in parentheses; see <a href="#expressions">2.5</a>).

<p>The assignment statement first evaluates all its expressions
and only then are the assignments performed.
Thus the code
<pre>
       i = 3
       i, a[i] = i+1, 20
</pre>
sets <code>a[3]</code> to 20, without affecting <code>a[4]</code>
because the <code>i</code> in <code>a[i]</code> is evaluated (to 3)
before it is assigned 4.
Similarly, the line
<pre>
       x, y = y, x
</pre>
exchanges the values of <code>x</code> and <code>y</code>.

<p>The meaning of assignments to global variables
and table fields can be changed via metatables.
An assignment to an indexed variable <code>t[i] = val</code> is equivalent to
<code>settable_event(t,i,val)</code>.
(See <a href="#metatable">2.8</a> for a complete description of the
<code>settable_event</code> function.
This function is not defined or callable in Lua.
We use it here only for explanatory purposes.)

<p>An assignment to a global variable <code>x = val</code>
is equivalent to the assignment
<code>_env.x = val</code>,
which in turn is equivalent to
<pre>
       settable_event(_env, "x", val)
</pre>
where <code>_env</code> is the environment of the running function.
(The <code>_env</code> variable is not defined in Lua.
We use it here only for explanatory purposes.)

<p><a name="control"></a><a name="2.4.4"></a><h3>2.4.4 - Control Structures</h3>
The control structures
<b>if</b>, <b>while</b>, and <b>repeat</b> have the usual meaning and
familiar syntax:



<pre>
	stat ::= <b>while</b> exp <b>do</b> block <b>end</b>
	stat ::= <b>repeat</b> block <b>until</b> exp
	stat ::= <b>if</b> exp <b>then</b> block {<b>elseif</b> exp <b>then</b> block} [<b>else</b> block] <b>end</b>
</pre>
Lua also has a <b>for</b> statement, in two flavors (see <a href="#for">2.4.5</a>).

<p>The condition expression of a
control structure may return any value.
Both <b>false</b> and <b>nil</b> are considered false.
All values different from <b>nil</b> and <b>false</b> are considered true
(in particular, the number 0 and the empty string are also true).

<p>In the <b>repeat</b>--<b>until</b> loop,
the inner block does not end at the <b>until</b> keyword,
but only after the condition.
So, the condition can refer to local variables
declared inside the loop block.

<p>The <b>return</b> statement is used to return values
from a function or a chunk (which is just a function).

Functions and chunks may return more than one value,
so the syntax for the <b>return</b> statement is
<pre>
	stat ::= <b>return</b> [explist1]
</pre>

<p>The <b>break</b> statement is used to terminate the execution of a
<b>while</b>, <b>repeat</b>, or <b>for</b> loop,
skipping to the next statement after the loop:

<pre>
	stat ::= <b>break</b>
</pre>
A <b>break</b> ends the innermost enclosing loop.

<p>The <b>return</b> and <b>break</b>
statements can only be written as the <em>last</em> statement of a block.
If it is really necessary to <b>return</b> or <b>break</b> in the
middle of a block,
then an explicit inner block can be used,
as in the idioms
`<code>do return end</code>&acute; and
`<code>do break end</code>&acute;,
because now <b>return</b> and <b>break</b> are the last statements in
their (inner) blocks.

<p><a name="for"></a><a name="2.4.5"></a><h3>2.4.5 - For Statement</h3>

<p>The <b>for</b> statement has two forms:
one numeric and one generic.


<p>The numeric <b>for</b> loop repeats a block of code while a
control variable runs through an arithmetic progression.
It has the following syntax:
<pre>
	stat ::= <b>for</b> Name `<b>=</b>&acute; exp `<b>,</b>&acute; exp [`<b>,</b>&acute; exp] <b>do</b> block <b>end</b>
</pre>
The <em>block</em> is repeated for <em>name</em> starting at the value of
the first <em>exp</em>, until it passes the second <em>exp</em> by steps of the
third <em>exp</em>.
More precisely, a <b>for</b> statement like
<pre>
       for var = e1, e2, e3 do block end
</pre>
is equivalent to the code:
<pre>
       do
         local _var, _limit, _step = tonumber(e1), tonumber(e2), tonumber(e3)
         if not (_var and _limit and _step) then error() end
         while (_step>0 and _var&#060;=_limit) or (_step&#060;=0 and _var>=_limit) do
           local var = _var
           block
           _var = _var + _step
         end
       end
</pre>
Note the following:
<ul>
<li> All three control expressions are evaluated only once,
before the loop starts.
They must all result in numbers.
<li> <code>_var</code>, <code>_limit</code>, and <code>_step</code> are invisible variables.
The names are here for explanatory purposes only.
<li> If the third expression (the step) is absent,
then a step of 1 is used.
<li> You can use <b>break</b> to exit a <b>for</b> loop.
<li> The loop variable <code>var</code> is local to the loop;
you cannot use its value after the <b>for</b> ends or is broken.
If you need the value of the loop variable <code>var</code>,
then assign it to another variable before breaking or exiting the loop.
</ul>

<p>The generic <b>for</b> statement works over functions,
called <em>iterators</em>.
On each iteration, the iterator function is called to produce a new value,
stopping when this new value is <b>nil</b>.
The generic <b>for</b> loop has the following syntax:
<pre>
	stat ::= <b>for</b> namelist <b>in</b> explist1 <b>do</b> block <b>end</b>
	namelist ::= Name {`<b>,</b>&acute; Name}
</pre>
A <b>for</b> statement like
<pre>
       for var_1, ..., var_n in explist do block end
</pre>
is equivalent to the code:
<pre>
       do
         local _f, _s, _var = explist
         while true do
           local var_1, ... , var_n = _f(_s, _var)
           _var = var_1
           if _var == nil then break end
           block
         end
       end
</pre>
Note the following:
<ul>
<li> <code>explist</code> is evaluated only once.
Its results are an <em>iterator</em> function,
a <em>state</em>, and an initial value for the first <em>iterator variable</em>.
<li> <code>_f</code>, <code>_s</code>, and <code>_var</code> are invisible variables.
The names are here for explanatory purposes only.
<li> You can use <b>break</b> to exit a <b>for</b> loop.
<li> The loop variables <code>var_i</code> are local to the loop;
you cannot use their values after the <b>for</b> ends.
If you need these values,
then assign them to other variables before breaking or exiting the loop.
</ul>

<p><a name="funcstat"></a><a name="2.4.6"></a><h3>2.4.6 - Function Calls as Statements</h3>
To allow possible side-effects,
function calls can be executed as statements:
<pre>
	stat ::= functioncall
</pre>
In this case, all returned values are thrown away.
Function calls are explained in <a href="#functioncall">2.5.8</a>.

<p><a name="localvar"></a><a name="2.4.7"></a><h3>2.4.7 - Local Declarations</h3>
Local variables may be declared anywhere inside a block.
The declaration may include an initial assignment:
<pre>
	stat ::= <b>local</b> namelist [`<b>=</b>&acute; explist1]
</pre>
If present, an initial assignment has the same semantics
of a multiple assignment (see <a href="#assignment">2.4.3</a>).
Otherwise, all variables are initialized with <b>nil</b>.

<p>A chunk is also a block (see <a href="#chunks">2.4.1</a>),
and so local variables can be declared in a chunk outside any explicit block.
The scope of such local variables extends until the end of the chunk.

<p>The visibility rules for local variables are explained in <a href="#visibility">2.6</a>.

<p><a name="expressions"></a><a name="2.5"></a><h2>2.5 - Expressions</h2>

<p>
The basic expressions in Lua are the following:
<pre>
	exp ::= prefixexp
	exp ::= <b>nil</b>  |  <b>false</b>  |  <b>true</b>
	exp ::= Number
	exp ::= String
	exp ::= function
	exp ::= tableconstructor
	exp ::= `<b>...</b>&acute;
	exp ::= exp binop exp
	exp ::= unop exp
	prefixexp ::= var  |  functioncall  |  `<b>(</b>&acute; exp `<b>)</b>&acute;
</pre>

<p>Numbers and literal strings are explained in <a href="#lexical">2.1</a>;
variables are explained in <a href="#variables">2.3</a>;
function definitions are explained in <a href="#func-def">2.5.9</a>;
function calls are explained in <a href="#functioncall">2.5.8</a>;
table constructors are explained in <a href="#tableconstructor">2.5.7</a>.
Vararg expressions,
denoted by three dots (`<code>...</code>&acute;), can only be used inside
vararg functions;
they are explained in <a href="#func-def">2.5.9</a>.


<p>Binary operators comprise arithmetic operators (see <a href="#arith">2.5.1</a>),
relational operators (see <a href="#rel-ops">2.5.2</a>), and logical operators (see <a href="#logic">2.5.3</a>).
Unary operators comprise the unary minus (see <a href="#arith">2.5.1</a>),
the unary <b>not</b> (see <a href="#logic">2.5.3</a>),
and the unary <em>length operator</em> (see <a href="#len-op">2.5.5</a>).

<p>Both function calls and vararg expressions may result in multiple values.
If the expression is used as a statement (see <a href="#funcstat">2.4.6</a>)
(only possible for function calls),
then its return list is adjusted to zero elements,
thus discarding all returned values.
If the expression is used inside another expression
or in the middle of a list of expressions,
then its result list is adjusted to one element,
thus discarding all values except the first one.
If the expression is used as the last element of a list of expressions,
then no adjustment is made,
unless the call is enclosed in parentheses.

<p>Here are some examples:
<pre>
       f()                -- adjusted to 0 results
       g(f(), x)          -- f() is adjusted to 1 result
       g(x, f())          -- g gets x plus all values returned by f()
       a,b,c = f(), x     -- f() is adjusted to 1 result (c gets nil)
       a,b = ...          -- a gets the first vararg parameter, b gets
                          -- the second (both a and b may get nil if there is
                          -- no corresponding vararg parameter)
       a,b,c = x, f()     -- f() is adjusted to 2 results
       a,b,c = f()        -- f() is adjusted to 3 results
       return f()         -- returns all values returned by f()
       return ...         -- returns all received vararg parameters
       return x,y,f()     -- returns x, y, and all values returned by f()
       {f()}              -- creates a list with all values returned by f()
       {...}              -- creates a list with all vararg parameters
       {f(), nil}         -- f() is adjusted to 1 result
</pre>

<p>An expression enclosed in parentheses always results in only one value.
Thus,
<code>(f(x,y,z))</code> is always a single value,
even if <code>f</code> returns several values.
(The value of <code>(f(x,y,z))</code> is the first value returned by <code>f</code>
or <b>nil</b> if <code>f</code> does not return any values.)

<p><a name="arith"></a><a name="2.5.1"></a><h3>2.5.1 - Arithmetic Operators</h3>
Lua supports the usual arithmetic operators:
the binary <code>+</code> (addition),
<code>-</code> (subtraction), <code>*</code> (multiplication),
<code>/</code> (division), <code>%</code> (modulo), and <code>^</code> (exponentiation);
and unary <code>-</code> (negation).
If the operands are numbers, or strings that can be converted to
numbers (see <a href="#coercion">2.2.1</a>),
then all operations have the usual meaning.
Exponentiation works for any exponent.
For instance, <code>x^(-0.5)</code> computes the inverse of the square root of <code>x</code>.
Modulus is defined as
<pre>
       a % b == a - math.floor(a/b)*b
</pre>
That is, it is the remainder of a division that rounds
the quotient towards minus infinity.

<p><a name="rel-ops"></a><a name="2.5.2"></a><h3>2.5.2 - Relational Operators</h3>
The relational operators in Lua are
<pre>
       ==    ~=    &#060;     >     &#060;=    >=
</pre>
These operators always result in <b>false</b> or <b>true</b>.

<p>Equality (<code>==</code>) first compares the type of its operands.
If the types are different, then the result is <b>false</b>.
Otherwise, the values of the operands are compared.
Numbers and strings are compared in the usual way.
Objects (tables, userdata, threads, and functions)
are compared by <em>reference</em>:
Two objects are considered equal only if they are the <em>same</em> object.
Every time you create a new object
(a table, userdata, thread, or function),
this new object is different from any previously existing object.

<p>You can change the way that Lua compares tables and userdata 
by using the "eq" metamethod (see <a href="#metatable">2.8</a>).

<p>The conversion rules of <a href="#coercion">2.2.1</a>
<em>do not</em> apply to equality comparisons.
Thus, <code>"0"==0</code> evaluates to <b>false</b>,
and <code>t[0]</code> and <code>t["0"]</code> denote different
entries in a table.


<p>The operator <code>~=</code> is exactly the negation of equality (<code>==</code>).

<p>The order operators work as follows.
If both arguments are numbers, then they are compared as such.
Otherwise, if both arguments are strings,
then their values are compared according to the current locale.
Otherwise, Lua tries to call the "lt" or the "le"
metamethod (see <a href="#metatable">2.8</a>).

<p><a name="logic"></a><a name="2.5.3"></a><h3>2.5.3 - Logical Operators</h3>
The logical operators in Lua are

<pre>
       and   or    not
</pre>
Like the control structures (see <a href="#control">2.4.4</a>),
all logical operators consider both <b>false</b> and <b>nil</b> as false
and anything else as true.


<p>The negation operator <b>not</b> always returns <b>false</b> or <b>true</b>.
The conjunction operator <b>and</b> returns its first argument
if this value is <b>false</b> or <b>nil</b>;
otherwise, <b>and</b> returns its second argument.
The disjunction operator <b>or</b> returns its first argument
if this value is different from <b>nil</b> and <b>false</b>;
otherwise, <b>or</b> returns its second argument.
Both <b>and</b> and <b>or</b> use short-cut evaluation;
that is,
the second operand is evaluated only if necessary.
Here are some examples:
<pre>
       10 or 20            --> 10
       10 or error()       --> 10
       nil or "a"          --> "a"
       nil and 10          --> nil
       false and error()   --> false
       false and nil       --> false
       false or nil        --> nil
       10 and 20           --> 20
</pre>
(In this manual,
`<code>--></code>&acute; indicates the result of the preceding expression.)

<p><a name="concat"></a><a name="2.5.4"></a><h3>2.5.4 - Concatenation</h3>
The string concatenation operator in Lua is
denoted by two dots (`<code>..</code>&acute;).
If both operands are strings or numbers, then they are converted to
strings according to the rules mentioned in <a href="#coercion">2.2.1</a>.
Otherwise, the "concat" metamethod is called (see <a href="#metatable">2.8</a>).

<p><a name="len-op"></a><a name="2.5.5"></a><h3>2.5.5 - The Length Operator</h3>

<p>The length operator is denoted by the unary operator <code>#</code>.
The length of a string is its number of bytes
(that is, the usual meaning of string length when each
character is one byte).

<p>The length of a table <code>t</code> is defined to be any
integer index <code>n</code>
such that <code>t[n]</code> is not <b>nil</b> and <code>t[n+1]</code> is <b>nil</b>;
moreover, if <code>t[1]</code> is <b>nil</b>, <code>n</code> may be zero.
For a regular array, with non-nil values from 1 to a given <code>n</code>,
its length is exactly that <code>n</code>,
the index of its last value.
If the array has "holes"
(that is, <b>nil</b> values between other non-nil values),
then <code>#t</code> may be any of the indices that
directly precedes a <b>nil</b> value
(that is, it may consider any such <b>nil</b> value as the end of
the array). 

<p><a name="2.5.6"></a><h3>2.5.6 - Precedence</h3>
Operator precedence in Lua follows the table below,
from lower to higher priority:
<pre>
       or
       and
       &#060;     >     &#060;=    >=    ~=    ==
       ..
       +     -
       *     /     %
       not   #     - (unary)
       ^
</pre>
As usual,
you can use parentheses to change the precedences of an expression.
The concatenation (`<code>..</code>&acute;) and exponentiation (`<code>^</code>&acute;)
operators are right associative.
All other binary operators are left associative.

<p><a name="tableconstructor"></a><a name="2.5.7"></a><h3>2.5.7 - Table Constructors</h3>
Table constructors are expressions that create tables.
Every time a constructor is evaluated, a new table is created.
Constructors can be used to create empty tables,
or to create a table and initialize some of its fields.
The general syntax for constructors is
<pre>
	tableconstructor ::= `<b>{</b>&acute; [fieldlist] `<b>}</b>&acute;
	fieldlist ::= field {fieldsep field} [fieldsep]
	field ::= `<b>[</b>&acute; exp `<b>]</b>&acute; `<b>=</b>&acute; exp  |  Name `<b>=</b>&acute; exp  |  exp
	fieldsep ::= `<b>,</b>&acute;  |  `<b>;</b>&acute;
</pre>

<p>Each field of the form <code>[exp1] = exp2</code> adds to the new table an entry
with key <code>exp1</code> and value <code>exp2</code>.
A field of the form <code>name = exp</code> is equivalent to
<code>["name"] = exp</code>.
Finally, fields of the form <code>exp</code> are equivalent to
<code>[i] = exp</code>, where <code>i</code> are consecutive numerical integers,
starting with 1.
Fields in the other formats do not affect this counting.
For example,
<pre>
       a = { [f(1)] = g; "x", "y"; x = 1, f(x), [30] = 23; 45 }
</pre>
is equivalent to
<pre>
       do
         local t = {}
         t[f(1)] = g
         t[1] = "x"         -- 1st exp
         t[2] = "y"         -- 2nd exp
         t.x = 1            -- t["x"] = 1
         t[3] = f(x)        -- 3rd exp
         t[30] = 23
         t[4] = 45          -- 4th exp
         a = t
       end
</pre>

<p>If the last field in the list has the form <code>exp</code>
and the expression is a function call or a vararg expression,
then all values returned by this expression enter the list consecutively
(see <a href="#functioncall">2.5.8</a>).
To avoid this,
enclose the function call (or the vararg expression)
in parentheses (see <a href="#expressions">2.5</a>).

<p>The field list may have an optional trailing separator,
as a convenience for machine-generated code.

<p><a name="functioncall"></a><a name="2.5.8"></a><h3>2.5.8 - Function Calls</h3>
A function call in Lua has the following syntax:
<pre>
	functioncall ::= prefixexp args
</pre>
In a function call,
first <em>prefixexp</em> and <em>args</em> are evaluated.
If the value of <em>prefixexp</em> has type <em>function</em>,
then this function is called
with the given arguments.
Otherwise, the <em>prefixexp</em> "call" metamethod is called,
having as first parameter the value of <em>prefixexp</em>,
followed by the original call arguments
(see <a href="#metatable">2.8</a>).

<p>The form
<pre>
	functioncall ::= prefixexp `<b>:</b>&acute; Name args
</pre>
can be used to call "methods".
A call <code>v:name(...)</code>
is syntactic sugar for <code>v.name(v,...)</code>,
except that <code>v</code> is evaluated only once.

<p>Arguments have the following syntax:
<pre>
	args ::= `<b>(</b>&acute; [explist1] `<b>)</b>&acute;
	args ::= tableconstructor
	args ::= String
</pre>
All argument expressions are evaluated before the call.
A call of the form <code>f{...}</code> is syntactic sugar for <code>f({...})</code>;
that is, the argument list is a single new table.
A call of the form <code>f'...'</code>
(or <code>f"..."</code> or <code>f[[...]]</code>) is syntactic sugar for <code>f('...')</code>;
that is, the argument list is a single literal string.

<p>As an exception to the free-format syntax of Lua,
you cannot put a line break before the `<code>(</code>&acute; in a function call.
This restriction avoids some ambiguities in the language.
If you write
<pre>
       a = f
       (g).x(a)
</pre>
Lua would see that as a single statement, <code>a = f(g).x(a)</code>.
So, if you want two statements, you must add a semi-colon between them.
If you actually want to call <code>f</code>,
you must remove the line break before <code>(g)</code>.

<p>A call of the form <code>return</code> <em>functioncall</em> is called
a <em>tail call</em>.
Lua implements <em>proper tail calls</em>
(or <em>proper tail recursion</em>):
In a tail call,
the called function reuses the stack entry of the calling function.
Therefore, there is no limit on the number of nested tail calls that
a program can execute.
However, a tail call erases any debug information about the
calling function.
Note that a tail call only happens with a particular syntax,
where the <b>return</b> has one single function call as argument;
this syntax makes the calling function return exactly
the returns of the called function.
So, none of the following examples are tail calls:
<pre>
       return (f(x))        -- results adjusted to 1
       return 2 * f(x)
       return x, f(x)       -- additional results
       f(x); return         -- results discarded
       return x or f(x)     -- results adjusted to 1
</pre>

<p><a name="func-def"></a><a name="2.5.9"></a><h3>2.5.9 - Function Definitions</h3>

<p>The syntax for function definition is
<pre>
	function ::= <b>function</b> funcbody
	funcbody ::= `<b>(</b>&acute; [parlist1] `<b>)</b>&acute; block <b>end</b>
</pre>

<p>The following syntactic sugar simplifies function definitions:
<pre>
	stat ::= <b>function</b> funcname funcbody
	stat ::= <b>local</b> <b>function</b> Name funcbody
	funcname ::= Name {`<b>.</b>&acute; Name} [`<b>:</b>&acute; Name]
</pre>
The statement
<pre>
       function f () ... end
</pre>
translates to
<pre>
       f = function () ... end
</pre>
The statement
<pre>
       function t.a.b.c.f () ... end
</pre>
translates to
<pre>
       t.a.b.c.f = function () ... end
</pre>
The statement
<pre>
       local function f () ... end
</pre>
translates to
<pre>
       local f; f = function () ... end
</pre>
<em>not</em> this:
<pre>
       local f = function () ... end
</pre>
(This only makes a difference when the body of the function
contains references to <code>f</code>.)

<p>A function definition is an executable expression,
whose value has type <em>function</em>.
When Lua pre-compiles a chunk,
all its function bodies are pre-compiled too.
Then, whenever Lua executes the function definition,
the function is <em>instantiated</em> (or <em>closed</em>).
This function instance (or <em>closure</em>)
is the final value of the expression.
Different instances of the same function
may refer to different  external local variables
and may have different environment tables.

<p>Parameters act as local variables that are
initialized with the argument values:
<pre>
	parlist1 ::= namelist [`<b>,</b>&acute; `<b>...</b>&acute;]  |  `<b>...</b>&acute;
</pre>
When a function is called,
the list of arguments is adjusted to
the length of the list of parameters,
unless the function is a variadic or <em>vararg function</em>,
which is
indicated by three dots (`<code>...</code>&acute;) at the end of its parameter list.
A vararg function does not adjust its argument list;
instead, it collects all extra arguments and supplies them
to the function through a <em>vararg expression</em>,
which is also written as three dots.
The value of this expression is a list of all actual extra arguments,
similar to a function with multiple results.
If a vararg expression is used inside another expression
or in the middle of a list of expressions,
then its return list is adjusted to one element.
If the expression is used as the last element of a list of expressions,
then no adjustment is made
(unless the call is enclosed in parentheses).

<p>As an example, consider the following definitions:
<pre>
       function f(a, b) end
       function g(a, b, ...) end
       function r() return 1,2,3 end
</pre>
Then, we have the following mapping from arguments to parameters and
to the vararg expression:
<pre>
       CALL            PARAMETERS

       f(3)             a=3, b=nil
       f(3, 4)          a=3, b=4
       f(3, 4, 5)       a=3, b=4
       f(r(), 10)       a=1, b=10
       f(r())           a=1, b=2

       g(3)             a=3, b=nil, ... -->  (nothing)
       g(3, 4)          a=3, b=4,   ... -->  (nothing)
       g(3, 4, 5, 8)    a=3, b=4,   ... -->  5  8
       g(5, r())        a=5, b=1,   ... -->  2  3
</pre>

<p>Results are returned using the <b>return</b> statement (see <a href="#control">2.4.4</a>).
If control reaches the end of a function
without encountering a <b>return</b> statement,
then the function returns with no results.

<p>The <em>colon</em> syntax
is used for defining <em>methods</em>,
that is, functions that have an implicit extra parameter <code>self</code>.
Thus, the statement
<pre>
       function t.a.b.c:f (...) ... end
</pre>
is syntactic sugar for
<pre>
       t.a.b.c.f = function (self, ...) ... end
</pre>

<p><a name="visibility"></a><a name="2.6"></a><h2>2.6 - Visibility Rules</h2>


<p>Lua is a lexically scoped language.
The scope of variables begins at the first statement <em>after</em>
their declaration and lasts until the end of the innermost block that
includes the declaration.
Consider the following example:
<pre>
       x = 10                -- global variable
       do                    -- new block
         local x = x         -- new `x', with value 10
         print(x)            --> 10
         x = x+1
         do                  -- another block
           local x = x+1     -- another `x'
           print(x)          --> 12
         end
         print(x)            --> 11
       end
       print(x)              --> 10  (the global one)
</pre>

<p>Notice that, in a declaration like <code>local x = x</code>,
the new <code>x</code> being declared is not in scope yet,
and so the second <code>x</code> refers to the outside variable.

<p>Because of the lexical scoping rules,
local variables can be freely accessed by functions
defined inside their scope.
A local variable used by an inner function is called
an <em>upvalue</em>, or <em>external local variable</em>,
inside the inner function.

<p>Notice that each execution of a <b>local</b> statement
defines new local variables.
Consider the following example:
<pre>
       a = {}
       local x = 20
       for i=1,10 do
         local y = 0
         a[i] = function () y=y+1; return x+y end
       end
</pre>
The loop creates ten closures
(that is, ten instances of the anonymous function).
Each of these closures uses a different <code>y</code> variable,
while all of them share the same <code>x</code>.

<p><a name="error"></a><a name="2.7"></a><h2>2.7 - Error Handling</h2>

<p>Because Lua is an embedded extension language,
all Lua actions start from C code in the host program
calling a function from the Lua library (see <a href="#lua_pcall"><code>lua_pcall</code></a>).
Whenever an error occurs during Lua compilation or execution,
control returns to C,
which can take appropriate measures
(such as printing an error message).

<p>Lua code can explicitly generate an error by calling the
<a href="#pdf-error"><code>error</code></a> function.
If you need to catch errors in Lua,
you can use the <a href="#pdf-pcall"><code>pcall</code></a> function.

<p><a name="metatable"></a><a name="2.8"></a><h2>2.8 - Metatables</h2>

<p>Every value in Lua may have a <em>metatable</em>.
This <em>metatable</em> is an ordinary Lua table
that defines the behavior of the original value
under certain special operations.
You can change several aspects of the behavior
of operations over a value by setting specific fields in its metatable.
For instance, when a non-numeric value is the operand of an addition,
Lua checks for a function in the field <code>"__add"</code> in its metatable.
If it finds one,
Lua calls this function to perform the addition.

<p>We call the keys in a metatable <em>events</em>
and the values <em>metamethods</em>.
In the previous example, the event is <code>"add"</code> 
and the metamethod is the function that performs the addition.

<p>You can query the metatable of any value
through the <a href="#pdf-getmetatable"><code>getmetatable</code></a> function.

<p>You can replace the metatable of tables
through the <a href="#pdf-setmetatable"><code>setmetatable</code></a>
function.
You cannot change the metatable of other types from Lua
(except using the debug library);
you must use the C API for that.

<p>Tables and userdata have individual metatables
(although multiple tables and userdata can share
a same table as their metatable);
values of all other types share one single metatable per type.
So, there is one single metatable for all numbers,
and for all strings, etc.

<p>A metatable may control how an object behaves in arithmetic operations,
order comparisons, concatenation, length operation, and indexing.
A metatable can also define a function to be called when a userdata
is garbage collected.
For each of these operations Lua associates a specific key
called an <em>event</em>.
When Lua performs one of these operations over a value,
it checks whether this value has a metatable with the corresponding event.
If so, the value associated with that key (the <em>metamethod</em>)
controls how Lua will perform the operation.

<p>Metatables control the operations listed next.
Each operation is identified by its corresponding name.
The key for each operation is a string with its name prefixed by
two underscores, `<code>__</code>&acute;;
for instance, the key for operation "add" is the
string <code>"__add"</code>.
The semantics of these operations is better explained by a Lua function
describing how the interpreter executes the operation.

<p>The code shown here in Lua is only illustrative;
the real behavior is hard coded in the interpreter
and it is much more efficient than this simulation.
All functions used in these descriptions
(<a href="#pdf-rawget"><code>rawget</code></a>, <a href="#pdf-tonumber"><code>tonumber</code></a>, etc.)
are described in <a href="#predefined">5.1</a>.
In particular, to retrieve the metamethod of a given object,
we use the expression
<pre>
       metatable(obj)[event]
</pre>
This should be read as
<pre>
       rawget(getmetatable(obj) or {}, event)
</pre>
That is, the access to a metamethod does not invoke other metamethods,
and the access to objects with no metatables does not fail
(it simply results in <b>nil</b>).

<p><ul>
<li><b>"add":</b>
the <code>+</code> operation.

<p>The function <code>getbinhandler</code> below defines how Lua chooses a handler
for a binary operation.
First, Lua tries the first operand.
If its type does not define a handler for the operation,
then Lua tries the second operand.
<pre>
 function getbinhandler (op1, op2, event)
   return metatable(op1)[event] or metatable(op2)[event]
 end
</pre>
Using this function,
the behavior of the <code>op1 + op2</code> is
<pre>
 function add_event (op1, op2)
   local o1, o2 = tonumber(op1), tonumber(op2)
   if o1 and o2 then  -- both operands are numeric?
     return o1 + o2   -- `+' here is the primitive `add'
   else  -- at least one of the operands is not numeric
     local h = getbinhandler(op1, op2, "__add")
     if h then
       -- call the handler with both operands
       return h(op1, op2)
     else  -- no handler available: default behavior
       error("...")
     end
   end
 end
</pre>

<p><li><b>"sub":</b>
the <code>-</code> operation.
Behavior similar to the "add" operation.

<p><li><b>"mul":</b>
the <code>*</code> operation.
Behavior similar to the "add" operation.

<p><li><b>"div":</b>
the <code>/</code> operation.
Behavior similar to the "add" operation.

<p><li><b>"mod":</b>
the <code>%</code> operation.
Behavior similar to the "add" operation,
with the operation
<code>o1 - floor(o1/o2)*o2</code> as the primitive operation.

<p><li><b>"pow":</b>
the <code>^</code> (exponentiation) operation.
Behavior similar to the "add" operation,
with the function <code>pow</code> (from the C math library)
as the primitive operation.

<p><li><b>"unm":</b>
the unary <code>-</code> operation.
<pre>
 function unm_event (op)
   local o = tonumber(op)
   if o then  -- operand is numeric?
     return -o  -- `-' here is the primitive `unm'
   else  -- the operand is not numeric.
     -- Try to get a handler from the operand
     local h = metatable(op).__unm
     if h then
       -- call the handler with the operand
       return h(op)
     else  -- no handler available: default behavior
       error("...")
     end
   end
 end
</pre>

<p><li><b>"concat":</b>
the <code>..</code> (concatenation) operation.
<pre>
 function concat_event (op1, op2)
   if (type(op1) == "string" or type(op1) == "number") and
      (type(op2) == "string" or type(op2) == "number") then
     return op1 .. op2  -- primitive string concatenation
   else
     local h = getbinhandler(op1, op2, "__concat")
     if h then
       return h(op1, op2)
     else
       error("...")
     end
   end
 end
</pre>

<p><li><b>"len":</b>
the <code>#</code> operation.
<pre>
 function len_event (op)
   if type(op) == "string" then
     return strlen(op)         -- primitive string length
   elseif type(op) == "table" then
     return #op                -- primitive table length
   else
     local h = metatable(op).__len
     if h then
       -- call the handler with the operand
       return h(op)
     else  -- no handler available: default behavior
       error("...")
     end
   end
 end
</pre>
See <a href="#len-op">2.5.5</a> for a description of the length of a table.

<p><li><b>"eq":</b>
the <code>==</code> operation.
The function <code>getcomphandler</code> defines how Lua chooses a metamethod
for comparison operators.
A metamethod only is selected when both objects
being compared have the same type
and the same metamethod for the selected operation.
<pre>
 function getcomphandler (op1, op2, event)
   if type(op1) ~= type(op2) then return nil end
   local mm1 = metatable(op1)[event]
   local mm2 = metatable(op2)[event]
   if mm1 == mm2 then return mm1 else return nil end
 end
</pre>
The "eq" event is defined as follows:
<pre>
 function eq_event (op1, op2)
   if type(op1) ~= type(op2) then  -- different types?
     return false   -- different objects
   end
   if op1 == op2 then   -- primitive equal?
     return true   -- objects are equal
   end
   -- try metamethod
   local h = getcomphandler(op1, op2, "__eq")
   if h then
     return h(op1, op2)
   else
     return false
   end
 end
</pre>
<code>a ~= b</code> is equivalent to <code>not (a == b)</code>.

<p><li><b>"lt":</b>
the <code>&#060;</code> operation.
<pre>
 function lt_event (op1, op2)
   if type(op1) == "number" and type(op2) == "number" then
     return op1 &#060; op2   -- numeric comparison
   elseif type(op1) == "string" and type(op2) == "string" then
     return op1 &#060; op2   -- lexicographic comparison
   else
     local h = getcomphandler(op1, op2, "__lt")
     if h then
       return h(op1, op2)
     else
       error("...");
     end
   end
 end
</pre>
<code>a > b</code> is equivalent to <code>b &#060; a</code>.

<p><li><b>"le":</b>
the <code>&#060;=</code> operation.
<pre>
 function le_event (op1, op2)
   if type(op1) == "number" and type(op2) == "number" then
     return op1 &#060;= op2   -- numeric comparison
   elseif type(op1) == "string" and type(op2) == "string" then
     return op1 &#060;= op2   -- lexicographic comparison
   else
     local h = getcomphandler(op1, op2, "__le")
     if h then
       return h(op1, op2)
     else
       h = getcomphandler(op1, op2, "__lt")
       if h then
         return not h(op2, op1)
       else
         error("...");
       end
     end
   end
 end
</pre>
<code>a >= b</code> is equivalent to <code>b &#060;= a</code>.
Note that, in the absence of a "le" metamethod,
Lua tries the "lt", assuming that <code>a &#060;= b</code> is
equivalent to <code>not (b &#060; a)</code>.

<p><li><b>"index":</b>
The indexing access <code>table[key]</code>.
<pre>
 function gettable_event (table, key)
   local h
   if type(table) == "table" then
     local v = rawget(table, key)
     if v ~= nil then return v end
     h = metatable(table).__index
     if h == nil then return nil end
   else
     h = metatable(table).__index
     if h == nil then
       error("...");
     end
   end
   if type(h) == "function" then
     return h(table, key)      -- call the handler
   else return h[key]          -- or repeat operation on it
   end
 end
</pre>

<p><li><b>"newindex":</b>
The indexing assignment <code>table[key] = value</code>.
<pre>
 function settable_event (table, key, value)
   local h
   if type(table) == "table" then
     local v = rawget(table, key)
     if v ~= nil then rawset(table, key, value); return end
     h = metatable(table).__newindex
     if h == nil then rawset(table, key, value); return end
   else
     h = metatable(table).__newindex
     if h == nil then
       error("...");
     end
   end
   if type(h) == "function" then
     return h(table, key,value)    -- call the handler
   else h[key] = value             -- or repeat operation on it
   end
 end
</pre>

<p><li><b>"call":</b>
called when Lua calls a value.
<pre>
 function function_event (func, ...)
   if type(func) == "function" then
     return func(...)   -- primitive call
   else
     local h = metatable(func).__call
     if h then
       return h(func, ...)
     else
       error("...")
     end
   end
 end
</pre>

<p></ul>

<p><a name="environ"></a><a name="2.9"></a><h2>2.9 - Environments</h2>

<p>Besides metatables,
objects of types thread, function, and userdata
have another table associated with them,
called their <em>environment</em>.
Like metatables, environments are regular tables and
multiple objects can share the same environment.

<p>Environments associated with userdata have no meaning for Lua.
It is only a feature for programmers to associate a table to
a userdata.

<p>Environments associated with threads are called
<em>global environments</em>.
They are used as the default environment for threads and
non-nested functions created by the thread
(through <a href="#pdf-loadfile"><code>loadfile</code></a>, <a href="#pdf-loadstring"><code>loadstring</code></a> or <a href="#pdf-load"><code>load</code></a>)
and can be directly accessed by C code (see <a href="#pseudo-index">3.3</a>).

<p>Environments associated with C functions can be directly
accessed by C code (see <a href="#pseudo-index">3.3</a>).
They are used as the default environment for other C functions
created by the function.

<p>Environments associated with Lua functions are used to resolve
all accesses to global variables within the function (see <a href="#variables">2.3</a>).
They are used as the default environment for other Lua functions
created by the function.

<p>You can change the environment of a Lua function or the
running thread by calling <a href="#pdf-setfenv"><code>setfenv</code></a>.
You can get the environment of a Lua function or the running thread
by calling <a href="#pdf-getfenv"><code>getfenv</code></a>.
To manipulate the environment of other objects
(userdata, C functions, other threads) you must
use the C API.

<p><a name="GC"></a><a name="2.10"></a><h2>2.10 - Garbage Collection</h2>

<p>Lua performs automatic memory management.
This means that
you have to worry neither about allocating memory for new objects
nor about freeing it when the objects are no longer needed.
Lua manages memory automatically by running
a <em>garbage collector</em> from time to time
to collect all <em>dead objects</em>
(that is, these objects that are no longer accessible from Lua).
All objects in Lua are subject to automatic management:
tables, userdata, functions, threads, and strings.

<p>Lua implements an incremental mark-and-sweep collector.
It uses two numbers to control its garbage-collection cycles:
the <em>garbage-collector pause</em> and
the <em>garbage-collector step multiplier</em>.

<p>The garbage-collector pause
controls how long the collector waits before starting a new cycle.
Larger values make the collector less aggressive.
Values smaller than 1 mean the collector will not wait to
start a new cycle.
A value of 2 means that the collector waits for the total memory in use
to double before starting a new cycle.

<p>The step multiplier
controls the relative speed of the collector relative to
memory allocation.
Larger values make the collector more aggressive but also increases
the size of each incremental step.
Values smaller than 1 make the collector too slow and
may result in  the collector never finishing a cycle.
The default, 2, means that the collector runs at "twice"
the speed of memory allocation.

<p>You can change these numbers by calling <a href="#lua_gc"><code>lua_gc</code></a> in C
or <a href="#pdf-collectgarbage"><code>collectgarbage</code></a> in Lua.
Both get as arguments percentage points
(so an argument 100 means a real value of 1).
With these functions you can also control 
the collector directly (e.g., stop and restart it).

<p><a name="2.10.1"></a><h3>2.10.1 - Garbage-Collection Metamethods</h3>

<p>Using the C API,
you can set garbage-collector metamethods for userdata (see <a href="#metatable">2.8</a>).
These metamethods are also called <em>finalizers</em>.
Finalizers allow you to coordinate Lua's garbage collection
with external resource management
(such as closing files, network or database connections,
or freeing your own memory).

<p>Garbage userdata with a field <code>__gc</code> in their metatables are not
collected immediately by the garbage collector.
Instead, Lua puts them in a list.
After the collection,
Lua does the equivalent of the following function
for each userdata in that list:
<pre>
 function gc_event (udata)
   local h = metatable(udata).__gc
   if h then
     h(udata)
   end
 end
</pre>

<p>At the end of each garbage-collection cycle,
the finalizers for userdata are called in <em>reverse</em>
order of their creation,
among those collected in that cycle.
That is, the first finalizer to be called is the one associated
with the userdata created last in the program.

<p><a name="weak-table"></a><a name="2.10.2"></a><h3>2.10.2 - Weak Tables</h3>

<p>A <em>weak table</em> is a table whose elements are
<em>weak references</em>.
A weak reference is ignored by the garbage collector.
In other words,
if the only references to an object are weak references,
then the garbage collector will collect this object.

<p>A weak table can have weak keys, weak values, or both.
A table with weak keys allows the collection of its keys,
but prevents the collection of its values.
A table with both weak keys and weak values allows the collection of
both keys and values.
In any case, if either the key or the value is collected,
the whole pair is removed from the table.
The weakness of a table is controlled by the value of the
<code>__mode</code> field of its metatable.
If the <code>__mode</code> field is a string containing the character `<code>k</code>&acute;,
the keys in the table are weak.
If <code>__mode</code> contains `<code>v</code>&acute;,
the values in the table are weak.

<p>After you use a table as a metatable,
you should not change the value of its field <code>__mode</code>.
Otherwise, the weak behavior of the tables controlled by this
metatable is undefined.

<p><a name="coroutine"></a><a name="2.11"></a><h2>2.11 - Coroutines</h2>

<p>Lua supports coroutines,
also called <em>collaborative multithreading</em>.
A coroutine in Lua represents an independent thread of execution.
Unlike threads in multithread systems, however,
a coroutine only suspends its execution by explicitly calling
a yield function.

<p>You create a coroutine with a call to <code>coroutine.create</code>.
Its sole argument is a function
that is the main function of the coroutine.
The <code>create</code> function only creates a new coroutine and
returns a handle to it (an object of type <em>thread</em>);
it does not start the coroutine execution.

<p>When you first call <code>coroutine.resume</code>,
passing as its first argument
the thread returned by <a href="#pdf-coroutine.create"><code>coroutine.create</code></a>,
the coroutine starts its execution,
at the first line of its main function.
Extra arguments passed to <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a> are passed on
to the coroutine main function.
After the coroutine starts running,
it runs until it terminates or <em>yields</em>.

<p>A coroutine can terminate its execution in two ways:
Normally, when its main function returns
(explicitly or implicitly, after the last instruction);
and abnormally, if there is an unprotected error.
In the first case, <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a> returns <b>true</b>,
plus any values returned by the coroutine main function.
In case of errors, <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a> returns <b>false</b>
plus an error message.

<p>A coroutine yields by calling <code>coroutine.yield</code>.
When a coroutine yields,
the corresponding <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a> returns immediately,
even if the yield happens inside nested function calls
(that is, not in the main function,
but in a function directly or indirectly called by the main function).
In the case of a yield, <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a> also returns <b>true</b>,
plus any values passed to <a href="#pdf-coroutine.yield"><code>coroutine.yield</code></a>.
The next time you resume the same coroutine,
it continues its execution from the point where it yielded,
with the call to <a href="#pdf-coroutine.yield"><code>coroutine.yield</code></a> returning any extra
arguments passed to <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a>.

<p>The <code>coroutine.wrap</code> function creates a coroutine,
just like <a href="#pdf-coroutine.create"><code>coroutine.create</code></a>,
but instead of returning the coroutine itself,
it returns a function that, when called, resumes the coroutine.
Any arguments passed to this function
go as extra arguments to <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a>.
<code>coroutine.wrap</code> returns all the values returned by <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a>,
except the first one (the boolean error code).
Unlike <a href="#pdf-coroutine.resume"><code>coroutine.resume</code></a>,
<code>coroutine.wrap</code> does not catch errors;
any error is propagated to the caller.

<p>As an example,
consider the next code:
<pre>
function foo (a)
  print("foo", a)
  return coroutine.yield(2*a)
end

co = coroutine.create(function (a,b)
      print("co-body", a, b)
      local r = foo(a+1)
      print("co-body", r)
      local r, s = coroutine.yield(a+b, a-b)
      print("co-body", r, s)
      return b, "end"
end)
       
print("main", coroutine.resume(co, 1, 10))
print("main", coroutine.resume(co, "r"))
print("main", coroutine.resume(co, "x", "y"))
print("main", coroutine.resume(co, "x", "y"))
</pre>
When you run it, it produces the following output:
<pre>
co-body 1       10
foo     2
main    true    4
co-body r
main    true    11      -9
co-body x       y
main    true    10      end
main    false   cannot resume dead coroutine
</pre>

<p>
<a name="API"></a><a name="3"></a><h1>3 - The Application Program Interface</h1>


<p>This section describes the C API for Lua, that is,
the set of C functions available to the host program to communicate
with Lua.
All API functions and related types and constants
are declared in the header file <code>lua.h</code>.

<p>Even when we use the term "function",
any facility in the API may be provided as a macro instead.
All such macros use each of its arguments exactly once
(except for the first argument, which is always a Lua state),
and so do not generate any hidden side-effects.

<p>As in most C libraries,
the Lua API functions do not check their arguments for validity or consistency.
However, you can change this behavior by compiling Lua
with a proper definition for the macro <code>luai_apicheck</code>,
in file <code>luaconf.h</code>.

<p><a name="3.1"></a><h2>3.1 - The Stack</h2>

<p>Lua uses a <em>virtual stack</em> to pass values to and from C.
Each element in this stack represents a Lua value
(<b>nil</b>, number, string, etc.).

<p>Whenever Lua calls C, the called function gets a new stack,
which is independent of previous stacks and of stacks of
C functions that are still active.
This stack initially contains any arguments to the C function
and it is where the C function pushes its results
to be returned to the caller (see <a href="#lua_CFunction"><code>lua_CFunction</code></a>).

<p>For convenience,
most query operations in the API do not follow a strict stack discipline.
Instead, they can refer to any element in the stack
by using an <em>index</em>:
A positive index represents an <em>absolute</em> stack position
(starting at 1);
a negative index represents an <em>offset</em> relative to the top of the stack.
More specifically, if the stack has <em>n</em> elements,
then index 1 represents the first element
(that is, the element that was pushed onto the stack first)
and
index <em>n</em> represents the last element;
index <em>-1</em> also represents the last element
(that is, the element at the top)
and index <em>-n</em> represents the first element.
We say that an index is <em>valid</em>
if it lies between 1 and the stack top
(that is, if <code>1 &#060;= abs(index) &#060;= top</code>).
 

<p><a name="3.2"></a><h2>3.2 - Stack Size</h2>

<p>When you interact with Lua API,
you are responsible for ensuring consistency.
In particular,
<em>you are responsible for controlling stack overflow</em>.
You can use the function <a href="#lua_checkstack"><code>lua_checkstack</code></a>
to grow the stack size.

<p>Whenever Lua calls C, 
it ensures that at least <code>LUA_MINSTACK</code> stack positions are available.
<code>LUA_MINSTACK</code> is defined as 20,
so that usually you do not have to worry about stack space
unless your code has loops pushing elements onto the stack.

<p>Most query functions accept as indices any value inside the
available stack space, that is, indices up to the maximum stack size
you have set through <a href="#lua_checkstack"><code>lua_checkstack</code></a>.
Such indices are called <em>acceptable indices</em>.
More formally, we define an <em>acceptable index</em>
as follows:
<pre>
       (index &#060; 0 &#038;&#038; abs(index) &#060;= top) || (index > 0 &#038;&#038; index &#060;= stackspace)
</pre>
Note that 0 is never an acceptable index.

<p><a name="pseudo-index"></a><a name="3.3"></a><h2>3.3 - Pseudo-Indices</h2>

<p>Unless otherwise noted,
any function that accepts valid indices can also be called with
<em>pseudo-indices</em>,
which represent some Lua values that are accessible to C code
but which are not in the stack.
Pseudo-indices are used to access the thread environment,
the function environment,
the registry,
and the upvalues of a C function (see <a href="#c-closure">3.4</a>).

<p>The thread environment (where global variables live) is
always at pseudo-index <code>LUA_GLOBALSINDEX</code>.
The environment of the running C function is always
at pseudo-index <code>LUA_ENVIRONINDEX</code>.

<p>To access and change the value of global variables,
you can use regular table operations over an environment table.
For instance, to access the value of a global variable, do
<pre>
       lua_getfield(L, LUA_GLOBALSINDEX, varname);
</pre>

<p><a name="c-closure"></a><a name="3.4"></a><h2>3.4 - C Closures</h2>

<p>When a C function is created,
it is possible to associate some values with it,
thus creating a <em>C closure</em>;
these values are called <em>upvalues</em> and are
accessible to the function whenever it is called
(see <a href="#lua_pushcclosure"><code>lua_pushcclosure</code></a>).

<p>Whenever a C function is called,
its upvalues are located at specific pseudo-indices.
These pseudo-indices are produced by the macro
<code>lua_upvalueindex</code>.
The first value associated with a function is at position
<code>lua_upvalueindex(1)</code>, and so on.
Any access to <code>lua_upvalueindex(<em>n</em>)</code>,
where <em>n</em> is greater than the number of upvalues of the
current function,
produces an acceptable (but invalid) index.

<p><a name="registry"></a><a name="3.5"></a><h2>3.5 - Registry</h2>

<p>Lua provides a <em>registry</em>,
a pre-defined table that can be used by any C code to
store whatever Lua value it needs to store.
This table is always located at pseudo-index
<code>LUA_REGISTRYINDEX</code>.
Any C library can store data into this table,
but it should take care to choose keys different from those used
by other libraries, to avoid collisions.
Typically, you should use as key a string containing your library name
or a light userdata with the address of a C object in your code.

<p>The integer keys in the registry are used by the reference mechanism,
implemented by the auxiliary library,
and therefore should not be used for other purposes.

<p><a name="3.6"></a><h2>3.6 - Error Handling in C</h2>

<p>Internally, Lua uses the C <code>longjmp</code> facility to handle errors.
(You can also choose to use exceptions if you use C++;
See file <code>luaconf.h</code>.)
When Lua faces any error
(such as memory allocation errors, type errors, syntax errors,
and runtime errors)
it <em>raises</em> an error;
that is, it does a long jump.
A <em>protected environment</em> uses <code>setjmp</code>
to set a recover point;
any error jumps to the most recent active recover point.

<p>Almost any function in the API may raise an error,
for instance due to a memory allocation error.
The following functions run in protected mode
(that is, they create a protected environment to run),
so they never raise an error:
<a href="#lua_newstate"><code>lua_newstate</code></a>, <a href="#lua_close"><code>lua_close</code></a>, <a href="#lua_load"><code>lua_load</code></a>,
<a href="#lua_pcall"><code>lua_pcall</code></a>, and <a href="#lua_cpcall"><code>lua_cpcall</code></a>.

<p>Inside a C function you can raise an error by calling <a href="#lua_error"><code>lua_error</code></a>.

<p><a name="3.7"></a><h2>3.7 - Functions and Types</h2>

<p>Here we list all functions and types from the C API in
alphabetical order.

<p><a name="lua_Alloc"></a>
<hr></hr><h3><code>lua_Alloc</code></h3>
<pre>
          typedef void * (*lua_Alloc) (void *ud,
                                       void *ptr,
                                       size_t osize,
                                       size_t nsize);

</pre>


<p>The type of the memory allocation function used by Lua states.
The allocator function must provide a
functionality similar to <code>realloc</code>,
but not exactly the same.
Its arguments are
<code>ud</code>, an opaque pointer passed to <a href="#lua_newstate"><code>lua_newstate</code></a>;
<code>ptr</code>, a pointer to the block being allocated/reallocated/freed;
<code>osize</code>, the original size of the block;
<code>nsize</code>, the new size of the block.
<code>ptr</code> is <code>NULL</code> if and only if <code>osize</code> is zero.
When <code>nsize</code> is zero, the allocator must return <code>NULL</code>;
if <code>osize</code> is not zero,
it should free the block pointed to by <code>ptr</code>.
When <code>nsize</code> is not zero, the allocator returns <code>NULL</code>
if and only if it cannot fill the request.
When <code>nsize</code> is not zero and <code>osize</code> is zero,
the allocator should behave like <code>malloc</code>.
When <code>nsize</code> and <code>osize</code> are not zero,
the allocator behaves like <code>realloc</code>.
Lua assumes that the allocator never fails when
<code>osize >= nsize</code>.

<p>Here is a simple implementation for the allocator function.
It is used in the auxiliary library by <a href="#lua_newstate"><code>lua_newstate</code></a>.
<pre>
static void *l_alloc (void *ud, void *ptr, size_t osize, size_t nsize) {
  (void)ud;     /* not used */
  (void)osize;  /* not used */
  if (nsize == 0) {
    free(ptr);  /* ANSI requires that free(NULL) has no effect */
    return NULL;
  }
  else
    /* ANSI requires that realloc(NULL, size) == malloc(size) */
    return realloc(ptr, nsize);
}
</pre>

<p><a name="lua_atpanic"></a>
<hr></hr><h3><code>lua_atpanic</code></h3>
<pre>
          lua_CFunction lua_atpanic (lua_State *L, lua_CFunction panicf);
</pre>


<p>Sets a new panic function and returns the old one.

<p>If an error happens outside any protected environment,
Lua calls a <em>panic function</em>
and then calls <code>exit(EXIT_FAILURE)</code>,
thus exiting the host application.
Your panic function may avoid this exit by
never returning (e.g., doing a long jump).

<p>The panic function can access the error message at the top of the stack.

<p><a name="lua_call"></a>
<hr></hr><h3><code>lua_call</code></h3>
<pre>
          void lua_call (lua_State *L, int nargs, int nresults);
</pre>


<p>Calls a function.

<p>To call a function you must use the following protocol:
First, the function to be called is pushed onto the stack;
then, the arguments to the function are pushed
in direct order;
that is, the first argument is pushed first.
Finally you call <a href="#lua_call"><code>lua_call</code></a>;
<code>nargs</code> is the number of arguments that you pushed onto the stack.
All arguments and the function value are popped from the stack
when the function is called.
The function results are pushed onto the stack when the function returns.
The number of results is adjusted to <code>nresults</code>,
unless <code>nresults</code> is <code>LUA_MULTRET</code>.
In this case, <em>all</em> results from the function are pushed.
Lua takes care that the returned values fit into the stack space.
The function results are pushed onto the stack in direct order
(the first result is pushed first),
so that after the call the last result is on the top of the stack.

<p>Any error inside the called function is propagated upwards
(with a <code>longjmp</code>).

<p>The following example shows how the host program may do the
equivalent to this Lua code:
<pre>
       a = f("how", t.x, 14)
</pre>
Here it is in C:
<pre>
    lua_getfield(L, LUA_GLOBALSINDEX, "f");          /* function to be called */
    lua_pushstring(L, "how");                                 /* 1st argument */
    lua_getfield(L, LUA_GLOBALSINDEX, "t");            /* table to be indexed */
    lua_getfield(L, -1, "x");                 /* push result of t.x (2nd arg) */
    lua_remove(L, -2);                           /* remove `t' from the stack */
    lua_pushinteger(L, 14);                                   /* 3rd argument */
    lua_call(L, 3, 1);         /* call function with 3 arguments and 1 result */
    lua_setfield(L, LUA_GLOBALSINDEX, "a");        /* set global variable `a' */
</pre>
Note that the code above is "balanced":
at its end, the stack is back to its original configuration.
This is considered good programming practice.

<p><a name="lua_CFunction"></a>
<hr></hr><h3><code>lua_CFunction</code></h3>
<pre>
          typedef int (*lua_CFunction) (lua_State *L);
</pre>


<p>Type for C functions.

<p>In order to communicate properly with Lua,
a C function must use the following protocol,
which defines the way parameters and results are passed:
A C function receives its arguments from Lua in its stack
in direct order (the first argument is pushed first).
So, when the function starts,
<a href="#lua_gettop(L)"><code>lua_gettop(L)</code></a> returns the number of arguments received by the function.
The first argument (if any) is at index 1
and its last argument is at index <a href="#lua_gettop(L)"><code>lua_gettop(L)</code></a>.
To return values to Lua, a C function just pushes them onto the stack,
in direct order (the first result is pushed first),
and returns the number of results.
Any other value in the stack below the results will be properly
discarded by Lua.
Like a Lua function, a C function called by Lua can also return
many results.

<p>As an example, the following function receives a variable number
of numerical arguments and returns their average and sum:
<pre>
       static int foo (lua_State *L) {
         int n = lua_gettop(L);    /* number of arguments */
         lua_Number sum = 0;
         int i;
         for (i = 1; i &#060;= n; i++) {
           if (!lua_isnumber(L, i)) {
             lua_pushstring(L, "incorrect argument to function `average'");
             lua_error(L);
           }
           sum += lua_tonumber(L, i);
         }
         lua_pushnumber(L, sum/n);        /* first result */
         lua_pushnumber(L, sum);         /* second result */
         return 2;                   /* number of results */
       }
</pre>

<p><a name="lua_checkstack"></a>
<hr></hr><h3><code>lua_checkstack</code></h3>
<pre>
          int lua_checkstack (lua_State *L, int extra);
</pre>


<p>Ensures that there are at least <code>extra</code> free stack slots in the stack.
It returns false if it cannot grow the stack to that size.
This function never shrinks the stack;
if the stack is already larger than the new size,
it is left unchanged.

<p><a name="lua_close"></a>
<hr></hr><h3><code>lua_close</code></h3>
<pre>
          void lua_close (lua_State *L);
</pre>


<p>Destroys all objects in the given Lua state
(calling the corresponding garbage-collection metamethods, if any)
and frees all dynamic memory used by this state.
On several platforms, you may not need to call this function,
because all resources are naturally released when the host program ends.
On the other hand, long-running programs,
such as a daemon or a web server,
might need to release states as soon as they are not needed,
to avoid growing too large.

<p><a name="lua_concat"></a>
<hr></hr><h3><code>lua_concat</code></h3>
<pre>
          void lua_concat (lua_State *L, int n);
</pre>


<p>Concatenates the <code>n</code> values at the top of the stack,
pops them, and leaves the result at the top.
If <code>n</code> is 1, the result is the single string on the stack
(that is, the function does nothing);
if <code>n</code> is 0, the result is the empty string.
Concatenation is done following the usual semantics of Lua
(see <a href="#concat">2.5.4</a>).

<p><a name="lua_cpcall"></a>
<hr></hr><h3><code>lua_cpcall</code></h3>
<pre>
          int lua_cpcall (lua_State *L, lua_CFunction func, void *ud);
</pre>


<p>Calls the C function <code>func</code> in protected mode.
<code>func</code> starts with only one element in its stack,
a light userdata containing <code>ud</code>.
In case of errors,
<a href="#lua_cpcall"><code>lua_cpcall</code></a> returns the same error codes as <a href="#lua_pcall"><code>lua_pcall</code></a>,
plus the error object on the top of the stack;
otherwise, it returns zero, and does not change the stack.
All values returned by <code>func</code> are discarded.

<p><a name="lua_createtable"></a>
<hr></hr><h3><code>lua_createtable</code></h3>
<pre>
          void lua_createtable (lua_State *L, int narr, int nrec);
</pre>


<p>Creates a new empty table and pushes it onto the stack.
The new table has space pre-allocated
for <code>narr</code> array elements and <code>nrec</code> non-array elements.
This pre-allocation is useful when you know exactly how many elements
the table will have.
Otherwise you can use the function <a href="#lua_newtable"><code>lua_newtable</code></a>.

<p><a name="lua_dump"></a>
<hr></hr><h3><code>lua_dump</code></h3>
<pre>
          int lua_dump (lua_State *L, lua_Writer writer, void *data);
</pre>


<p>Dumps a function as a binary chunk.
Receives a Lua function on the top of the stack
and produces a binary chunk that,
if loaded again,
results in a function equivalent to the one dumped.
As it produces parts of the chunk,
<a href="#lua_dump"><code>lua_dump</code></a> calls function <code>writer</code> (see <a href="#lua_Writer"><code>lua_Writer</code></a>)
with the given <code>data</code>
to write them.

<p>The value returned is the error code returned by the last
call to the writer;
0 means no errors.

<p>This function does not pop the Lua function from the stack.

<p><a name="lua_equal"></a>
<hr></hr><h3><code>lua_equal</code></h3>
<pre>
          int lua_equal (lua_State *L, int index1, int index2);
</pre>


<p>Returns 1 if the two values in acceptable indices <code>index1</code> and
<code>index2</code> are equal,
following the semantics of the Lua <code>==</code> operator
(that is, may call metamethods).
Otherwise returns 0.
Also returns 0 if any of the indices is non valid.

<p><a name="lua_error"></a>
<hr></hr><h3><code>lua_error</code></h3>
<pre>
          int lua_error (lua_State *L);
</pre>


<p>Generates a Lua error.
The error message (which can actually be a Lua value of any type)
must be on the stack top.
This function does a long jump,
and therefore never returns.
(see <a href="#pdf-luaL_error"><code>luaL_error</code></a>).

<p><a name="lua_gc"></a>
<hr></hr><h3><code>lua_gc</code></h3>
<pre>
          int lua_gc (lua_State *L, int what, int data);
</pre>


<p>Controls the garbage collector.

<p>This function performs several tasks,
according to the value of the parameter <code>what</code>:
<ul>
<li> <code>LUA_GCSTOP</code>--- stops the garbage collector.
<li> <code>LUA_GCRESTART</code>--- restarts the garbage collector.
<li> <code>LUA_GCCOLLECT</code>--- performs a full garbage-collection cycle.
<li> <code>LUA_GCCOUNT</code>--- returns the current
amount of memory (in Kbytes) in use by Lua.
<li> <code>LUA_GCCOUNTB</code>--- returns the remainder of
dividing the current amount of bytes of memory in use by Lua 
by 1024.
<li> <code>LUA_GCSTEP</code>--- performs an incremental step of
garbage collection.
The step "size" is controlled by <code>data</code>
(larger values mean more steps) in a non-specified way.
If you want to control the step size
you must tune experimentally the value of <code>data</code>.
The function returns 1 if the step finished a
garbage-collection cycle.
<li> <code>LUA_GCSETPAUSE</code>---
sets <code>data</code>/100 as the new value
for the <em>pause</em> of the collector (see <a href="#GC">2.10</a>).
The function returns the previous value of the pause.
<li> <code>LUA_GCSETSTEPMUL</code>---
sets <code>arg</code>/100 as the new value for the <em>step multiplier</em> of
the collector (see <a href="#GC">2.10</a>).
The function returns the previous value of the step multiplier.
</ul>

<p><a name="lua_getallocf"></a>
<hr></hr><h3><code>lua_getallocf</code></h3>
<pre>
          lua_Alloc lua_getallocf (lua_State *L, void **ud);
</pre>


<p>Returns the memory allocator function of a given state.
If <code>ud</code> is not <code>NULL</code>, Lua stores in <code>*ud</code> the
opaque pointer passed to <a href="#lua_newstate"><code>lua_newstate</code></a>.

<p><a name="lua_getfenv"></a>
<hr></hr><h3><code>lua_getfenv</code></h3>
<pre>
          void lua_getfenv (lua_State *L, int index);
</pre>


<p>Pushes on the stack the environment table of
the value at the given index.

<p><a name="lua_getfield"></a>
<hr></hr><h3><code>lua_getfield</code></h3>
<pre>
          void lua_getfield (lua_State *L, int index, const char *k);
</pre>


<p>Pushes onto the stack the value <code>t[k]</code>,
where <code>t</code> is the value at the given valid index <code>index</code>.
As in Lua, this function may trigger a metamethod
for the "index" event (see <a href="#metatable">2.8</a>).

<p><a name="lua_getglobal"></a>
<hr></hr><h3><code>lua_getglobal</code></h3>
<pre>
          void lua_getglobal (lua_State *L, const char *name);
</pre>


<p>Pushes onto the stack the value of the global <code>name</code>.
It is defined as a macro:
<pre>
#define lua_getglobal(L,s)  lua_getfield(L, LUA_GLOBALSINDEX, s)
</pre>

<p><a name="lua_getmetatable"></a>
<hr></hr><h3><code>lua_getmetatable</code></h3>
<pre>
          int lua_getmetatable (lua_State *L, int index);
</pre>


<p>Pushes onto the stack the metatable of the value at the given
acceptable index.
If the index is not valid,
or if the value does not have a metatable,
the function returns 0 and pushes nothing on the stack.

<p><a name="lua_gettable"></a>
<hr></hr><h3><code>lua_gettable</code></h3>
<pre>
          void lua_gettable (lua_State *L, int index);
</pre>


<p>Pushes onto the stack the value <code>t[k]</code>,
where <code>t</code> is the value at the given valid index <code>index</code>
and <code>k</code> is the value at the top of the stack.

<p>This function pops the key from the stack
(putting the resulting value in its place).
As in Lua, this function may trigger a metamethod
for the "index" event (see <a href="#metatable">2.8</a>).

<p><a name="lua_gettop"></a>
<hr></hr><h3><code>lua_gettop</code></h3>
<pre>
          int lua_gettop (lua_State *L);
</pre>


<p>Returns the index of the top element in the stack.
Because indices start at 1,
this result is equal to the number of elements in the stack
(and so 0 means an empty stack).

<p><a name="lua_insert"></a>
<hr></hr><h3><code>lua_insert</code></h3>
<pre>
          void lua_insert (lua_State *L, int index);
</pre>


<p>Moves the top element into the given valid index,
shifting up the elements above this index to open space.
Cannot be called with a pseudo-index,
because a pseudo-index is not an actual stack position.

<p><a name="lua_Integer"></a>
<hr></hr><h3><code>lua_Integer</code></h3>
<pre>
          typedef ptrdiff_t lua_Integer;
</pre>


<p>The type used by the Lua API to represent integral values.

<p>By default it is a <code>ptrdiff_t</code>,
which is usually the largest integral type the machine handles
"comfortably".

<p><a name="lua_isboolean"></a>
<hr></hr><h3><code>lua_isboolean</code></h3>
<pre>
          int lua_isboolean (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index has type boolean,
and 0 otherwise.

<p><a name="lua_iscfunction"></a>
<hr></hr><h3><code>lua_iscfunction</code></h3>
<pre>
          int lua_iscfunction (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a C function,
and 0 otherwise.

<p><a name="lua_isfunction"></a>
<hr></hr><h3><code>lua_isfunction</code></h3>
<pre>
          int lua_isfunction (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a function
(either C or Lua), and 0 otherwise.

<p><a name="lua_islightuserdata"></a>
<hr></hr><h3><code>lua_islightuserdata</code></h3>
<pre>
          int lua_islightuserdata (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a light userdata,
and 0 otherwise.

<p><a name="lua_isnil"></a>
<hr></hr><h3><code>lua_isnil</code></h3>
<pre>
          int lua_isnil (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is <b>nil</b>,
and 0 otherwise.

<p><a name="lua_isnumber"></a>
<hr></hr><h3><code>lua_isnumber</code></h3>
<pre>
          int lua_isnumber (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a number
or a string convertible to a number,
and 0 otherwise.

<p><a name="lua_isstring"></a>
<hr></hr><h3><code>lua_isstring</code></h3>
<pre>
          int lua_isstring (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a string
or a number (which is always convertible to a string),
and 0 otherwise.

<p><a name="lua_istable"></a>
<hr></hr><h3><code>lua_istable</code></h3>
<pre>
          int lua_istable (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a table,
and 0 otherwise.

<p><a name="lua_isthread"></a>
<hr></hr><h3><code>lua_isthread</code></h3>
<pre>
          int lua_isthread (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a thread,
and 0 otherwise.

<p><a name="lua_isuserdata"></a>
<hr></hr><h3><code>lua_isuserdata</code></h3>
<pre>
          int lua_isuserdata (lua_State *L, int index);
</pre>


<p>Returns 1 if the value at the given acceptable index is a userdata
(either full or light), and 0 otherwise.

<p><a name="lua_lessthan"></a>
<hr></hr><h3><code>lua_lessthan</code></h3>
<pre>
          int lua_lessthan (lua_State *L, int index1, int index2);
</pre>


<p>Returns 1 if the value at acceptable index <code>index1</code> is smaller
than the value at acceptable index <code>index2</code>,
following the semantics of the Lua <code>&#060;</code> operator
(that is, may call metamethods).
Otherwise returns 0.
Also returns 0 if any of the indices is non valid.

<p><a name="lua_load"></a>
<hr></hr><h3><code>lua_load</code></h3>
<pre>
          int lua_load (lua_State *L, lua_Reader reader, void *data,
                                      const char *chunkname);

</pre>


<p>Loads a Lua chunk.
If there are no errors,
<a href="#lua_load"><code>lua_load</code></a> pushes the compiled chunk as a Lua
function on top of the stack.
Otherwise, it pushes an error message.
The return values of <a href="#lua_load"><code>lua_load</code></a> are:
<ul>
<li> 0 --- no errors;
<li> <code>LUA_ERRSYNTAX</code> ---
syntax error during pre-compilation.
<li> <code>LUA_ERRMEM</code> ---
memory allocation error.
</ul>

<p><a href="#lua_load"><code>lua_load</code></a> automatically detects whether the chunk is text or binary,
and loads it accordingly (see program <code>luac</code>).

<p><a href="#lua_load"><code>lua_load</code></a> uses a user-supplied <code>reader</code> function to read the chunk
(see <a href="#lua_Reader"><code>lua_Reader</code></a>).
The <code>data</code> argument is an opaque value passed to the reader function.

<p>The <code>chunkname</code> argument gives a name to the chunk,
which is used for error messages and in debug information (see <a href="#debugI">3.8</a>).

<p><a name="lua_newstate"></a>
<hr></hr><h3><code>lua_newstate</code></h3>
<pre>
          lua_State *lua_newstate (lua_Alloc f, void *ud);
</pre>


<p>Creates a new, independent state.
Returns <code>NULL</code> if cannot create the state
(due to lack of memory).
The argument <code>f</code> is the allocator function;
Lua does all memory allocation for this state through this function.
The second argument, <code>ud</code>, is an opaque pointer that Lua
simply passes to the allocator in every call.

<p><a name="lua_newtable"></a>
<hr></hr><h3><code>lua_newtable</code></h3>
<pre>
          void lua_newtable (lua_State *L);
</pre>


<p>Creates a new empty table and pushes it onto the stack.
Equivalent to <code>lua_createtable(L, 0, 0)</code>.

<p><a name="lua_newthread"></a>
<hr></hr><h3><code>lua_newthread</code></h3>
<pre>
          lua_State *lua_newthread (lua_State *L);
</pre>


<p>Creates a new thread, pushes it on the stack,
and returns a pointer to a <a href="#lua_State"><code>lua_State</code></a> that represents this new thread.
The new state returned by this function shares with the original state
all global objects (such as tables),
but has an independent execution stack.

<p>There is no explicit function to close or to destroy a thread.
Threads are subject to garbage collection,
like any Lua object.

<p><a name="lua_newuserdata"></a>
<hr></hr><h3><code>lua_newuserdata</code></h3>
<pre>
          void *lua_newuserdata (lua_State *L, size_t size);
</pre>


<p>This function allocates a new block of memory with the given size,
pushes on the stack a new full userdata with the block address,
and returns this address.

<p>Userdata represents C values in Lua.
A <em>full userdata</em> represents a block of memory.
It is an object (like a table):
You must create it, it can have its own metatable,
and you can detect when it is being collected.
A full userdata is only equal to itself (under raw equality).

<p>When Lua collects a full userdata with a <code>gc</code> metamethod,
Lua calls the metamethod and marks the userdata as finalized.
When this userdata is collected again then
Lua frees its corresponding memory.

<p><a name="lua_next"></a>
<hr></hr><h3><code>lua_next</code></h3>
<pre>
          int lua_next (lua_State *L, int index);
</pre>


<p>Pops a key from the stack,
and pushes a key-value pair from the table at the given index
(the "next" pair after the given key).
If there are no more elements in the table,
then <a href="#lua_next"><code>lua_next</code></a> returns 0 (and pushes nothing).

<p>A typical traversal looks like this:
<pre>
       /* table is in the stack at index `t' */
       lua_pushnil(L);  /* first key */
       while (lua_next(L, t) != 0) {
         /* `key' is at index -2 and `value' at index -1 */
         printf("%s - %s\n",
           lua_typename(L, lua_type(L, -2)), lua_typename(L, lua_type(L, -1)));
         lua_pop(L, 1);  /* removes `value'; keeps `key' for next iteration */
       }
</pre>

<p>While traversing a table,
do not call <a href="#lua_tolstring"><code>lua_tolstring</code></a> directly on a key,
unless you know that the key is actually a string.
Recall that <a href="#lua_tolstring"><code>lua_tolstring</code></a> <em>changes</em>
the value at the given index;
this confuses the next call to <a href="#lua_next"><code>lua_next</code></a>.

<p><a name="lua_Number"></a>
<hr></hr><h3><code>lua_Number</code></h3>
<pre>
          typedef double lua_Number;
</pre>


<p>The type of numbers in Lua.
By default, it is double, but that can be changed in <code>luaconf.h</code>.

<p>Through the configuration file you can change
Lua to operate with another type for numbers (e.g., float or long).

<p><a name="lua_objlen"></a>
<hr></hr><h3><code>lua_objlen</code></h3>
<pre>
          size_t lua_objlen (lua_State *L, int index);
</pre>


<p>Returns the "length" of the value at the given acceptable index:
for strings, this is the string length;
for tables, this is the result of the length operator (`<code>#</code>&acute;);
for userdata, this is the size of the block of memory allocated
for the userdata;
for other values, it is 0.

<p><a name="lua_pcall"></a>
<hr></hr><h3><code>lua_pcall</code></h3>
<pre>
          lua_pcall (lua_State *L, int nargs, int nresults, int errfunc);
</pre>


<p>Calls a function in protected mode.

<p>Both <code>nargs</code> and <code>nresults</code> have the same meaning as
in <a href="#lua_call"><code>lua_call</code></a>.
If there are no errors during the call,
<a href="#lua_pcall"><code>lua_pcall</code></a> behaves exactly like <a href="#lua_call"><code>lua_call</code></a>.
However, if there is any error,
<a href="#lua_pcall"><code>lua_pcall</code></a> catches it,
pushes a single value on the stack (the error message),
and returns an error code.
Like <a href="#lua_call"><code>lua_call</code></a>,
<a href="#lua_pcall"><code>lua_pcall</code></a> always removes the function
and its arguments from the stack.

<p>If <code>errfunc</code> is 0,
then the error message returned on the stack
is exactly the original error message.
Otherwise, <code>errfunc</code> is the stack index of an
<em>error handler function</em>.
(In the current implementation, this index cannot be a pseudo-index.)
In case of runtime errors,
this function will be called with the error message
and its return value will be the message returned on the stack by <a href="#lua_pcall"><code>lua_pcall</code></a>.

<p>Typically, the error handler function is used to add more debug
information to the error message, such as a stack traceback.
Such information cannot be gathered after the return of <a href="#lua_pcall"><code>lua_pcall</code></a>,
since by then the stack has unwound.

<p>The <a href="#lua_pcall"><code>lua_pcall</code></a> function returns 0 in case of success
or one of the following error codes
(defined in <code>lua.h</code>):
<ul>
<li> <code>LUA_ERRRUN</code> --- a runtime error.
<li> <code>LUA_ERRMEM</code> --- memory allocation error.
For such errors, Lua does not call the error handler function.
<li> <code>LUA_ERRERR</code> ---
error while running the error handler function.
</ul>

<p><a name="lua_pop"></a>
<hr></hr><h3><code>lua_pop</code></h3>
<pre>
          void lua_pop (lua_State *L, int n);
</pre>


<p>Pops <code>n</code> elements from the stack.

<p><a name="lua_pushboolean"></a>
<hr></hr><h3><code>lua_pushboolean</code></h3>
<pre>
          void lua_pushboolean (lua_State *L, int b);
</pre>


<p>Pushes a boolean value with value <code>b</code> onto the stack.

<p><a name="lua_pushcclosure"></a>
<hr></hr><h3><code>lua_pushcclosure</code></h3>
<pre>
          void lua_pushcclosure (lua_State *L, lua_CFunction fn, int n);
</pre>


<p>Pushes a new C closure onto the stack.

<p>When a C function is created,
it is possible to associate some values with it,
thus creating a <em>C closure</em> (see <a href="#c-closure">3.4</a>);
these values are then accessible to the function whenever it is called.
To associate values with a C function,
first these values should be pushed onto the stack
(when there are multiple values, the first value is pushed first).
Then <a href="#lua_pushcclosure"><code>lua_pushcclosure</code></a>
is called to create and push the C function onto the stack,
with the argument <code>n</code> telling how many values should be
associated with the function.
<a href="#lua_pushcclosure"><code>lua_pushcclosure</code></a> also pops these values from the stack.

<p><a name="lua_pushcfunction"></a>
<hr></hr><h3><code>lua_pushcfunction</code></h3>
<pre>
          void lua_pushcfunction (lua_State *L, lua_CFunction f);
</pre>


<p>Pushes a C function onto the stack.
This function receives a pointer to a C function
and pushes on the stack a Lua value of type <code>function</code> that,
when called, invokes the corresponding C function.

<p>Any function to be registered in Lua must
follow the correct protocol to receive its parameters
and return its results (see <a href="#lua_CFunction"><code>lua_CFunction</code></a>).

<p>The call <code>lua_pushcfunction(L, f)</code> is equivalent to
<code>lua_pushcclosure(L, f, 0)</code>.

<p><a name="lua_pushfstring"></a>
<hr></hr><h3><code>lua_pushfstring</code></h3>
<pre>
          const char *lua_pushfstring (lua_State *L, const char *fmt, ...);
</pre>


<p>Pushes onto the stack a formatted string
and returns a pointer to this string.
It is similar to the C function <code>sprintf</code>,
but has some important differences:
<ul>
<li> You do not have to allocate space for the result:
The result is a Lua string and Lua takes care of memory allocation
(and deallocation, through garbage collection).
<li> The conversion specifiers are quite restricted.
There are no flags, widths, or precisions.
The conversion specifiers can only be
`<code>%%</code>&acute; (inserts a `<code>%</code>&acute; in the string),
`<code>%s</code>&acute; (inserts a zero-terminated string, with no size restrictions),
`<code>%f</code>&acute; (inserts a <a href="#lua_Number"><code>lua_Number</code></a>),
`<code>%p</code>&acute; (inserts a pointer as a hexadecimal numeral),
`<code>%d</code>&acute; (inserts an <code>int</code>), and
`<code>%c</code>&acute; (inserts an <code>int</code> as a character).
</ul>

<p><a name="lua_pushinteger"></a>
<hr></hr><h3><code>lua_pushinteger</code></h3>
<pre>
          void lua_pushinteger (lua_State *L, lua_Integer n);
</pre>


<p>Pushes a number with value <code>n</code> onto the stack.

<p><a name="lua_pushlightuserdata"></a>
<hr></hr><h3><code>lua_pushlightuserdata</code></h3>
<pre>
          void lua_pushlightuserdata (lua_State *L, void *p);
</pre>


<p>Pushes a light userdata onto the stack.

<p>Userdata represents C values in Lua.
A <em>light userdata</em> represents a pointer.
It is a value (like a number):
You do not create it, it has no metatables,
it is not collected (as it was never created).
A light userdata is equal to "any"
light userdata with the same C address.

<p><a name="lua_pushlstring"></a>
<hr></hr><h3><code>lua_pushlstring</code></h3>
<pre>
          void lua_pushlstring (lua_State *L, const char *s, size_t len);
</pre>


<p>Pushes the string pointed to by <code>s</code> with size <code>len</code>
onto the stack.
Lua makes (or reuses) an internal copy of the given string,
so the memory at <code>s</code> can be freed or reused immediately after
the function returns.
The string can contain embedded zeros.

<p><a name="lua_pushnil"></a>
<hr></hr><h3><code>lua_pushnil</code></h3>
<pre>
          void lua_pushnil (lua_State *L);
</pre>


<p>Pushes a nil value onto the stack.

<p><a name="lua_pushnumber"></a>
<hr></hr><h3><code>lua_pushnumber</code></h3>
<pre>
          void lua_pushnumber (lua_State *L, lua_Number n);
</pre>


<p>Pushes a number with value <code>n</code> onto the stack.

<p><a name="lua_pushstring"></a>
<hr></hr><h3><code>lua_pushstring</code></h3>
<pre>
          void lua_pushstring (lua_State *L, const char *s);
</pre>


<p>Pushes the zero-terminated string pointed to by <code>s</code>
onto the stack.
Lua makes (or reuses) an internal copy of the given string,
so the memory at <code>s</code> can be freed or reused immediately after
the function returns.
The string cannot contain embedded zeros;
it is assumed to end at the first zero.

<p><a name="lua_pushthread"></a>
<hr></hr><h3><code>lua_pushthread</code></h3>
<pre>
          void lua_pushthread (lua_State *L);
</pre>


<p>Pushes the thread represented by <code>L</code> onto the stack.

<p><a name="lua_pushvalue"></a>
<hr></hr><h3><code>lua_pushvalue</code></h3>
<pre>
          void lua_pushvalue (lua_State *L, int index);
</pre>


<p>Pushes a copy of the element at the given valid index
onto the stack.

<p><a name="lua_pushvfstring"></a>
<hr></hr><h3><code>lua_pushvfstring</code></h3>
<pre>
          const char *lua_pushvfstring (lua_State *L, const char *fmt, va_list argp);
</pre>


<p>Equivalent to <a href="#lua_pushfstring"><code>lua_pushfstring</code></a>, except that it receives a <code>va_list</code>
instead of a variable number of arguments.

<p><a name="lua_rawequal"></a>
<hr></hr><h3><code>lua_rawequal</code></h3>
<pre>
          int lua_rawequal (lua_State *L, int index1, int index2);
</pre>


<p>Returns 1 if the two values in acceptable indices <code>index1</code> and
<code>index2</code> are primitively equal
(that is, without calling metamethods).
Otherwise returns 0.
Also returns 0 if any of the indices are non valid.

<p><a name="lua_rawget"></a>
<hr></hr><h3><code>lua_rawget</code></h3>
<pre>
          void lua_rawget (lua_State *L, int index);
</pre>


<p>Similar to <a href="#lua_gettable"><code>lua_gettable</code></a>, but does a raw access
(i.e., without metamethods).

<p><a name="lua_rawgeti"></a>
<hr></hr><h3><code>lua_rawgeti</code></h3>
<pre>
          void lua_rawgeti (lua_State *L, int index, int n);
</pre>


<p>Pushes onto the stack the value <code>t[n]</code>,
where <code>t</code> is the value at the given valid index <code>index</code>.
The access is raw;
that is, it does not invoke metamethods.

<p><a name="lua_rawset"></a>
<hr></hr><h3><code>lua_rawset</code></h3>
<pre>
          void lua_rawset (lua_State *L, int index);
</pre>


<p>Similar to <a href="#lua_settable"><code>lua_settable</code></a>, but does a raw assignment
(i.e., without metamethods).

<p><a name="lua_rawseti"></a>
<hr></hr><h3><code>lua_rawseti</code></h3>
<pre>
          void lua_rawseti (lua_State *L, int index, int n);
</pre>


<p>Does the equivalent of <code>t[n] = v</code>,
where <code>t</code> is the value at the given valid index <code>index</code>
and <code>v</code> is the value at the top of the stack,

<p>This function pops the value from the stack.
The assignment is raw;
that is, it does not invoke metamethods.

<p><a name="lua_Reader"></a>
<hr></hr><h3><code>lua_Reader</code></h3>
<pre>
          typedef const char * (*lua_Reader)
                               (lua_State *L, void *data, size_t *size);

</pre>


<p>The reader function used by <a href="#lua_load"><code>lua_load</code></a>.
Every time it needs another piece of the chunk,
<a href="#lua_load"><code>lua_load</code></a> calls the reader,
passing along its <code>data</code> parameter.
The reader must return a pointer to a block of memory
with a new piece of the chunk
and set <code>size</code> to the block size.
The block must exist until the reader function is called again.
To signal the end of the chunk, the reader must return <code>NULL</code>.
The reader function may return pieces of any size greater than zero.

<p><a name="lua_register"></a>
<hr></hr><h3><code>lua_register</code></h3>
<pre>
          void lua_register (lua_State *L, const char *name, lua_CFunction f);
</pre>


<p>Sets the C function <code>f</code> as the new value of global <code>name</code>.
It is defined as a macro:
<pre>
#define lua_register(L,n,f)  (lua_pushcfunction(L, f), lua_setglobal(L, n))
</pre>

<p><a name="lua_remove"></a>
<hr></hr><h3><code>lua_remove</code></h3>
<pre>
          void lua_remove (lua_State *L, int index);
</pre>


<p>Removes the element at the given valid index,
shifting down the elements above this index to fill the gap.
Cannot be called with a pseudo-index,
because a pseudo-index is not an actual stack position.

<p><a name="lua_replace"></a>
<hr></hr><h3><code>lua_replace</code></h3>
<pre>
          void lua_replace (lua_State *L, int index);
</pre>


<p>Moves the top element into the given position (and pops it),
without shifting any element
(therefore replacing the value at the given position).

<p><a name="lua_resume"></a>
<hr></hr><h3><code>lua_resume</code></h3>
<pre>
          int lua_resume (lua_State *L, int narg);
</pre>


<p>Starts and resumes a coroutine in a given thread.

<p>To start a coroutine, you first create a new thread
(see <a href="#lua_newthread"><code>lua_newthread</code></a>);
then you push on its stack the main function plus any eventual arguments;
then you call <a href="#lua_resume"><code>lua_resume</code></a>,
with <code>narg</code> being the number of arguments.
This call returns when the coroutine suspends or finishes its execution.
When it returns, the stack contains all values passed to <a href="#lua_yield"><code>lua_yield</code></a>,
or all values returned by the body function.
<a href="#lua_resume"><code>lua_resume</code></a> returns
<a href="#pdf-LUA_YIELD"><code>LUA_YIELD</code></a> if the coroutine yields,
0 if the coroutine finishes its execution
without errors,
or an error code in case of errors (see <a href="#lua_pcall"><code>lua_pcall</code></a>).
In case of errors,
the stack is not unwound,
so you can use the debug API over it.
The error message is on the top of the stack.
To restart a coroutine, you put on its stack only the values to
be passed as results from <code>yield</code>,
and then call <a href="#lua_resume"><code>lua_resume</code></a>.

<p><a name="lua_setallocf"></a>
<hr></hr><h3><code>lua_setallocf</code></h3>
<pre>
          void lua_setallocf (lua_State *L, lua_Alloc f, void *ud);
</pre>


<p>Changes the allocator function of a given state to <code>f</code>
with user data <code>ud</code>.

<p><a name="lua_setfenv"></a>
<hr></hr><h3><code>lua_setfenv</code></h3>
<pre>
          int lua_setfenv (lua_State *L, int index);
</pre>


<p>Pops a table from the stack and sets it as
the new environment for the value at the given index.
If the value at the given index is
neither a function nor a thread nor a userdata,
<a href="#lua_setfenv"><code>lua_setfenv</code></a> returns 0.
Otherwise it returns 1.

<p><a name="lua_setfield"></a>
<hr></hr><h3><code>lua_setfield</code></h3>
<pre>
          void lua_setfield (lua_State *L, int index, const char *k);
</pre>


<p>Does the equivalent to <code>t[k] = v</code>,
where <code>t</code> is the value at the given valid index <code>index</code>
and <code>v</code> is the value at the top of the stack,

<p>This function pops the value from the stack.
As in Lua, this function may trigger a metamethod
for the "newindex" event (see <a href="#metatable">2.8</a>).

<p><a name="lua_setglobal"></a>
<hr></hr><h3><code>lua_setglobal</code></h3>
<pre>
          void lua_setglobal (lua_State *L, const char *name);
</pre>


<p>Pops a value from the stack and
sets it as the new value of global <code>name</code>.
It is defined as a macro:
<pre>
#define lua_setglobal(L,s)   lua_setfield(L, LUA_GLOBALSINDEX, s)
</pre>

<p><a name="lua_setmetatable"></a>
<hr></hr><h3><code>lua_setmetatable</code></h3>
<pre>
          int lua_setmetatable (lua_State *L, int index);
</pre>


<p>Pops a table from the stack and
sets it as the new metatable for the value at the given
acceptable index.

<p><a name="lua_settable"></a>
<hr></hr><h3><code>lua_settable</code></h3>
<pre>
          void lua_settable (lua_State *L, int index);
</pre>


<p>Does the equivalent to <code>t[k] = v</code>,
where <code>t</code> is the value at the given valid index <code>index</code>,
<code>v</code> is the value at the top of the stack,
and <code>k</code> is the value just below the top.

<p>This function pops both the key and the value from the stack.
As in Lua, this function may trigger a metamethod
for the "newindex" event (see <a href="#metatable">2.8</a>).

<p><a name="lua_settop"></a>
<hr></hr><h3><code>lua_settop</code></h3>
<pre>
          void lua_settop (lua_State *L, int index);
</pre>


<p>Accepts any acceptable index, or 0,
and sets the stack top to this index.
If the new top is larger than the old one,
then the new elements are filled with <b>nil</b>.
If <code>index</code> is 0, then all stack elements are removed.

<p><a name="lua_State"></a>
<hr></hr><h3><code>lua_State</code></h3>
<pre>
          typedef struct lua_State lua_State;
</pre>


<p>Opaque structure that keeps the whole state of a Lua interpreter.
The Lua library is fully reentrant:
it has no global variables.
All information about a state is kept in this structure.

<p>A pointer to this state must be passed as the first argument to
every function in the library, except to <a href="#lua_newstate"><code>lua_newstate</code></a>,
which creates a Lua state from scratch.

<p><a name="lua_status"></a>
<hr></hr><h3><code>lua_status</code></h3>
<pre>
          int lua_status (lua_State *L);
</pre>


<p>Returns the status of the thread <code>L</code>.

<p>The status can be 0 for a normal thread,
an error code if the thread finished its execution with an error,
or <code>LUA_YIELD</code> if the thread is suspended.

<p><a name="lua_toboolean"></a>
<hr></hr><h3><code>lua_toboolean</code></h3>
<pre>
          int lua_toboolean (lua_State *L, int index);
</pre>


<p>Converts the Lua value at the given acceptable index to a C boolean
value (0 or 1).
Like all tests in Lua,
<a href="#lua_toboolean"><code>lua_toboolean</code></a> returns 1 for any Lua value
different from <b>false</b> and <b>nil</b>;
otherwise it returns 0.
It also returns 0 when called with a non-valid index.
(If you want to accept only actual boolean values,
use <a href="#lua_isboolean"><code>lua_isboolean</code></a> to test the value's type.)

<p><a name="lua_tocfunction"></a>
<hr></hr><h3><code>lua_tocfunction</code></h3>
<pre>
          lua_CFunction lua_tocfunction (lua_State *L, int index);
</pre>


<p>Converts a value at the given acceptable index to a C function.
That value must be a C function;
otherwise, returns <code>NULL</code>.

<p><a name="lua_tointeger"></a>
<hr></hr><h3><code>lua_tointeger</code></h3>
<pre>
          lua_Integer lua_tointeger (lua_State *L, int idx);
</pre>


<p>Converts the Lua value at the given acceptable index
to the signed integral type <a href="#lua_Integer"><code>lua_Integer</code></a>.
The Lua value must be a number or a string convertible to a number
(see <a href="#coercion">2.2.1</a>);
otherwise, <a href="#lua_tointeger"><code>lua_tointeger</code></a> returns 0.

<p>If the number is not an integer,
it is truncated in some non-specified way.

<p><a name="lua_tolstring"></a>
<hr></hr><h3><code>lua_tolstring</code></h3>
<pre>
          const char *lua_tolstring (lua_State *L, int index, size_t *len);
</pre>


<p>Converts the Lua value at the given acceptable index to a string
(<code>const char*</code>).
If <code>len</code> is not <code>NULL</code>,
it also sets <code>*len</code> with the string length.
The Lua value must be a string or a number;
otherwise, the function returns <code>NULL</code>.
If the value is a number,
then <a href="#lua_tolstring"><code>lua_tolstring</code></a> also
<em>changes the actual value in the stack to a string</em>.
(This change confuses <a href="#lua_next"><code>lua_next</code></a>
when <a href="#lua_tolstring"><code>lua_tolstring</code></a> is applied to keys during a table traversal.)

<p><a href="#lua_tolstring"><code>lua_tolstring</code></a> returns a fully aligned pointer
to a string inside the Lua state.
This string always has a zero (`<code>\0</code>&acute;)
after its last character (as in C),
but may contain other zeros in its body.
Because Lua has garbage collection,
there is no guarantee that the pointer returned by <a href="#lua_tolstring"><code>lua_tolstring</code></a>
will be valid after the corresponding value is removed from the stack.

<p><a name="lua_tonumber"></a>
<hr></hr><h3><code>lua_tonumber</code></h3>
<pre>
          lua_Number lua_tonumber (lua_State *L, int index);
</pre>


<p>Converts the Lua value at the given acceptable index
to a number (see <a href="#lua_Number"><code>lua_Number</code></a>).
The Lua value must be a number or a string convertible to a number
(see <a href="#coercion">2.2.1</a>);
otherwise, <a href="#lua_tonumber"><code>lua_tonumber</code></a> returns 0.

<p><a name="lua_topointer"></a>
<hr></hr><h3><code>lua_topointer</code></h3>
<pre>
          const void *lua_topointer (lua_State *L, int index);
</pre>


<p>Converts the value at the given acceptable index to a generic
C pointer (<code>void*</code>).
The value may be a userdata, a table, a thread, or a function;
otherwise, <a href="#lua_topointer"><code>lua_topointer</code></a> returns <code>NULL</code>.
Lua ensures that different objects return different pointers.
There is no direct way to convert the pointer back to its original value.

<p>Typically this function is used only for debug information.

<p><a name="lua_tostring"></a>
<hr></hr><h3><code>lua_tostring</code></h3>
<pre>
          const char *lua_tostring (lua_State *L, int index);
</pre>


<p>Equivalent to <a href="#lua_tolstring"><code>lua_tolstring</code></a> with <code>len</code> equal to <code>NULL</code>.

<p><a name="lua_tothread"></a>
<hr></hr><h3><code>lua_tothread</code></h3>
<pre>
          lua_State *lua_tothread (lua_State *L, int index);
</pre>


<p>Converts the value at the given acceptable index to a Lua thread
(represented as <code>lua_State*</code>).
This value must be a thread;
otherwise, the function returns <code>NULL</code>.

<p><a name="lua_touserdata"></a>
<hr></hr><h3><code>lua_touserdata</code></h3>
<pre>
          void *lua_touserdata (lua_State *L, int index);
</pre>


<p>If the value at the given acceptable index is a full userdata,
returns its block address.
If the value is a light userdata,
returns its pointer.
Otherwise, returns <code>NULL</code>.

<p><a name="lua_type"></a>
<hr></hr><h3><code>lua_type</code></h3>
<pre>
          int lua_type (lua_State *L, int index);
</pre>


<p>Returns the type of the value in the given acceptable index,
or <code>LUA_TNONE</code> for a non-valid index
(that is, an index to an "empty" stack position).
The types returned by <a href="#lua_type"><code>lua_type</code></a> are coded by the following constants
defined in <code>lua.h</code>:
<code>LUA_TNIL</code>,
<code>LUA_TNUMBER</code>,
<code>LUA_TBOOLEAN</code>,
<code>LUA_TSTRING</code>,
<code>LUA_TTABLE</code>,
<code>LUA_TFUNCTION</code>,
<code>LUA_TUSERDATA</code>,
<code>LUA_TTHREAD</code>,
and
<code>LUA_TLIGHTUSERDATA</code>.

<p><a name="lua_typename"></a>
<hr></hr><h3><code>lua_typename</code></h3>
<pre>
          const char *lua_typename  (lua_State *L, int tp);
</pre>


<p>Returns the name of the type encoded by the value <code>tp</code>,
which must be one the values returned by <a href="#lua_type"><code>lua_type</code></a>.

<p><a name="lua_Writer"></a>
<hr></hr><h3><code>lua_Writer</code></h3>
<pre>
          typedef int (*lua_Writer)
                          (lua_State *L, const void* p, size_t sz, void* ud);

</pre>


<p>The writer function used by <a href="#lua_dump"><code>lua_dump</code></a>.
Every time it produces another piece of chunk,
<a href="#lua_dump"><code>lua_dump</code></a> calls the writer,
passing along the buffer to be written (<code>p</code>),
its size (<code>sz</code>),
and the <code>data</code> parameter supplied to <a href="#lua_dump"><code>lua_dump</code></a>.

<p>The writer returns an error code:
0 means no errors;
any other value means an error and stops <a href="#lua_dump"><code>lua_dump</code></a> from
calling the writer again.

<p><a name="lua_xmove"></a>
<hr></hr><h3><code>lua_xmove</code></h3>
<pre>
          void lua_xmove (lua_State *from, lua_State *to, int n);
</pre>


<p>Exchange values between different threads of the <em>same</em> global state.

<p>This function pops <code>n</code> values from the stack <code>from</code>,
and pushes them onto the stack <code>to</code>.

<p><a name="lua_yield"></a>
<hr></hr><h3><code>lua_yield</code></h3>
<pre>
          int lua_yield  (lua_State *L, int nresults);
</pre>


<p>Yields a coroutine.

<p>This function should only be called as the
return expression of a C function, as follows:
<pre>
       return lua_yield (L, nresults);
</pre>
When a C function calls <a href="#lua_yield"><code>lua_yield</code></a> in that way,
the running coroutine suspends its execution,
and the call to <a href="#lua_resume"><code>lua_resume</code></a> that started this coroutine returns.
The parameter <code>nresults</code> is the number of values from the stack
that are passed as results to <a href="#lua_resume"><code>lua_resume</code></a>.

<p>
<a name="debugI"></a><a name="3.8"></a><h2>3.8 - The Debug Interface</h2>

<p>Lua has no built-in debugging facilities.
Instead, it offers a special interface
by means of functions and <em>hooks</em>.
This interface allows the construction of different
kinds of debuggers, profilers, and other tools
that need "inside information" from the interpreter.

<p><a name="lua_Debug"></a>
<hr></hr><h3><code>lua_Debug</code></h3>
<pre>
          typedef struct lua_Debug {
            int event;
            const char *name;           /* (n) */
            const char *namewhat;       /* (n) */
            const char *what;           /* (S) */
            const char *source;         /* (S) */
            int currentline;            /* (l) */
            int nups;                   /* (u) number of upvalues */
            int linedefined;            /* (S) */
            int lastlinedefined;        /* (S) */
            char short_src[LUA_IDSIZE]; /* (S) */
            /* private part */
            ...
          } lua_Debug;

</pre>


<p>A structure used to carry different pieces of
information about an active function.
<a href="#lua_getstack"><code>lua_getstack</code></a> fills only the private part
of this structure, for later use.
To fill the other fields of <a href="#lua_Debug"><code>lua_Debug</code></a> with useful information,
call <a href="#lua_getinfo"><code>lua_getinfo</code></a>.

<p>The fields of <a href="#lua_Debug"><code>lua_Debug</code></a> have the following meaning:
<ul>
<li><b><code>source</code></b> ---
If the function was defined in a string,
then <code>source</code> is that string.
If the function was defined in a file,
then <code>source</code> starts with a `<code>@</code>&acute; followed by the file name.

<p><li><b><code>short_src</code></b> ---
a "printable" version of <code>source</code>, to be used in error messages.

<p><li><b><code>linedefined</code></b> ---
the line number where the definition of the function starts.

<p><li><b><code>lastlinedefined</code></b> ---
the line number where the definition of the function ends.

<p><li><b><code>what</code></b> ---
the string <code>"Lua"</code> if the function is a Lua function,
<code>"C"</code> if it is a C function,
<code>"main"</code> if it is the main part of a chunk,
and <code>"tail"</code> if it was a function that did a tail call.
In the latter case,
Lua has no other information about the function.

<p><li><b><code>currentline</code></b> ---
the current line where the given function is executing.
When no line information is available,
<code>currentline</code> is set to <em>-1</em>.

<p><li><b><code>name</code></b> ---
a reasonable name for the given function.
Because functions in Lua are first-class values,
they do not have a fixed name:
Some functions may be the value of multiple global variables,
while others may be stored only in a table field.
The <code>lua_getinfo</code> function checks how the function was
called to find a suitable name.
If it cannot find a name,
then <code>name</code> is set to <code>NULL</code>.

<p><li><b><code>namewhat</code></b> ---
explains the <code>name</code> field.
The value of <code>namewhat</code> can be
<code>"global"</code>, <code>"local"</code>, <code>"method"</code>,
<code>"field"</code>, <code>"upvalue"</code>, or <code>""</code> (the empty string),
according to how the function was called.
(Lua uses the empty string when no other option seems to apply.)

<p><li><b><code>nups</code></b> ---
the number of upvalues of the function.

<p></ul>

<p><a name="lua_gethook"></a>
<hr></hr><h3><code>lua_gethook</code></h3>
<pre>
          lua_Hook lua_gethook (lua_State *L);
</pre>


<p>Returns the current hook function.

<p><a name="lua_gethookcount"></a>
<hr></hr><h3><code>lua_gethookcount</code></h3>
<pre>
          int lua_gethookcount (lua_State *L);
</pre>


<p>Returns the current hook count.

<p><a name="lua_gethookmask"></a>
<hr></hr><h3><code>lua_gethookmask</code></h3>
<pre>
          int lua_gethookmask (lua_State *L);
</pre>


<p>Returns the current hook mask.

<p><a name="lua_getinfo"></a>
<hr></hr><h3><code>lua_getinfo</code></h3>
<pre>
          int lua_getinfo (lua_State *L, const char *what, lua_Debug *ar);
</pre>


<p>Fills the fields of <a href="#lua_Debug"><code>lua_Debug</code></a> with useful information.

<p>This function returns 0 on error
(for instance, an invalid option in <code>what</code>).
Each character in the string <code>what</code>
selects some fields of the structure <code>ar</code> to be filled,
as indicated by the letter in parentheses in the definition of <a href="#lua_Debug"><code>lua_Debug</code></a>:
`<code>S</code>&acute; fills in the fields <code>source</code>, <code>linedefined</code>,
<code>lastlinedefined</code>,
and <code>what</code>;
`<code>l</code>&acute; fills in the field <code>currentline</code>, etc.
Moreover, `<code>f</code>&acute; pushes onto the stack the function that is
running at the given level.

<p>To get information about a function that is not active
(that is, not in the stack),
you push it onto the stack
and start the <code>what</code> string with the character `<code>></code>&acute;.
For instance, to know in which line a function <code>f</code> was defined,
you can write the following code:
<pre>
       lua_Debug ar;
       lua_getfield(L, LUA_GLOBALSINDEX, "f");  /* get global `f' */
       lua_getinfo(L, ">S", &#038;ar);
       printf("%d\n", ar.linedefined);
</pre>

<p><a name="lua_getlocal"></a>
<hr></hr><h3><code>lua_getlocal</code></h3>
<pre>
          const char *lua_getlocal (lua_State *L, const lua_Debug *ar, int n);
</pre>


<p>Gets information about a local variable of a given activation record.
The parameter <code>ar</code> must be a valid activation record that was
filled by a previous call to <a href="#lua_getstack"><code>lua_getstack</code></a> or
given as argument to a hook (see <a href="#lua_Hook"><code>lua_Hook</code></a>).
The index <code>n</code> selects which local variable to inspect
(1 is the first parameter or active local variable, and so on,
until the last active local variable).
<a href="#lua_getlocal"><code>lua_getlocal</code></a> pushes the variable's value onto the stack
and returns its name.

<p>Variable names starting with `<code>(</code>&acute; (open parentheses)
represent internal variables
(loop control variables, temporaries, and C function locals).

<p>Returns <code>NULL</code> (and pushes nothing)
when the index is greater than
the number of active local variables.

<p><a name="lua_getstack"></a>
<hr></hr><h3><code>lua_getstack</code></h3>
<pre>
          int lua_getstack (lua_State *L, int level, lua_Debug *ar);
</pre>


<p>Get information about the interpreter runtime stack.

<p>This function fills parts of a <a href="#lua_Debug"><code>lua_Debug</code></a> structure with
an identification of the <em>activation record</em>
of the function executing at a given level.
Level 0 is the current running function,
whereas level <em>n+1</em> is the function that has called level <em>n</em>.
When there are no errors, <a href="#lua_getstack"><code>lua_getstack</code></a> returns 1;
when called with a level greater than the stack depth,
it returns 0.

<p><a name="lua_getupvalue"></a>
<hr></hr><h3><code>lua_getupvalue</code></h3>
<pre>
          const char *lua_getupvalue (lua_State *L, int funcindex, int n);
</pre>


<p>Gets information about a closure's upvalue.
(For Lua functions,
upvalues are the external local variables that the function uses,
and that are consequently included in its closure.)
<a href="#lua_getupvalue"><code>lua_getupvalue</code></a> gets the index <code>n</code> of an upvalue,
pushes the upvalue's value onto the stack,
and returns its name.
<code>funcindex</code> points to the closure in the stack.
(Upvalues have no particular order,
as they are active through the whole function.
So, they are numbered in an arbitrary order.)

<p>Returns <code>NULL</code> (and pushes nothing)
when the index is greater than the number of upvalues.
For C functions, this function uses the empty string <code>""</code>
as a name for all upvalues.

<p><a name="lua_Hook"></a>
<hr></hr><h3><code>lua_Hook</code></h3>
<pre>
          typedef void (*lua_Hook) (lua_State *L, lua_Debug *ar);
</pre>


<p>Type for debugging hook functions.

<p>Whenever a hook is called, its <code>ar</code> argument has its field
<code>event</code> set to the specific event that triggered the hook.
Lua identifies these events with the following constants:
<code>LUA_HOOKCALL</code>, <code>LUA_HOOKRET</code>,
<code>LUA_HOOKTAILRET</code>, <code>LUA_HOOKLINE</code>,
and <code>LUA_HOOKCOUNT</code>.
Moreover, for line events, the field <code>currentline</code> is also set.
To get the value of any other field in <code>ar</code>,
the hook must call <a href="#lua_getinfo"><code>lua_getinfo</code></a>.
For return events, <code>event</code> may be <code>LUA_HOOKRET</code>,
the normal value, or <code>LUA_HOOKTAILRET</code>.
In the latter case, Lua is simulating a return from
a function that did a tail call;
in this case, it is useless to call <a href="#lua_getinfo"><code>lua_getinfo</code></a>.

<p>While Lua is running a hook, it disables other calls to hooks.
Therefore, if a hook calls back Lua to execute a function or a chunk,
this execution occurs without any calls to hooks.

<p><a name="lua_sethook"></a>
<hr></hr><h3><code>lua_sethook</code></h3>
<pre>
          int lua_sethook (lua_State *L, lua_Hook func, int mask, int count);
</pre>


<p>Sets the debugging hook function.

<p><code>func</code> is the hook function.
<code>mask</code> specifies on which events the hook will be called:
It is formed by a bitwise or of the constants
<code>LUA_MASKCALL</code>,
<code>LUA_MASKRET</code>,
<code>LUA_MASKLINE</code>,
and <code>LUA_MASKCOUNT</code>.
The <code>count</code> argument is only meaningful when the mask
includes <code>LUA_MASKCOUNT</code>.
For each event, the hook is called as explained below:
<ul>
<li><b>The call hook</b> is called when the interpreter calls a function.
The hook is called just after Lua enters the new function,
before the function gets its arguments.
<li><b>The return hook</b> is called when the interpreter returns from a function.
The hook is called just before Lua leaves the function.
You have no access to the values to be returned by the function.
<li><b>The line hook</b> is called when the interpreter is about to
start the execution of a new line of code,
or when it jumps back in the code (even to the same line).
(This event only happens while Lua is executing a Lua function.)
<li><b>The count hook</b> is called after the interpreter executes every
<code>count</code> instructions.
(This event only happens while Lua is executing a Lua function.)
</ul>

<p>A hook is disabled by setting <code>mask</code> to zero.

<p><a name="lua_setlocal"></a>
<hr></hr><h3><code>lua_setlocal</code></h3>
<pre>
          const char *lua_setlocal (lua_State *L, const lua_Debug *ar, int n);
</pre>


<p>Sets the value of a local variable of a given activation record.
Parameters <code>ar</code> and <code>n</code> are as in <a href="#lua_getlocal"><code>lua_getlocal</code></a>
(see <a href="#lua_getlocal"><code>lua_getlocal</code></a>).
<a href="#lua_setlocal"><code>lua_setlocal</code></a> assigns the value at the top of the stack
to the variable and returns its name.
It also pops the value from the stack.

<p>Returns <code>NULL</code> (and pops nothing)
when the index is greater than
the number of active local variables.

<p><a name="lua_setupvalue"></a>
<hr></hr><h3><code>lua_setupvalue</code></h3>
<pre>
          const char *lua_setupvalue (lua_State *L, int funcindex, int n);
</pre>


<p>Sets the value of a closure's upvalue.
Parameters <code>funcindex</code> and <code>n</code> are as in <a href="#lua_getupvalue"><code>lua_getupvalue</code></a>
(see <a href="#lua_getupvalue"><code>lua_getupvalue</code></a>).
It assigns the value at the top of the stack
to the upvalue and returns its name.
It also pops the value from the stack.

<p>Returns <code>NULL</code> (and pops nothing)
when the index is greater than the number of upvalues.

<p>
<a name="4"></a><h1>4 - The Auxiliary Library</h1>

<p>
The <em>auxiliary library</em> provides several convenient functions
to interface C with Lua.
While the basic API provides the primitive functions for all 
interactions between C and Lua,
the auxiliary library provides higher-level functions for some
common tasks.

<p>All functions from the auxiliary library
are defined in header file <code>lauxlib.h</code> and
have a prefix <code>luaL_</code>.

<p>All functions in the auxiliary library are built on
top of the basic API,
and so they provide nothing that cannot be done with this API.

<p>Several functions in the auxiliary library are used to
check C function arguments.
Their names are always <code>luaL_check*</code> or <code>luaL_opt*</code>.
All of these functions raise an error if the check is not satisfied.
Because the error message is formatted for arguments
(e.g., <code>"bad argument #1"</code>),
you should not use these functions for other stack values.

<p><a name="4.1"></a><h2>4.1 - Functions and Types</h2>

<p>Here we list all functions and types from the auxiliary library
in alphabetical order.

<p><a name="luaL_addchar"></a>
<hr></hr><h3><code>luaL_addchar</code></h3>
<pre>
          void luaL_addchar (luaL_Buffer B, char c);
</pre>


<p>Adds the character <code>c</code> to the buffer <code>B</code>
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).

<p><a name="luaL_addlstring"></a>
<hr></hr><h3><code>luaL_addlstring</code></h3>
<pre>
          void luaL_addlstring (luaL_Buffer *B, const char *s, size_t l);
</pre>


<p>Adds the string pointed to by <code>s</code> with length <code>l</code> to
the buffer <code>B</code>
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).
The string may contain embedded zeros.

<p><a name="luaL_addsize"></a>
<hr></hr><h3><code>luaL_addsize</code></h3>
<pre>
          void luaL_addsize (luaL_Buffer B, size_t n);
</pre>


<p>Adds a string of length <code>n</code> previously copied to the
buffer area (see <a href="#luaL_prepbuffer"><code>luaL_prepbuffer</code></a>) to the buffer <code>B</code>
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).

<p><a name="luaL_addstring"></a>
<hr></hr><h3><code>luaL_addstring</code></h3>
<pre>
          void luaL_addstring (luaL_Buffer *B, const char *s);
</pre>


<p>Adds the zero-terminated string pointed to by <code>s</code>
to the buffer <code>B</code>
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).
The string may not contain embedded zeros.

<p><a name="luaL_addvalue"></a>
<hr></hr><h3><code>luaL_addvalue</code></h3>
<pre>
          void luaL_addvalue (luaL_Buffer *B);
</pre>


<p>Adds the value at the top of the stack
to the buffer <code>B</code>
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).
Pops the value.

<p>This is the only function on string buffers that can (and must)
be called with an extra element on the stack,
which is the value to be added to the buffer.

<p><a name="luaL_argcheck"></a>
<hr></hr><h3><code>luaL_argcheck</code></h3>
<pre>
          void luaL_argcheck (lua_State *L, int cond, int numarg,
                              const char *extramsg);
</pre>


<p>Checks whether <code>cond</code> is true.
If not, raises an error with message
<code>"bad argument #&#060;numarg> to &#060;func> (&#060;extramsg>)"</code>,
where <code>func</code> is retrieved from the call stack.

<p><a name="luaL_argerror"></a>
<hr></hr><h3><code>luaL_argerror</code></h3>
<pre>
          int luaL_argerror (lua_State *L, int numarg, const char *extramsg);
</pre>


<p>Raises an error with message
<code>"bad argument #&#060;numarg> to &#060;func> (&#060;extramsg>)"</code>,
where <code>func</code> is retrieved from the call stack.

<p>This function never returns,
but it is an idiom to use it as <code>return luaL_argerror ...</code>
in C functions.

<p><a name="luaL_Buffer"></a>
<hr></hr><h3><code>luaL_Buffer</code></h3>
<pre>
          typedef struct luaL_Buffer luaL_Buffer;
</pre>


<p>Type for a <em>string buffer</em>.

<p>A string buffer allows C code to build Lua strings piecemeal.
Its pattern of use is as follows:
<ul>
<li> First you declare a variable <code>b</code> of type <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>.
<li> Then you initialize it with a call <code>luaL_buffinit(L, &#038;b)</code>.
<li> Then you add string pieces to the buffer calling any of
the <code>luaL_add*</code> functions.
<li> You finish by calling <code>luaL_pushresult(&#038;b)</code>.
This call leaves the final string on the top of the stack.
</ul>

<p>During its normal operation,
a string buffer uses a variable number of stack slots.
So, while using a buffer, you cannot assume that you know where
the top of the stack is.
You can use the stack between successive calls to buffer operations
as long as that use is balanced;
that is,
when you call a buffer operation,
the stack is at the same level
it was immediately after the previous buffer operation.
(The only exception to this rule is <a href="#luaL_addvalue"><code>luaL_addvalue</code></a>.)
After calling <a href="#luaL_pushresult"><code>luaL_pushresult</code></a> the stack is back to its
level when the buffer was initialized,
plus the final string on its top.

<p><a name="luaL_buffinit"></a>
<hr></hr><h3><code>luaL_buffinit</code></h3>
<pre>
          void luaL_buffinit (lua_State *L, luaL_Buffer *B);
</pre>


<p>Initializes a buffer <code>B</code>.
This function does not allocate any space;
the buffer must be declared as a variable
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).

<p><a name="luaL_callmeta"></a>
<hr></hr><h3><code>luaL_callmeta</code></h3>
<pre>
          int luaL_callmeta (lua_State *L, int obj, const char *e);
</pre>


<p>Calls a metamethod.

<p>If the object at index <code>obj</code> has a metatable and this
metatable has a field <code>e</code>,
this function calls this field and passes the object as its only argument.
In this case this function returns 1 and pushes on the
stack the value returned by the call.
If there is no metatable or no metamethod,
this function returns 0 (without pushing any value on the stack).

<p><a name="luaL_checkany"></a>
<hr></hr><h3><code>luaL_checkany</code></h3>
<pre>
          void luaL_checkany (lua_State *L, int narg);
</pre>


<p>Checks whether the function has an argument
of any type (including <b>nil</b>) at position <code>narg</code>.

<p><a name="luaL_checkint"></a>
<hr></hr><h3><code>luaL_checkint</code></h3>
<pre>
          int luaL_checkint (lua_State *L, int narg);
</pre>


<p>Checks whether the function argument <code>narg</code> is a number
and returns this number cast to an <code>int</code>.

<p><a name="luaL_checkinteger"></a>
<hr></hr><h3><code>luaL_checkinteger</code></h3>
<pre>
          lua_Integer luaL_checkinteger (lua_State *L, int narg);
</pre>


<p>Checks whether the function argument <code>narg</code> is a number
and returns this number cast to a <a href="#lua_Integer"><code>lua_Integer</code></a>.

<p><a name="luaL_checklong"></a>
<hr></hr><h3><code>luaL_checklong</code></h3>
<pre>
          long luaL_checklong (lua_State *L, int narg);
</pre>


<p>Checks whether the function argument <code>narg</code> is a number
and returns this number cast to a <code>long</code>.

<p><a name="luaL_checklstring"></a>
<hr></hr><h3><code>luaL_checklstring</code></h3>
<pre>
          const char *luaL_checklstring (lua_State *L, int narg, size_t *l);
</pre>


<p>Checks whether the function argument <code>narg</code> is a string
and returns this string;
if <code>l</code> is not <code>NULL</code> fills <code>*l</code>
with the string's length.

<p><a name="luaL_checknumber"></a>
<hr></hr><h3><code>luaL_checknumber</code></h3>
<pre>
          lua_Number luaL_checknumber (lua_State *L, int narg);
</pre>


<p>Checks whether the function argument <code>narg</code> is a number
and returns this number.

<p><a name="luaL_checkoption"></a>
<hr></hr><h3><code>luaL_checkoption</code></h3>
<pre>
          int luaL_checkoption (lua_State *L, int narg, const char *def,
                                const char *const lst[]);
</pre>


<p>Checks whether the function argument <code>narg</code> is a string and
searches for this string in the array <code>lst</code>
(which must be NULL-terminated).
If <code>def</code> is not <code>NULL</code>,
uses <code>def</code> as a default value when
the function has no argument <code>narg</code> or if this argument is <b>nil</b>.

<p>Returns the index in the array where the string was found.
Raises an error if the argument is not a string or
if the string cannot be found.

<p>This is a useful function for mapping strings to C enums.
The usual convention in Lua libraries is to use strings instead of numbers
to select options.

<p><a name="luaL_checkstack"></a>
<hr></hr><h3><code>luaL_checkstack</code></h3>
<pre>
          void luaL_checkstack (lua_State *L, int sz, const char *msg);
</pre>


<p>Grows the stack size to <code>top + sz</code> elements,
raising an error if the stack cannot grow to that size.
<code>msg</code> is an additional text to go into the error message.

<p><a name="luaL_checkstring"></a>
<hr></hr><h3><code>luaL_checkstring</code></h3>
<pre>
          const char *luaL_checkstring (lua_State *L, int narg);
</pre>


<p>Checks whether the function argument <code>narg</code> is a string
and returns this string.

<p><a name="luaL_checktype"></a>
<hr></hr><h3><code>luaL_checktype</code></h3>
<pre>
          void luaL_checktype (lua_State *L, int narg, int t);
</pre>


<p>Checks whether the function argument <code>narg</code> has type <code>t</code>.

<p><a name="luaL_checkudata"></a>
<hr></hr><h3><code>luaL_checkudata</code></h3>
<pre>
          void *luaL_checkudata (lua_State *L, int narg, const char *tname);
</pre>


<p>Checks whether the function argument <code>narg</code> is a userdata
of the type <code>tname</code> (see <a href="#luaL_newmetatable"><code>luaL_newmetatable</code></a>).

<p><a name="luaL_error"></a>
<hr></hr><h3><code>luaL_error</code></h3>
<pre>
          int luaL_error (lua_State *L, const char *fmt, ...);
</pre>


<p>Raises an error.
The error message format is given by <code>fmt</code>
plus any extra arguments,
following the same rules of <a href="#lua_pushfstring"><code>lua_pushfstring</code></a>.
It also adds at the beginning of the message the file name and
the line number where the error occurred,
if this information is available.

<p>This function never returns,
but it is an idiom to use it as <code>return luaL_error ...</code>
in C functions.

<p><a name="luaL_getmetafield"></a>
<hr></hr><h3><code>luaL_getmetafield</code></h3>
<pre>
          int luaL_getmetafield (lua_State *L, int obj, const char *e);
</pre>


<p>Pushes on the stack the field <code>e</code> from the metatable
of the object at index <code>obj</code>.
If the object does not have a metatable,
or if the metatable does not have this field,
returns 0 and pushes nothing.

<p><a name="luaL_getmetatable"></a>
<hr></hr><h3><code>luaL_getmetatable</code></h3>
<pre>
          void luaL_getmetatable (lua_State *L, const char *tname);
</pre>


<p>Pushes on the stack the metatable associated with name <code>tname</code>
in the registry (see <a href="#luaL_newmetatable"><code>luaL_newmetatable</code></a>).

<p><a name="luaL_gsub"></a>
<hr></hr><h3><code>luaL_gsub</code></h3>
<pre>
          const char *luaL_gsub (lua_State *L, const char *s,
                                 const char *p, const char *r);
</pre>


<p>Creates a copy of string <code>s</code> by replacing
any occurrence of the string <code>p</code>
with the string <code>r</code>.
Pushes the resulting string on the stack and returns it.

<p><a name="luaL_loadbuffer"></a>
<hr></hr><h3><code>luaL_loadbuffer</code></h3>
<pre>
          int luaL_loadbuffer (lua_State *L, const char *buff,
                               size_t sz, const char *name);
</pre>


<p>Loads a buffer as a Lua chunk.
This function uses <a href="#lua_load"><code>lua_load</code></a> to load the chunk in the
buffer pointed to by <code>buff</code> with size <code>sz</code>.

<p>This function returns the same results as <a href="#lua_load"><code>lua_load</code></a>.
<code>name</code> is the chunk name,
used for debug information and error messages.

<p><a name="luaL_loadfile"></a>
<hr></hr><h3><code>luaL_loadfile</code></h3>
<pre>
          int luaL_loadfile (lua_State *L, const char *filename);
</pre>


<p>Loads a file as a Lua chunk.
This function uses <a href="#lua_load"><code>lua_load</code></a> to load the chunk in the file
named <code>filename</code>.
If <code>filename</code> is <code>NULL</code>,
then it loads from the standard input.
The first line in the file is ignored if it starts with a <code>#</code>.

<p>This function returns the same results as <a href="#lua_load"><code>lua_load</code></a>,
but it has an extra error code <code>LUA_ERRFILE</code>
if it cannot open/read the file.

<p><a name="luaL_loadstring"></a>
<hr></hr><h3><code>luaL_loadstring</code></h3>
<pre>
          int luaL_loadstring (lua_State *L, const char *s);
</pre>


<p>Loads a string as a Lua chunk.
This function uses <a href="#lua_load"><code>lua_load</code></a> to load the chunk in
the zero-terminated string <code>s</code>.

<p>This function returns the same results as <a href="#lua_load"><code>lua_load</code></a>.

<p><a name="luaL_newmetatable"></a>
<hr></hr><h3><code>luaL_newmetatable</code></h3>
<pre>
          int luaL_newmetatable (lua_State *L, const char *tname);
</pre>


<p>If the registry already has the key <code>tname</code>,
returns 0.
Otherwise,
creates a new table to be used as a metatable for userdata,
adds it to the registry with key <code>tname</code>,
and returns 1.

<p>In both cases pushes on the stack the final value associated
with <code>tname</code> in the registry.

<p><a name="luaL_newstate"></a>
<hr></hr><h3><code>luaL_newstate</code></h3>
<pre>
          lua_State *luaL_newstate (void);
</pre>


<p>Creates a new Lua state, calling <a href="#lua_newstate"><code>lua_newstate</code></a> with an
allocation function based on the standard C <code>realloc</code> function
and setting a panic function (see <a href="#lua_atpanic"><code>lua_atpanic</code></a>) that prints
an error message to the standard error output in case of fatal
errors.

<p>Returns the new state,
or <code>NULL</code> if there is a memory allocation error.

<p><a name="luaL_openlibs"></a>
<hr></hr><h3><code>luaL_openlibs</code></h3>
<pre>
          void luaL_openlibs (lua_State *L);
</pre>


<p>Opens all standard Lua libraries into the given state.

<p><a name="luaL_optint"></a>
<hr></hr><h3><code>luaL_optint</code></h3>
<pre>
          int luaL_optint (lua_State *L, int narg, int d);
</pre>


<p>If the function argument <code>narg</code> is a number,
returns this number cast to an <code>int</code>.
If this argument is absent or is <b>nil</b>,
returns <code>d</code>.
Otherwise, raises an error.

<p><a name="luaL_optinteger"></a>
<hr></hr><h3><code>luaL_optinteger</code></h3>
<pre>
          lua_Integer luaL_optinteger (lua_State *L, int narg, lua_Integer d);
</pre>


<p>If the function argument <code>narg</code> is a number,
returns this number cast to a <a href="#lua_Integer"><code>lua_Integer</code></a>.
If this argument is absent or is <b>nil</b>,
returns <code>d</code>.
Otherwise, raises an error.

<p><a name="luaL_optlong"></a>
<hr></hr><h3><code>luaL_optlong</code></h3>
<pre>
          long luaL_optlong (lua_State *L, int narg, long d);
</pre>


<p>If the function argument <code>narg</code> is a number,
returns this number cast to a <code>long</code>.
If this argument is absent or is <b>nil</b>,
returns <code>d</code>.
Otherwise, raises an error.

<p><a name="luaL_optlstring"></a>
<hr></hr><h3><code>luaL_optlstring</code></h3>
<pre>
          const char *luaL_optlstring (lua_State *L, int narg,
                                       const char *d, size_t *l);
</pre>


<p>If the function argument <code>narg</code> is a string,
returns this string.
If this argument is absent or is <b>nil</b>,
returns <code>d</code>.
Otherwise, raises an error.

<p>If <code>l</code> is not <code>NULL</code>,
fills the position <code>*l</code> with the results's length.

<p><a name="luaL_optnumber"></a>
<hr></hr><h3><code>luaL_optnumber</code></h3>
<pre>
          lua_Number luaL_optnumber (lua_State *L, int narg, lua_Number d);
</pre>


<p>If the function argument <code>narg</code> is a number,
returns this number.
If this argument is absent or is <b>nil</b>,
returns <code>d</code>.
Otherwise, raises an error.

<p><a name="luaL_optstring"></a>
<hr></hr><h3><code>luaL_optstring</code></h3>
<pre>
          const char *luaL_optstring (lua_State *L, int narg, const char *d);
</pre>


<p>If the function argument <code>narg</code> is a string,
returns this string.
If this argument is absent or is <b>nil</b>,
returns <code>d</code>.
Otherwise, raises an error.

<p><a name="luaL_prepbuffer"></a>
<hr></hr><h3><code>luaL_prepbuffer</code></h3>
<pre>
          char *luaL_prepbuffer (luaL_Buffer *B);
</pre>


<p>Returns an address to a space of size <code>LUAL_BUFFERSIZE</code>
where you can copy a string to be added to buffer <code>B</code>
(see <a href="#luaL_Buffer"><code>luaL_Buffer</code></a>).
After copying the string into this space you must call
<a href="#luaL_addsize"><code>luaL_addsize</code></a> with the size of the string to actually add 
it to the buffer.

<p><a name="luaL_pushresult"></a>
<hr></hr><h3><code>luaL_pushresult</code></h3>
<pre>
          void luaL_pushresult (luaL_Buffer *B);
</pre>


<p>Finishes the use of buffer <code>B</code> leaving the final string on
the top of the stack.

<p><a name="luaL_ref"></a>
<hr></hr><h3><code>luaL_ref</code></h3>
<pre>
          int luaL_ref (lua_State *L, int t);
</pre>


<p>Creates and returns a <em>reference</em>,
in the table at index <code>t</code>,
for the object at the top of the stack (and pops the object).

<p>A reference is a unique integer key.
As long as you do not manually add integer keys into table <code>t</code>,
<a href="#luaL_ref"><code>luaL_ref</code></a> ensures the uniqueness of the key it returns.
You can retrieve an object referred by reference <code>r</code>
by calling <code>lua_rawgeti(L, t, r)</code>.
Function <a href="#luaL_unref"><code>luaL_unref</code></a> frees a reference and its associated object.

<p>If the object at the top of the stack is <b>nil</b>,
<a href="#luaL_ref"><code>luaL_ref</code></a> returns the constant <code>LUA_REFNIL</code>.
The constant <code>LUA_NOREF</code> is guaranteed to be different
from any reference returned by <a href="#luaL_ref"><code>luaL_ref</code></a>.

<p><a name="luaL_Reg"></a>
<hr></hr><h3><code>luaL_Reg</code></h3>
<pre>
          typedef struct luaL_Reg {
            const char *name;
            lua_CFunction func;
          } luaL_Reg;

</pre>


<p>Type for arrays of functions to be registered by
<a href="#luaL_register"><code>luaL_register</code></a>.
<code>name</code> is the function name and <code>func</code> is a pointer to
the function.
Any array of <a href="#luaL_Reg"><code>luaL_Reg</code></a> must end with an sentinel entry
in which both <code>name</code> and <code>func</code> are <code>NULL</code>.

<p><a name="luaL_register"></a>
<hr></hr><h3><code>luaL_register</code></h3>
<pre>
          void luaL_register (lua_State *L, const char *libname,
                              const luaL_Reg *l);
</pre>


<p>Opens a library.

<p>When called with <code>libname</code> equal to <code>NULL</code>,
simply registers all functions in the list <code>l</code>
(see <a href="#luaL_Reg"><code>luaL_Reg</code></a>) into the table on the top of the stack.

<p>When called with a non-null <code>libname</code>,
creates a new table <code>t</code>,
sets it as the value of the global variable <code>libname</code>,
sets it as the value of <code>package.loaded[libname]</code>,
and registers on it all functions in the list <code>l</code>.
If there is a table in <code>package.loaded[libname]</code> or in
variable <code>libname</code>,
reuses this table instead of creating a new one.

<p>In any case the function leaves the table
on the top of the stack.

<p><a name="luaL_typename"></a>
<hr></hr><h3><code>luaL_typename</code></h3>
<pre>
          const char *luaL_typename (lua_State *L, int idx);
</pre>


<p>Returns the name of the type of the value at index <code>idx</code>.

<p><a name="luaL_typerror"></a>
<hr></hr><h3><code>luaL_typerror</code></h3>
<pre>
          int luaL_typerror (lua_State *L, int narg, const char *tname);
</pre>


<p>Generates an error with a message like
<pre>
&#060;location>: bad argument &#060;narg> to &#060;function> (&#060;tname> expected, got &#060;realt>)
</pre>
where <code>&#060;location></code> is produced by <a href="#luaL_where"><code>luaL_where</code></a>,
<code>&#060;function></code> is the name of the current function,
and <code>&#060;realt></code> is the type name of the actual argument.

<p><a name="luaL_unref"></a>
<hr></hr><h3><code>luaL_unref</code></h3>
<pre>
          void luaL_unref (lua_State *L, int t, int ref);
</pre>


<p>Releases reference <code>ref</code> from the table at index <code>t</code>
(see <a href="#luaL_ref"><code>luaL_ref</code></a>).
The entry is removed from the table,
so that the referred object can be collected.
The reference <code>ref</code> is also freed to be used again.

<p>If <code>ref</code> is <code>LUA_NOREF</code> or <code>LUA_REFNIL</code>,
<a href="#luaL_unref"><code>luaL_unref</code></a> does nothing.

<p><a name="luaL_where"></a>
<hr></hr><h3><code>luaL_where</code></h3>
<pre>
          void luaL_where (lua_State *L, int lvl);
</pre>


<p>Pushes on the stack a string identifying the current position
of the control at level <code>lvl</code> in the call stack.
Typically this string has the format <code>&#060;chunkname>:&#060;currentline>:</code>.
Level 0 is the running function,
level 1 is the function that called the running function,
etc.

<p>This function is used to build a prefix for error messages.

<p>
<a name="libraries"></a><a name="5"></a><h1>5 - Standard Libraries</h1>

<p>The standard Lua libraries provide useful functions
that are implemented directly through the C API.
Some of these functions provide essential services to the language
(e.g., <a href="#pdf-type"><code>type</code></a> and <a href="#pdf-getmetatable"><code>getmetatable</code></a>);
others provide access to "outside" services (e.g., I/O);
and others could be implemented in Lua itself,
but are quite useful or have critical performance requirements that
deserve an implementation in C (e.g., <code>sort</code>).

<p>All libraries are implemented through the official C API
and are provided as separate C modules.
Currently, Lua has the following standard libraries:
<ul>
<li> basic library;
<li> package library;
<li> string manipulation;
<li> table manipulation;
<li> mathematical functions (sin, log, etc.);
<li> input and output;
<li> operating system facilities;
<li> debug facilities.
</ul>
Except for the basic and package libraries,
each library provides all its functions as fields of a global table
or as methods of its objects.

<p>To have access to these libraries,
the C host program must call
<code>luaL_openlibs</code>,
which open all standard libraries.
Alternatively,
it can open them individually by calling
<code>luaopen_base</code> (for the basic library),
<code>luaopen_package</code> (for the package library),
<code>luaopen_string</code> (for the string library),
<code>luaopen_table</code> (for the table library),
<code>luaopen_math</code> (for the mathematical library),
<code>luaopen_io</code> (for the I/O and the Operating System libraries),
and <code>luaopen_debug</code> (for the debug library).
These functions are declared in <code>lualib.h</code>
and should not be called directly:
you must call them like any other Lua C function,
e.g., by using <code>lua_call</code>.

<p><a name="predefined"></a><a name="5.1"></a><h2>5.1 - Basic Functions</h2>

<p>The basic library provides some core functions to Lua.
If you do not include this library in your application,
you should check carefully whether you need to provide 
implementations for some of its facilities.

<p><a name="pdf-assert"></a><hr></hr><h3><code>assert (v [, message])</code></h3>
Issues an  error when
the value of its argument <code>v</code> is false (i.e., <b>nil</b> or <b>false</b>);
otherwise, returns all its arguments.
<code>message</code> is an error message;
when absent, it defaults to "assertion failed!"

<p><a name="pdf-collectgarbage"></a><hr></hr><h3><code>collectgarbage (opt [, arg])</code></h3>

<p>This function is a generic interface to the garbage collector.
It performs different functions according to its first argument, <code>opt</code>:
<ul>
<li><b>"stop"</b> --- stops the garbage collector.
<li><b>"restart"</b> --- restarts the garbage collector.
<li><b>"collect"</b> --- performs a full garbage-collection cycle.
<li><b>"count"</b> --- returns the total memory in use by Lua (in Kbytes).
<li><b>"step"</b> --- performs a garbage-collection step.
The step "size" is controlled by <code>arg</code>
(larger values mean more steps) in a non-specified way.
If you want to control the step size
you must tune experimentally the value of <code>arg</code>.
Returns <b>true</b> if the step finished a collection cycle.
<li><b>"steppause"</b> ---
sets <code>arg</code>/100 as the new value for the <em>pause</em> of
the collector (see <a href="#GC">2.10</a>).
<li><b>"setstepmul"</b> ---
sets <code>arg</code>/100 as the new value for the <em>step multiplier</em> of
the collector (see <a href="#GC">2.10</a>).
</ul>

<p><a name="pdf-dofile"></a><hr></hr><h3><code>dofile (filename)</code></h3>
Opens the named file and executes its contents as a Lua chunk.
When called without arguments,
<code>dofile</code> executes the contents of the standard input (<code>stdin</code>).
Returns all values returned by the chunk.
In case of errors, <code>dofile</code> propagates the error
to its caller (that is, <code>dofile</code> does not run in protected mode).

<p><a name="pdf-error"></a><hr></hr><h3><code>error (message [, level])</code></h3>
Terminates the last protected function called
and returns <code>message</code> as the error message.
Function <code>error</code> never returns.

<p>Usually, <code>error</code> adds some information about the error position
at the beginning of the message.
The <code>level</code> argument specifies how to get the error position.
With level 1 (the default), the error position is where the
<code>error</code> function was called.
Level 2 points the error to where the function
that called <code>error</code> was called; and so on.
Passing a level 0 avoids the addition of error position information
to the message.

<p><a name="pdf-_G"></a><hr></hr><h3><code>_G</code></h3>
A global variable (not a function) that
holds the global environment (that is, <code>_G._G = _G</code>).
Lua itself does not use this variable;
changing its value does not affect any environment,
nor vice-versa.
(Use <a href="#pdf-setfenv"><code>setfenv</code></a> to change environments.)

<p><a name="pdf-getfenv"></a><hr></hr><h3><code>getfenv (f)</code></h3>
Returns the current environment in use by the function.
<code>f</code> can be a Lua function or a number
that specifies the function at that stack level:
Level 1 is the function calling <code>getfenv</code>.
If the given function is not a Lua function,
or if <code>f</code> is 0,
<code>getfenv</code> returns the global environment.
The default for <code>f</code> is 1.

<p><a name="pdf-getmetatable"></a><hr></hr><h3><code>getmetatable (object)</code></h3>

<p>If <code>object</code> does not have a metatable, returns <b>nil</b>.
Otherwise,
if the object's metatable has a <code>"__metatable"</code> field,
returns the associated value.
Otherwise, returns the metatable of the given object.

<p><a name="pdf-ipairs"></a><hr></hr><h3><code>ipairs (t)</code></h3>

<p>Returns three values: an iterator function, the table <code>t</code>, and 0,
so that the construction
<pre>
       for i,v in ipairs(t) do ... end
</pre>
will iterate over the pairs (<code>1,t[1]</code>), (<code>2,t[2]</code>), ...,
up to the first integer key with a nil value in the table.

<p>See <a href="#pdf-next"><code>next</code></a> for the caveats of modifying the table during its traversal.

<p><a name="pdf-load"></a><hr></hr><h3><code>load (func [, chunkname])</code></h3>

<p>Loads a chunk using function <code>func</code> to get its pieces.
Each call to <code>func</code> must return a string that concatenates
with previous results.
A return of <b>nil</b> (or no value) signals the end of the chunk.

<p>If there are no errors, 
returns the compiled chunk as a function;
otherwise, returns <b>nil</b> plus the error message.
The environment of the returned function is the global environment.

<p><code>chunkname</code> is used as the chunk name for error messages
and debug information.

<p><a name="pdf-loadfile"></a><hr></hr><h3><code>loadfile ([filename])</code></h3>

<p>Similar to <a href="#pdf-load"><code>load</code></a>,
but gets the chunk from file <code>filename</code>
or from the standard input,
if no file name is given.

<p><a name="pdf-loadstring"></a><hr></hr><h3><code>loadstring (string [, chunkname])</code></h3>

<p>Similar to <a href="#pdf-load"><code>load</code></a>,
but gets the chunk from the given string.

<p>To load and run a given string, use the idiom
<pre>
      assert(loadstring(s))()
</pre>

<p><a name="pdf-next"></a><hr></hr><h3><code>next (table [, index])</code></h3>

<p>Allows a program to traverse all fields of a table.
Its first argument is a table and its second argument
is an index in this table.
<code>next</code> returns the next index of the table
and its associated value.
When called with <b>nil</b> as its second argument,
<code>next</code> returns an initial index
and its associated value.
When called with the last index,
or with <b>nil</b> in an empty table,
<code>next</code> returns <b>nil</b>.
If the second argument is absent, then it is interpreted as <b>nil</b>.
In particular,
you can use <code>next(t)</code> to check whether a table is empty.

<p>Lua has no declaration of fields.
There is no difference between a
field not present in a table or a field with value <b>nil</b>.
Therefore, <code>next</code> only considers fields with non-<b>nil</b> values.
The order in which the indices are enumerated is not specified,
<em>even for numeric indices</em>.
(To traverse a table in numeric order,
use a numerical <b>for</b> or the <a href="#pdf-ipairs"><code>ipairs</code></a> function.)

<p>The behavior of <code>next</code> is <em>undefined</em> if,
during the traversal,
you assign any value to a non-existent field in the table.
You may however modify existing fields.
In particular, you may clear existing fields.

<p><a name="pdf-pairs"></a><hr></hr><h3><code>pairs (t)</code></h3>

<p>Returns three values: the <a href="#pdf-next"><code>next</code></a> function, the table <code>t</code>, and <b>nil</b>,
so that the construction
<pre>
       for k,v in pairs(t) do ... end
</pre>
will iterate over all key--value pairs of table <code>t</code>.

<p>See <a href="#pdf-next"><code>next</code></a> for the caveats of modifying the table during its traversal.

<p><a name="pdf-pcall"></a><hr></hr><h3><code>pcall (f, arg1, arg2, ...)</code></h3>

<p>Calls function <code>f</code> with
the given arguments in protected mode.
This means that any error inside <code>f</code> is not propagated;
instead, <code>pcall</code> catches the error
and returns a status code.
Its first result is the status code (a boolean),
which is true if the call succeeds without errors.
In such case, <code>pcall</code> also returns all results from the call,
after this first result.
In case of any error, <code>pcall</code> returns <b>false</b> plus the error message.

<p><a name="pdf-print"></a><hr></hr><h3><code>print (e1, e2, ...)</code></h3>
Receives any number of arguments,
and prints their values to <code>stdout</code>,
using the <a href="#pdf-tostring"><code>tostring</code></a> function to convert them to strings.
<code>print</code> is not intended for formatted output,
but only as a quick way to show a value,
typically for debugging.
For formatted output, use <a href="#pdf-string.format"><code>string.format</code></a>.

<p><a name="pdf-rawequal"></a><hr></hr><h3><code>rawequal (v1, v2)</code></h3>
Checks whether <code>v1</code> is equal to <code>v2</code>,
without invoking any metamethod.
Returns a boolean.

<p><a name="pdf-rawget"></a><hr></hr><h3><code>rawget (table, index)</code></h3>
Gets the real value of <code>table[index]</code>,
without invoking any metamethod.
<code>table</code> must be a table and
<code>index</code> any value different from <b>nil</b>.

<p><a name="pdf-rawset"></a><hr></hr><h3><code>rawset (table, index, value)</code></h3>
Sets the real value of <code>table[index]</code> to <code>value</code>,
without invoking any metamethod.
<code>table</code> must be a table,
<code>index</code> any value different from <b>nil</b>,
and <code>value</code> any Lua value.

<p><a name="pdf-select"></a><hr></hr><h3><code>select (index, ...)</code></h3>

<p>If <code>index</code> is a number,
returns all arguments after argument number <code>index</code>.
Otherwise, <code>index</code> must be the string <code>"#"</code>,
and <code>select</code> returns the total number of extra arguments it received.

<p><a name="setfenv"></a><a name="pdf-setfenv"></a><hr></hr><h3><code>setfenv (f, table)</code></h3>

<p>Sets the environment to be used by the given function.
<code>f</code> can be a Lua function or a number
that specifies the function at that stack level:
Level 1 is the function calling <code>setfenv</code>.
<code>setfenv</code> returns the given function.

<p>As a special case, when <code>f</code> is 0 <code>setfenv</code> changes
the environment of the running thread.
In this case, <code>setfenv</code> returns no values.

<p><a name="pdf-setmetatable"></a><hr></hr><h3><code>setmetatable (table, metatable)</code></h3>

<p>Sets the metatable for the given table.
(You cannot change the metatable of other types from Lua, only from C.)
If <code>metatable</code> is <b>nil</b>,
removes the metatable of the given table.
If the original metatable has a <code>"__metatable"</code> field,
raises an error.

<p>This function returns <code>table</code>.

<p><a name="pdf-tonumber"></a><hr></hr><h3><code>tonumber (e [, base])</code></h3>
Tries to convert its argument to a number.
If the argument is already a number or a string convertible
to a number, then <code>tonumber</code> returns this number;
otherwise, it returns <b>nil</b>.

<p>An optional argument specifies the base to interpret the numeral.
The base may be any integer between 2 and 36, inclusive.
In bases above 10, the letter `<code>A</code>&acute; (in either upper or lower case)
represents 10, `<code>B</code>&acute; represents 11, and so forth,
with `<code>Z</code>&acute; representing 35.
In base 10 (the default), the number may have a decimal part,
as well as an optional exponent part (see <a href="#lexical">2.1</a>).
In other bases, only unsigned integers are accepted.

<p><a name="pdf-tostring"></a><hr></hr><h3><code>tostring (e)</code></h3>
Receives an argument of any type and
converts it to a string in a reasonable format.
For complete control of how numbers are converted,
use <a href="#pdf-string.format"><code>string.format</code></a>.

<p>If the metatable of <code>e</code> has a <code>"__tostring"</code> field,
then <code>tostring</code> calls the corresponding value
with <code>e</code> as argument,
and uses the result of the call as its result.

<p><a name="pdf-type"></a><hr></hr><h3><code>type (v)</code></h3>
Returns the type of its only argument, coded as a string.
The possible results of this function are
<code>"nil"</code> (a string, not the value <b>nil</b>),
<code>"number"</code>,
<code>"string"</code>,
<code>"boolean</code>,
<code>"table"</code>,
<code>"function"</code>,
<code>"thread"</code>,
and <code>"userdata"</code>.

<p><a name="pdf-unpack"></a><hr></hr><h3><code>unpack (list [, i [, j]])</code></h3>
Returns the elements from the given table.
This function is equivalent to
<pre>
  return list[i], list[i+1], ..., list[j]
</pre>
except that the above code can be written only for a fixed number
of elements.
By default, <code>i</code> is 1 and <code>j</code> is the length of the list,
as defined by the length operator (see <a href="#len-op">2.5.5</a>).

<p><a name="pdf-_VERSION"></a><hr></hr><h3><code>_VERSION</code></h3>
A global variable (not a function) that
holds a string containing the current interpreter version.
The current contents of this variable is <code>"Lua 5.1"</code>.

<p><a name="pdf-xpcall"></a><hr></hr><h3><code>xpcall (f, err)</code></h3>

<p>This function is similar to <code>pcall</code>,
except that you can set a new error handler.

<p><code>xpcall</code> calls function <code>f</code> in protected mode,
using <code>err</code> as the error handler.
Any error inside <code>f</code> is not propagated;
instead, <code>xpcall</code> catches the error,
calls the <code>err</code> function with the original error object,
and returns a status code.
Its first result is the status code (a boolean),
which is true if the call succeeds without errors.
In this case, <code>xpcall</code> also returns all results from the call,
after this first result.
In case of any error,
<code>xpcall</code> returns <b>false</b> plus the result from <code>err</code>.

<p><a name="5.2"></a><h2>5.2 - Coroutine Manipulation</h2>

<p>The operations related to coroutines comprise a sub-library of
the basic library and come inside the table <code>coroutine</code>.
See <a href="#coroutine">2.11</a> for a general description of coroutines.

<p><a name="pdf-coroutine.create"></a><hr></hr><h3><code>coroutine.create (f)</code></h3>

<p>Creates a new coroutine, with body <code>f</code>.
<code>f</code> must be a Lua function.
Returns this new coroutine,
an object with type <code>"thread"</code>.

<p><a name="pdf-coroutine.resume"></a><hr></hr><h3><code>coroutine.resume (co [, val1, ..., valn])</code></h3>

<p>Starts or continues the execution of coroutine <code>co</code>.
The first time you resume a coroutine,
it starts running its body.
The values <code>val1</code>, ..., <code>valn</code> are passed
as the arguments to the body function.
If the coroutine has yielded,
<code>resume</code> restarts it;
the values <code>val1</code>, ..., <code>valn</code> are passed
as the results from the yield.

<p>If the coroutine runs without any errors,
<code>resume</code> returns <b>true</b> plus any values passed to <code>yield</code>
(if the coroutine yields) or any values returned by the body function
(if the coroutine terminates).
If there is any error,
<code>resume</code> returns <b>false</b> plus the error message.

<p><a name="pdf-coroutine.running"></a><hr></hr><h3><code>coroutine.running ()</code></h3>

<p>Returns the running coroutine,
or <b>nil</b> when called by the main thread.

<p><a name="pdf-coroutine.status"></a><hr></hr><h3><code>coroutine.status (co)</code></h3>

<p>Returns the status of coroutine <code>co</code>, as a string:
<code>"running"</code>,
if the coroutine is running (that is, it called <code>status</code>);
<code>"suspended"</code>, if the coroutine is suspended in a call to <code>yield</code>,
or if it has not started running yet;
<code>"normal"</code> if the coroutine is active but not running
(that is, it has resumed another coroutine);
and <code>"dead"</code> if the coroutine has finished its body function,
or if it has stopped with an error.

<p><a name="pdf-coroutine.wrap"></a><hr></hr><h3><code>coroutine.wrap (f)</code></h3>

<p>Creates a new coroutine, with body <code>f</code>.
<code>f</code> must be a Lua function.
Returns a function that resumes the coroutine each time it is called.
Any arguments passed to the function behave as the
extra arguments to <code>resume</code>.
Returns the same values returned by <code>resume</code>,
except the first boolean.
In case of error, propagates the error.

<p><a name="pdf-coroutine.yield"></a><hr></hr><h3><code>coroutine.yield ([val1, ..., valn])</code></h3>

<p>Suspends the execution of the calling coroutine.
The coroutine cannot be running a C function,
a metamethod, or an iterator.
Any arguments to <code>yield</code> are passed as extra results to <code>resume</code>.

<p><a name="5.3"></a><h2>5.3 - Modules</h2>

<p>The package library provides basic
facilities for loading and building modules in Lua.
It exports two of its functions directly in the global environment:
<a href="#pdf-require"><code>require</code></a> and <a href="#pdf-module"><code>module</code></a>.
Everything else is exported in a table <code>package</code>.

<p><a name="pdf-module"></a><hr></hr><h3><code>module (name [, ...])</code></h3>

<p>Creates a module.
If there is a table in <code>package.loaded[name]</code>,
this table is the module.
Otherwise, if there is a global table <code>t</code> with the given name,
this table is the module.
Otherwise creates a new table <code>t</code> and
sets it as the value of the global <code>name</code> and
the value of <code>package.loaded[name]</code>.
This function also initializes <code>t._NAME</code> with the given name,
<code>t._M</code> with the module (<code>t</code> itself),
and <code>t._PACKAGE</code> with the package name
(the full module name minus last component; see below).
Finally, <code>module</code> sets <code>t</code> as the new environment
of the current function and the new value of <code>package.loaded[name]</code>,
so that <a href="#pdf-require"><code>require</code></a> returns <code>t</code>.

<p>If <code>name</code> is a compound name
(that is, one with components separated by dots),
<code>module</code> creates (or reuses, if they already exist)
tables for each component.
For instance, if <code>name</code> is <code>a.b.c</code>,
then <code>module</code> stores the module table in field <code>c</code> of
field <code>b</code> of global <code>a</code>.

<p>This function may receive optional <em>options</em> after
the module name,
where each option is a function to be applied over the module.

<p><a name="pdf-require"></a><hr></hr><h3><code>require (modname)</code></h3>

<p>Loads the given module.
The function starts by looking into the table <code>package.loaded</code>
to determine whether <code>modname</code> is already loaded.
If it is, then <code>require</code> returns the value stored
at <code>package.loaded[modname]</code>.
Otherwise, it tries to find a <em>loader</em> for the module.

<p>To find a loader,
first <code>require</code> queries <code>package.preload[modname]</code>.
If it has a value,
this value (which should be a function) is the loader.
Otherwise <code>require</code> searches for a Lua loader using the
path stored in <code>package.path</code>.
If that also fails, it searches for a C loader using the
path stored in <code>package.cpath</code>.
If that also fails,
it tries an <em>all-in-one</em> loader (see below).

<p>When loading a C library,
<code>require</code> first uses a dynamic link facility to link the
application with the library.
Then it tries to find a C function inside this library to
be used as the loader.
The name of this C function is the string <code>"luaopen_"</code>
concatenated with a copy of the module name where each dot
is replaced by an underscore.
Moreover, if the module name has a hyphen,
its prefix up to (and including) the first hyphen is removed.
For instance, if the module name is <code>a.v1-b.c</code>,
the function name will be <code>luaopen_b_c</code>.

<p>If <code>require</code> finds neither a Lua library nor a
C library for a module,
it calls the <em>all-in-one loader</em>.
This loader searches the C path for a library for
the root name of the given module.
For instance, when requiring <code>a.b.c</code>,
it will search for a C library for <code>a</code>.
If found, it looks into it for an open function for
the submodule;
in our example, that would be <code>luaopen_a_b_c</code>.
With this facility, a package can pack several C submodules
into one single library,
with each submodule keeping its original open function.

<p>Once a loader is found,
<code>require</code> calls the loader with a single argument, <code>modname</code>.
If the loader returns any value,
<code>require</code> assigns it to <code>package.loaded[modname]</code>.
If the loader returns no value and
has not assigned any value to <code>package.loaded[modname]</code>,
then <code>require</code> assigns <b>true</b> to this entry.
In any case, <code>require</code> returns the
final value of <code>package.loaded[modname]</code>.

<p>If there is any error loading or running the module,
or if it cannot find any loader for the module,
then <code>require</code> signals an error. 

<p><a name="pdf-package.cpath"></a><hr></hr><h3><code>package.cpath</code></h3>

<p>The path used by <a href="#pdf-require"><code>require</code></a> to search for a C loader.

<p>Lua initializes the C path <code>package.cpath</code> in the same way
it initializes the Lua path <a href="#pdf-package.path"><code>package.path</code></a>,
using the environment variable <code>LUA_CPATH</code>
(plus another default path defined in <code>luaconf.h</code>).

<p><a name="pdf-package.loaded"></a><hr></hr><h3><code>package.loaded</code></h3>

<p>A table used by <a href="#pdf-require"><code>require</code></a> to control which
modules are already loaded.
When you require a module <code>modname</code> and
<code>package.loaded[modname]</code> is not false,
<a href="#pdf-require"><code>require</code></a> simply returns the value stored there.

<p><a name="pdf-package.loadlib"></a><hr></hr><h3><code>package.loadlib (libname, funcname)</code></h3>

<p>Dynamically links the host program with the C library <code>libname</code>.
Inside this library, looks for a function <code>funcname</code>
and returns this function as a C function.
(So, <code>funcname</code> must follow the protocol (see <a href="#lua_CFunction"><code>lua_CFunction</code></a>)).

<p>This is a low-level function.
It completely bypasses the package and module system.
Unlike <a href="#pdf-require"><code>require</code></a>,
it does not perform any path searching and
does not automatically adds extensions.
<code>libname</code> must be the complete file name of the C library,
including if necessary a path and extension.
<code>funcname</code> must be the exact name exported by the C library
(which may depend on the C compiler and linker used).

<p>This function is not supported by ANSI C.
As such, it is only available on some platforms
(Windows, Linux, Mac OS X, Solaris, BSD,
plus other Unix systems that support the <code>dlfcn</code> standard).

<p><a name="pdf-package.path"></a><hr></hr><h3><code>package.path</code></h3>

<p>The path used by <a href="#pdf-require"><code>require</code></a> to search for a Lua loader.

<p>At start-up, Lua initializes this variable with
the value of the environment variable <code>LUA_PATH</code> or
with a default path defined in <code>luaconf.h</code>,
if the environment variable is not defined.
Any <code>";;"</code> in the value of the environment variable
is replaced by the default path.

<p>A path is a sequence of <em>templates</em> separated by semicolons.
For each template, <a href="#pdf-require"><code>require</code></a> will change each interrogation
mark in the template by <code>filename</code>,
which is <code>modname</code> with each dot replaced by a
"directory separator" (such as <code>"/"</code> in Unix);
then it will try to load the resulting file name.
So, for instance, if the Lua path is
<pre>
  "./?.lua;./?.lc;/usr/local/?/init.lua"
</pre>
the search for a Lua loader for module <code>foo</code>
will try to load the files
<code>./foo.lua</code>, <code>./foo.lc</code>, and
<code>/usr/local/foo/init.lua</code>, in that order.

<p><a name="pdf-package.preload"></a><hr></hr><h3><code>package.preload</code></h3>

<p>A table to store loaders for specific modules
(see <a href="#pdf-require"><code>require</code></a>).

<p><a name="pdf-package.seeall"></a><hr></hr><h3><code>package.seeall (module)</code></h3>

<p>Sets a metatable for <code>module</code> with
its <code>__index</code> field referring to the global environment,
so that this module inherits values
from the global environment.
To be used as an option to function <a href="#pdf-module"><code>module</code></a>.

<p><a name="5.4"></a><h2>5.4 - String Manipulation</h2>

<p>This library provides generic functions for string manipulation,
such as finding and extracting substrings, and pattern matching.
When indexing a string in Lua, the first character is at position 1
(not at 0, as in C).
Indices are allowed to be negative and are interpreted as indexing backwards,
from the end of the string.
Thus, the last character is at position <em>-1</em>, and so on.

<p>The string library provides all its functions inside the table
<code>string</code>.
It also sets a metatable for strings
where the <code>__index</code> field points to the metatable itself.
Therefore, you can use the string functions in object-oriented style.
For instance, <code>string.byte(s, i)</code>
can be written as <code>s:byte(i)</code>.

<p><a name="pdf-string.byte"></a><hr></hr><h3><code>string.byte (s [, i [, j]])</code></h3>
Returns the internal numerical codes of the characters <code>s[i]</code>,
<code>s[i+1]</code>, ..., <code>s[j]</code>.
The default value for <code>i</code> is 1;
the default value for <code>j</code> is <code>i</code>.

<p>Note that numerical codes are not necessarily portable across platforms.

<p><a name="pdf-string.char"></a><hr></hr><h3><code>string.char (i1, i2, ...)</code></h3>
Receives 0 or more integers.
Returns a string with length equal to the number of arguments,
in which each character has the internal numerical code equal
to its corresponding argument.

<p>Note that numerical codes are not necessarily portable across platforms.

<p><a name="pdf-string.dump"></a><hr></hr><h3><code>string.dump (function)</code></h3>

<p>Returns a string containing a binary representation of the given function,
so that a later <a href="#pdf-loadstring"><code>loadstring</code></a> on this string returns
a copy of the function.
<code>function</code> must be a Lua function without upvalues.

<p><a name="pdf-string.find"></a><hr></hr><h3><code>string.find (s, pattern [, init [, plain]])</code></h3>
Looks for the first match of
<code>pattern</code> in the string <code>s</code>.
If it finds a match, then <code>find</code> returns the indices of <code>s</code>
where this occurrence starts and ends;
otherwise, it returns <b>nil</b>.
A third, optional numerical argument <code>init</code> specifies
where to start the search;
its default value is 1 and may be negative.
A value of <b>true</b> as a fourth, optional argument <code>plain</code>
turns off the pattern matching facilities,
so the function does a plain "find substring" operation,
with no characters in <code>pattern</code> being considered "magic".
Note that if <code>plain</code> is given, then <code>init</code> must be given as well.

<p>If the pattern has captures,
then in a successful match
the captured values are also returned,
after the two indices.

<p><a name="format"></a><a name="pdf-string.format"></a><hr></hr><h3><code>string.format (formatstring, e1, e2, ...)</code></h3>
Returns a formatted version of its variable number of arguments
following the description given in its first argument (which must be a string).
The format string follows the same rules as the <code>printf</code> family of
standard C functions.
The only differences are that the options/modifiers
<code>*</code>, <code>l</code>, <code>L</code>, <code>n</code>, <code>p</code>,
and <code>h</code> are not supported
and that there is an extra option, <code>q</code>.
The <code>q</code> option formats a string in a form suitable to be safely read
back by the Lua interpreter:
The string is written between double quotes,
and all double quotes, newlines, embedded zeros,
and backslashes in the string
are correctly escaped when written.
For instance, the call
<pre>
       string.format('%q', 'a string with "quotes" and \n new line')
</pre>
will produce the string:
<pre>
"a string with \"quotes\" and \
 new line"
</pre>

<p>The options <code>c</code>, <code>d</code>, <code>E</code>, <code>e</code>, <code>f</code>,
<code>g</code>, <code>G</code>, <code>i</code>, <code>o</code>, <code>u</code>, <code>X</code>, and <code>x</code> all
expect a number as argument,
whereas <code>q</code> and <code>s</code> expect a string.

<p>This function does not accept string values
containing embedded zeros.

<p><a name="pdf-string.gmatch"></a><hr></hr><h3><code>string.gmatch (s, pattern)</code></h3>
Returns an iterator function that,
each time it is called,
returns the next captures from <code>pattern</code> over string <code>s</code>.

<p>If <code>pattern</code> specifies no captures,
then the whole match is produced in each call.

<p>As an example, the following loop
<pre>
  s = "hello world from Lua"
  for w in string.gmatch(s, "%a+") do
    print(w)
  end
</pre>
will iterate over all the words from string <code>s</code>,
printing one per line.
The next example collects all pairs <code>key=value</code> from the
given string into a table:
<pre>
  t = {}
  s = "from=world, to=Lua"
  for k, v in string.gmatch(s, "(%w+)=(%w+)") do
    t[k] = v
  end
</pre>

<p><a name="pdf-string.gsub"></a><hr></hr><h3><code>string.gsub (s, pattern, repl [, n])</code></h3>
Returns a copy of <code>s</code>
in which all occurrences of the <code>pattern</code> have been
replaced by a replacement string specified by <code>repl</code>,
which may be a string, a table, or a function.
<code>gsub</code> also returns, as its second value,
the total number of substitutions made.

<p>If <code>repl</code> is a string, then its value is used for replacement.
The character <code>%</code> works as an escape character:
Any sequence in <code>repl</code> of the form <code>%</code><em>n</em>,
with <em>n</em> between 1 and 9,
stands for the value of the <em>n</em>-th captured substring (see below).
The sequence <code>%0</code> stands for the whole match.
The sequence <code>%%</code> stands for a single <code>%</code>.

<p>If <code>repl</code> is a table, then the table is queried for every match,
using the first capture as the key;
if the pattern specifies no captures,
then the whole match is used as the key.

<p>If <code>repl</code> is a function, then this function is called every time a
match occurs, with all captured substrings passed as arguments,
in order;
if the pattern specifies no captures,
then the whole match is passed as a sole argument.

<p>If the value returned by the table query or by the function call
is a string or a number,
then it is used as the replacement string;
otherwise, if it is <b>false</b> or <b>nil</b>,
then there is no replacement
(that is, the original match is kept in the string).

<p>The optional last parameter <code>n</code> limits
the maximum number of substitutions to occur.
For instance, when <code>n</code> is 1 only the first occurrence of
<code>pattern</code> is replaced.

<p>Here are some examples:
<pre>
   x = string.gsub("hello world", "(%w+)", "%1 %1")
   --> x="hello hello world world"

   x = string.gsub("hello world", "%w+", "%0 %0", 1)
   --> x="hello hello world"

   x = string.gsub("hello world from Lua", "(%w+)%s*(%w+)", "%2 %1")
   --> x="world hello Lua from"

   x = string.gsub("home = $HOME, user = $USER", "%$(%w+)", os.getenv)
   --> x="home = /home/<USER>"

   x = string.gsub("4+5 = $return 4+5$", "%$(.-)%$", function (s)
         return loadstring(s)()
       end)
   --> x="4+5 = 9"

   local t = {name="lua", version="5.1"}
   x = string.gsub("$name%-$version.tar.gz", "%$(%w+)", t)
   --> x="lua-5.1.tar.gz"
</pre>

<p><a name="pdf-string.len"></a><hr></hr><h3><code>string.len (s)</code></h3>
Receives a string and returns its length.
The empty string <code>""</code> has length 0.
Embedded zeros are counted,
so <code>"a\000bc\000"</code> has length 5.

<p><a name="pdf-string.lower"></a><hr></hr><h3><code>string.lower (s)</code></h3>
Receives a string and returns a copy of this string with all
uppercase letters changed to lowercase.
All other characters are left unchanged.
The definition of what an uppercase letter is depends on the current locale.

<p><a name="pdf-string.match"></a><hr></hr><h3><code>string.match (s, pattern [, init])</code></h3>
Looks for the first <em>match</em> of
<code>pattern</code> in the string <code>s</code>.
If it finds one, then <code>match</code> returns
the captures from the pattern;
otherwise it returns <b>nil</b>.
If <code>pattern</code> specifies no captures,
then the whole match is returned.
A third, optional numerical argument <code>init</code> specifies
where to start the search;
its default value is 1 and may be negative.

<p><a name="pdf-string.rep"></a><hr></hr><h3><code>string.rep (s, n)</code></h3>
Returns a string that is the concatenation of <code>n</code> copies of
the string <code>s</code>.

<p><a name="pdf-string.reverse"></a><hr></hr><h3><code>string.reverse (s)</code></h3>
Returns a string that is the string <code>s</code> reversed.

<p><a name="pdf-string.sub"></a><hr></hr><h3><code>string.sub (s, i [, j])</code></h3>
Returns the substring of <code>s</code> that
starts at <code>i</code>  and continues until <code>j</code>;
<code>i</code> and <code>j</code> may be negative.
If <code>j</code> is absent, then it is assumed to be equal to <em>-1</em>
(which is the same as the string length).
In particular,
the call <code>string.sub(s,1,j)</code> returns a prefix of <code>s</code>
with length <code>j</code>,
and <code>string.sub(s, -i)</code> returns a suffix of <code>s</code>
with length <code>i</code>.

<p><a name="pdf-string.upper"></a><hr></hr><h3><code>string.upper (s)</code></h3>
Receives a string and returns a copy of this string with all
lowercase letters changed to uppercase.
All other characters are left unchanged.
The definition of what a lowercase letter is depends on the current locale.

<p><a name="pm"></a><h3>Patterns</h3>

<p><p>
A <em>character class</em> is used to represent a set of characters.
The following combinations are allowed in describing a character class:
<ul>
<li><b><em>x</em></b> (where <em>x</em> is not one of the <em>magic characters</em>
<code>^$()%.[]*+-?</code>)
--- represents the character <em>x</em> itself.
<li><b><code>.</code></b> --- (a dot) represents all characters.
<li><b><code>%a</code></b> --- represents all letters.
<li><b><code>%c</code></b> --- represents all control characters.
<li><b><code>%d</code></b> --- represents all digits.
<li><b><code>%l</code></b> --- represents all lowercase letters.
<li><b><code>%p</code></b> --- represents all punctuation characters.
<li><b><code>%s</code></b> --- represents all space characters.
<li><b><code>%u</code></b> --- represents all uppercase letters.
<li><b><code>%w</code></b> --- represents all alphanumeric characters.
<li><b><code>%x</code></b> --- represents all hexadecimal digits.
<li><b><code>%z</code></b> --- represents the character with representation 0.
<li><b><code>%<em>x</em></code></b> (where <em>x</em> is any non-alphanumeric character)  ---
represents the character <em>x</em>.
This is the standard way to escape the magic characters.
Any punctuation character (even the non magic)
can be preceded by a `<code>%</code>&acute;
when used to represent itself in a pattern.

<p><li><b><code>[<em>set</em>]</code></b> ---
represents the class which is the union of all
characters in <em>set</em>.
A range of characters may be specified by
separating the end characters of the range with a `<code>-</code>&acute;.
All classes <code>%</code><em>x</em> described above may also be used as
components in <em>set</em>.
All other characters in <em>set</em> represent themselves.
For example, <code>[%w_]</code> (or <code>[_%w]</code>)
represents all alphanumeric characters plus the underscore,
<code>[0-7]</code> represents the octal digits,
and <code>[0-7%l%-]</code> represents the octal digits plus
the lowercase letters plus the `<code>-</code>&acute; character.

<p>The interaction between ranges and classes is not defined.
Therefore, patterns like <code>[%a-z]</code> or <code>[a-%%]</code>
have no meaning.

<p><li><b><code>[^<em>set</em>]</code></b> ---
represents the complement of <em>set</em>,
where <em>set</em> is interpreted as above.
</ul>
For all classes represented by single letters (<code>%a</code>, <code>%c</code>, etc.),
the corresponding uppercase letter represents the complement of the class.
For instance, <code>%S</code> represents all non-space characters.

<p>The definitions of letter, space, and other character groups
depend on the current locale.
In particular, the class <code>[a-z]</code> may not be equivalent to <code>%l</code>.

<p><p>
A <em>pattern item</em> may be
<ul>
<li> 
a single character class,
which matches any single character in the class;
<li> 
a single character class followed by `<code>*</code>&acute;,
which matches 0 or more repetitions of characters in the class.
These repetition items will always match the longest possible sequence;
<li> 
a single character class followed by `<code>+</code>&acute;,
which matches 1 or more repetitions of characters in the class.
These repetition items will always match the longest possible sequence;
<li> 
a single character class followed by `<code>-</code>&acute;,
which also matches 0 or more repetitions of characters in the class.
Unlike `<code>*</code>&acute;,
these repetition items will always match the <em>shortest</em> possible sequence;
<li> 
a single character class followed by `<code>?</code>&acute;,
which matches 0 or 1 occurrence of a character in the class;
<li> 
<code>%</code><em>n</em>, for <em>n</em> between 1 and 9;
such item matches a substring equal to the <em>n</em>-th captured string
(see below);
<li> 
<code>%b</code><em>xy</em>, where <em>x</em> and <em>y</em> are two distinct characters;
such item matches strings that start with <em>x</em>, end with <em>y</em>,
and where the <em>x</em> and <em>y</em> are <em>balanced</em>.
This means that, if one reads the string from left to right,
counting <em>+1</em> for an <em>x</em> and <em>-1</em> for a <em>y</em>,
the ending <em>y</em> is the first <em>y</em> where the count reaches 0.
For instance, the item <code>%b()</code> matches expressions with
balanced parentheses.
</ul>

<p><p>
A <em>pattern</em> is a sequence of pattern items.
A `<code>^</code>&acute; at the beginning of a pattern anchors the match at the
beginning of the subject string.
A `<code>$</code>&acute; at the end of a pattern anchors the match at the
end of the subject string.
At other positions,
`<code>^</code>&acute; and `<code>$</code>&acute; have no special meaning and represent themselves.

<p><p>
A pattern may contain sub-patterns enclosed in parentheses;
they describe <em>captures</em>.
When a match succeeds, the substrings of the subject string
that match captures are stored (<em>captured</em>) for future use.
Captures are numbered according to their left parentheses.
For instance, in the pattern <code>"(a*(.)%w(%s*))"</code>,
the part of the string matching <code>"a*(.)%w(%s*)"</code> is
stored as the first capture (and therefore has number 1);
the character matching <code>"."</code> is captured with number 2,
and the part matching <code>"%s*"</code> has number 3.

<p>As a special case, the empty capture <code>()</code> captures
the current string position (a number).
For instance, if we apply the pattern <code>"()aa()"</code> on the
string <code>"flaaap"</code>, there will be two captures: 3 and 5.

<p>A pattern cannot contain embedded zeros.  Use <code>%z</code> instead.

<p><a name="5.5"></a><h2>5.5 - Table Manipulation</h2>
This library provides generic functions for table manipulation.
It provides all its functions inside the table <code>table</code>.

<p>Most functions in the table library assume that the table
represents an array or a list.
For these functions, when we talk about the "length" of a table
we mean the result of the length operator.

<p><a name="pdf-table.concat"></a><hr></hr><h3><code>table.concat (table [, sep [, i [, j]]])</code></h3>
Returns <code>table[i]..sep..table[i+1] ... sep..table[j]</code>.
The default value for <code>sep</code> is the empty string,
the default for <code>i</code> is 1,
and the default for <code>j</code> is the length of the table.
If <code>i</code> is greater than <code>j</code>, returns the empty string.

<p><a name="pdf-table.insert"></a><hr></hr><h3><code>table.insert (table, [pos,] value)</code></h3>

<p>Inserts element <code>value</code> at position <code>pos</code> in <code>table</code>,
shifting up other elements to open space, if necessary.
The default value for <code>pos</code> is <code>n+1</code>,
where <code>n</code> is the length of the table (see <a href="#len-op">2.5.5</a>),
so that a call <code>table.insert(t,x)</code> inserts <code>x</code> at the end
of table <code>t</code>.

<p><a name="pdf-table.maxn"></a><hr></hr><h3><code>table.maxn (table)</code></h3>

<p>Returns the largest positive numerical index of the given table,
or zero if the table has no positive numerical indices.
(To do its job this function does a linear traversal of
the whole table.) 

<p><a name="pdf-table.remove"></a><hr></hr><h3><code>table.remove (table [, pos])</code></h3>

<p>Removes from <code>table</code> the element at position <code>pos</code>,
shifting down other elements to close the space, if necessary.
Returns the value of the removed element.
The default value for <code>pos</code> is <code>n</code>,
where <code>n</code> is the length of the table,
so that a call <code>table.remove(t)</code> removes the last element
of table <code>t</code>.

<p><a name="pdf-table.sort"></a><hr></hr><h3><code>table.sort (table [, comp])</code></h3>
Sorts table elements in a given order, <em>in-place</em>,
from <code>table[1]</code> to <code>table[n]</code>,
where <code>n</code> is the length of the table.
If <code>comp</code> is given,
then it must be a function that receives two table elements,
and returns true
when the first is less than the second
(so that <code>not comp(a[i+1],a[i])</code> will be true after the sort).
If <code>comp</code> is not given,
then the standard Lua operator <code>&#060;</code> is used instead.

<p>The sort algorithm is not stable;
that is, elements considered equal by the given order
may have their relative positions changed by the sort.

<p><a name="mathlib"></a><a name="5.6"></a><h2>5.6 - Mathematical Functions</h2>

<p>This library is an interface to the standard C math library.
It provides all its functions inside the table <code>math</code>.
The library provides the following functions:
<a name="pdf-math.abs"></a> <a name="pdf-math.acos"></a> <a name="pdf-math.asin"></a> <a name="pdf-math.atan"></a>
<a name="pdf-math.atan2"></a> <a name="pdf-math.ceil"></a> <a name="pdf-math.cos"></a> <a name="pdf-math.cosh"></a>
<a name="pdf-math.deg"></a> <a name="pdf-math.exp"></a> <a name="pdf-math.floor"></a> <a name="pdf-math.fmod"></a>
<a name="pdf-math.frexp"></a> <a name="pdf-math.ldexp"></a> <a name="pdf-math.log"></a> <a name="pdf-math.log10"></a>
<a name="pdf-math.max"></a> <a name="pdf-math.min"></a> <a name="pdf-math.modf"></a> <a name="pdf-math.pow"></a>
<a name="pdf-math.rad"></a> <a name="pdf-math.random"></a> <a name="pdf-math.randomseed"></a>
<a name="pdf-math.sin"></a> <a name="pdf-math.sinh"></a> <a name="pdf-math.sqrt"></a> <a name="pdf-math.tan"></a>
<a name="pdf-math.tanh"></a>
<pre>
       math.abs     math.acos    math.asin    math.atan    math.atan2
       math.ceil    math.cos     math.cosh    math.deg     math.exp
       math.floor   math.fmod    math.frexp   math.ldexp   math.log
       math.log10   math.max     math.min     math.modf    math.pow
       math.rad     math.random  math.randomseed           math.sin
       math.sinh    math.sqrt    math.tan     math.tanh
</pre>
plus a variable <code>math.pi</code> and
a variable <code>math.huge</code>,
with the value <code>HUGE_VAL</code>.
Most of these functions
are only interfaces to the corresponding functions in the C library.
All trigonometric functions work in radians.
The functions <code>math.deg</code> and <code>math.rad</code> convert
between radians and degrees.

<p>The function <code>math.max</code> returns the maximum
value of its numeric arguments.
Similarly, <code>math.min</code> computes the minimum.
Both can be used with 1, 2, or more arguments.

<p>The function <code>math.modf</code> corresponds to the <code>modf</code> C function.
It returns two values:
The integral part and the fractional part of its argument.
The function <code>math.frexp</code> also returns 2 values:
The normalized fraction and the exponent of its argument.

<p>The functions <code>math.random</code> and <code>math.randomseed</code>
are interfaces to the simple random generator functions
<code>rand</code> and <code>srand</code> that are provided by ANSI C.
(No guarantees can be given for their statistical properties.)
When called without arguments,
<code>math.random</code> returns a pseudo-random real number
in the range <em>[0,1)</em>.  
When called with a number <em>n</em>,
<code>math.random</code> returns
a pseudo-random integer in the range <em>[1,n]</em>.
When called with two arguments,
<em>l</em> and <em>u</em>,
<code>math.random</code> returns a pseudo-random
integer in the range <em>[l,u]</em>.
The <code>math.randomseed</code> function sets a "seed"
for the pseudo-random generator:
Equal seeds produce equal sequences of numbers.

<p><a name="libio"></a><a name="5.7"></a><h2>5.7 - Input and Output Facilities</h2>

<p>The I/O library provides two different styles for file manipulation.
The first one uses implicit file descriptors;
that is, there are operations to set a default input file and a
default output file,
and all input/output operations are over these default files.
The second style uses explicit file descriptors.

<p>When using implicit file descriptors,
all operations are supplied by table <code>io</code>.
When using explicit file descriptors,
the operation <a href="#pdf-io.open"><code>io.open</code></a> returns a file descriptor
and then all operations are supplied as methods of the file descriptor.

<p>The table <code>io</code> also provides
three predefined file descriptors with their usual meanings from C:
<code>io.stdin</code>, <code>io.stdout</code>, and <code>io.stderr</code>.

<p>Unless otherwise stated,
all I/O functions return <b>nil</b> on failure
(plus an error message as a second result)
and some value different from <b>nil</b> on success.

<p><a name="pdf-io.close"></a><hr></hr><h3><code>io.close ([file])</code></h3>

<p>Equivalent to <code>file:close()</code>.
Without a <code>file</code>, closes the default output file.

<p><a name="pdf-io.flush"></a><hr></hr><h3><code>io.flush ()</code></h3>

<p>Equivalent to <code>file:flush</code> over the default output file.

<p><a name="pdf-io.input"></a><hr></hr><h3><code>io.input ([file])</code></h3>

<p>When called with a file name, it opens the named file (in text mode),
and sets its handle as the default input file.
When called with a file handle,
it simply sets this file handle as the default input file.
When called without parameters,
it returns the current default input file.

<p>In case of errors this function raises the error,
instead of returning an error code.

<p><a name="pdf-io.lines"></a><hr></hr><h3><code>io.lines ([filename])</code></h3>

<p>Opens the given file name in read mode
and returns an iterator function that,
each time it is called,
returns a new line from the file.
Therefore, the construction
<pre>
       for line in io.lines(filename) do ... end
</pre>
will iterate over all lines of the file.
When the iterator function detects the end of file,
it returns <b>nil</b> (to finish the loop) and automatically closes the file.

<p>The call <code>io.lines()</code> (without a file name) is equivalent
to <code>io.input():lines()</code>;
that is, it iterates over the lines of the default input file.
In this case it does not close the file when the loop ends.

<p><a name="pdf-io.open"></a><hr></hr><h3><code>io.open (filename [, mode])</code></h3>

<p>This function opens a file,
in the mode specified in the string <code>mode</code>.
It returns a new file handle,
or, in case of errors, <b>nil</b> plus an error message.

<p>The <code>mode</code> string can be any of the following:
<ul>
<li><b>"r"</b> --- read mode (the default);
<li><b>"w"</b> --- write mode;
<li><b>"a"</b> --- append mode;
<li><b>"r+"</b> --- update mode, all previous data is preserved;
<li><b>"w+"</b> --- update mode, all previous data is erased;
<li><b>"a+"</b> --- append update mode, previous data is preserved,
  writing is only allowed at the end of file.
</ul>
The <code>mode</code> string may also have a `<code>b</code>&acute; at the end,
which is needed in some systems to open the file in binary mode.
This string is exactly what is used in the
standard C function <code>fopen</code>.

<p><a name="pdf-io.output"></a><hr></hr><h3><code>io.output ([file])</code></h3>

<p>Similar to <a href="#pdf-io.input"><code>io.input</code></a>, but operates over the default output file.

<p><a name="pdf-io.popen"></a><hr></hr><h3><code>io.popen ([prog [, mode]])</code></h3>

<p>Starts program <code>prog</code> in a separated process and returns
a file handle that you can use to read data from this program
(if <code>mode</code> is <code>"r"</code>, the default)
or to write data to this program
(if <code>mode</code> is <code>"w"</code>).

<p>This function is system dependent and is not available
on all platforms.

<p><a name="pdf-io.read"></a><hr></hr><h3><code>io.read (format1, ...)</code></h3>

<p>Equivalent to <code>io.input():read</code>.

<p><a name="pdf-io.tmpfile"></a><hr></hr><h3><code>io.tmpfile ()</code></h3>

<p>Returns a handle for a temporary file.
This file is opened in update mode
and it is automatically removed when the program ends.

<p><a name="pdf-io.type"></a><hr></hr><h3><code>io.type (obj)</code></h3>

<p>Checks whether <code>obj</code> is a valid file handle.
Returns the string <code>"file"</code> if <code>obj</code> is an open file handle,
<code>"closed file"</code> if <code>obj</code> is a closed file handle,
or <b>nil</b> if <code>obj</code> is not a file handle.

<p><a name="pdf-io.write"></a><hr></hr><h3><code>io.write (value1, ...)</code></h3>

<p>Equivalent to <code>io.output():write</code>.

<p><a name="pdf-file:close"></a><hr></hr><h3><code>file:close ()</code></h3>

<p>Closes <code>file</code>.
Note that files are automatically closed when
their handles are garbage collected,
but that takes an unpredictable amount of time to happen.

<p><a name="flush"></a><a name="pdf-file:flush"></a><hr></hr><h3><code>file:flush ()</code></h3>

<p>Saves any written data to <code>file</code>.

<p><a name="pdf-file:lines"></a><hr></hr><h3><code>file:lines ()</code></h3>

<p>Returns an iterator function that,
each time it is called,
returns a new line from the file.
Therefore, the construction
<pre>
       for line in file:lines() do ... end
</pre>
will iterate over all lines of the file.
(Unlike <a href="#pdf-io.lines"><code>io.lines</code></a>, this function does not close the file
when the loop ends.)

<p><a name="pdf-file:read"></a><hr></hr><h3><code>file:read (format1, ...)</code></h3>

<p>Reads the file <code>file</code>,
according to the given formats, which specify what to read.
For each format,
the function returns a string (or a number) with the characters read,
or <b>nil</b> if it cannot read data with the specified format.
When called without formats,
it uses a default format that reads the entire next line
(see below).

<p>The available formats are
<ul>
<li><b>"*n"</b> reads a number;
this is the only format that returns a number instead of a string.
<li><b>"*a"</b> reads the whole file, starting at the current position.
On end of file, it returns the empty string.
<li><b>"*l"</b> reads the next line (skipping the end of line),
returning <b>nil</b> on end of file.
This is the default format.
<li><b><em>number</em></b> reads a string with up to this number of characters,
returning <b>nil</b> on end of file.
If number is zero,
it reads nothing and returns an empty string,
or <b>nil</b> on end of file.
</ul>

<p><a name="pdf-file:seek"></a><hr></hr><h3><code>file:seek ([whence] [, offset])</code></h3>

<p>Sets and gets the file position,
measured from the beginning of the file,
to the position given by <code>offset</code> plus a base
specified by the string <code>whence</code>, as follows:
<ul>
<li><b>"set"</b> --- base is position 0 (beginning of the file);
<li><b>"cur"</b> --- base is current position;
<li><b>"end"</b> --- base is end of file;
</ul>
In case of success, function <code>seek</code> returns the final file position,
measured in bytes from the beginning of the file.
If this function fails, it returns <b>nil</b>,
plus a string describing the error.

<p>The default value for <code>whence</code> is <code>"cur"</code>,
and for <code>offset</code> is 0.
Therefore, the call <code>file:seek()</code> returns the current
file position, without changing it;
the call <code>file:seek("set")</code> sets the position to the
beginning of the file (and returns 0);
and the call <code>file:seek("end")</code> sets the position to the
end of the file, and returns its size.

<p><a name="pdf-file:setvbuf"></a><hr></hr><h3><code>file:setvbuf (mode [, size])</code></h3>

<p>Sets the buffering mode for an output file.
There are three available modes:
<ul>
<li><b>"no"</b> ---
no buffering; the result of any output operation appears immediately.
<li><b>"full"</b> ---
full buffering; output operation is performed only
when the buffer is full (or when you explicitly <code>flush</code> the file (see <a href="#flush">5.7</a>)).
<li><b>"line"</b> ---
line buffering; output is buffered until a newline is output
or there is any input from some special files
(such as a terminal device).
</ul>
For the last two cases, <code>sizes</code>
specifies the size of the buffer, in bytes.
The default is an appropriate size.

<p><a name="pdf-file:write"></a><hr></hr><h3><code>file:write (value1, ...)</code></h3>

<p>Writes the value of each of its arguments to
the <code>file</code>.
The arguments must be strings or numbers.
To write other values,
use <a href="#pdf-tostring"><code>tostring</code></a> or <a href="#pdf-string.format"><code>string.format</code></a> before <code>write</code>.

<p><a name="libiosys"></a><a name="5.8"></a><h2>5.8 - Operating System Facilities</h2>

<p>This library is implemented through table <code>os</code>.

<p><a name="pdf-os.clock"></a><hr></hr><h3><code>os.clock ()</code></h3>

<p>Returns an approximation of the amount in seconds of CPU time
used by the program.

<p><a name="pdf-os.date"></a><hr></hr><h3><code>os.date ([format [, time]])</code></h3>

<p>Returns a string or a table containing date and time,
formatted according to the given string <code>format</code>.

<p>If the <code>time</code> argument is present,
this is the time to be formatted
(see the <a href="#pdf-os.time"><code>os.time</code></a> function for a description of this value).
Otherwise, <code>date</code> formats the current time.

<p>If <code>format</code> starts with `<code>!</code>&acute;,
then the date is formatted in Coordinated Universal Time.
After this optional character,
if <code>format</code> is <code>*t</code>,
then <code>date</code> returns a table with the following fields:
<code>year</code> (four digits), <code>month</code> (1--12), <code>day</code> (1--31),
<code>hour</code> (0--23), <code>min</code> (0--59), <code>sec</code> (0--61),
<code>wday</code> (weekday, Sunday is 1),
<code>yday</code> (day of the year),
and <code>isdst</code> (daylight saving flag, a boolean).

<p>If <code>format</code> is not <code>*t</code>,
then <code>date</code> returns the date as a string,
formatted according to the same rules as the C function <code>strftime</code>.

<p>When called without arguments,
<code>date</code> returns a reasonable date and time representation that depends on
the host system and on the current locale
(that is, <code>os.date()</code> is equivalent to <code>os.date("%c")</code>).

<p><a name="pdf-os.difftime"></a><hr></hr><h3><code>os.difftime (t2, t1)</code></h3>

<p>Returns the number of seconds from time <code>t1</code> to time <code>t2</code>.
In POSIX, Windows, and some other systems,
this value is exactly <code>t2</code><em>-</em><code>t1</code>.

<p><a name="pdf-os.execute"></a><hr></hr><h3><code>os.execute ([command])</code></h3>

<p>This function is equivalent to the C function <code>system</code>.
It passes <code>command</code> to be executed by an operating system shell.
It returns a status code, which is system-dependent.
If <code>command</code> is absent, then it returns nonzero if a shell is available
and zero otherwise.

<p><a name="pdf-os.exit"></a><hr></hr><h3><code>os.exit ([code])</code></h3>

<p>Calls the C function <code>exit</code>,
with an optional <code>code</code>,
to terminate the host program.
The default value for <code>code</code> is the success code.

<p><a name="pdf-os.getenv"></a><hr></hr><h3><code>os.getenv (varname)</code></h3>

<p>Returns the value of the process environment variable <code>varname</code>,
or <b>nil</b> if the variable is not defined.

<p><a name="pdf-os.remove"></a><hr></hr><h3><code>os.remove (filename)</code></h3>

<p>Deletes the file or directory with the given name.
Directories must be empty to be removed.
If this function fails, it returns <b>nil</b>,
plus a string describing the error.

<p><a name="pdf-os.rename"></a><hr></hr><h3><code>os.rename (oldname, newname)</code></h3>

<p>Renames file or directory named <code>oldname</code> to <code>newname</code>.
If this function fails, it returns <b>nil</b>,
plus a string describing the error.

<p><a name="pdf-os.setlocale"></a><hr></hr><h3><code>os.setlocale (locale [, category])</code></h3>

<p>Sets the current locale of the program.
<code>locale</code> is a string specifying a locale;
<code>category</code> is an optional string describing which category to change:
<code>"all"</code>, <code>"collate"</code>, <code>"ctype"</code>,
<code>"monetary"</code>, <code>"numeric"</code>, or <code>"time"</code>;
the default category is <code>"all"</code>.
The function returns the name of the new locale,
or <b>nil</b> if the request cannot be honored.

<p><a name="pdf-os.time"></a><hr></hr><h3><code>os.time ([table])</code></h3>

<p>Returns the current time when called without arguments,
or a time representing the date and time specified by the given table.
This table must have fields <code>year</code>, <code>month</code>, and <code>day</code>,
and may have fields <code>hour</code>, <code>min</code>, <code>sec</code>, and <code>isdst</code>
(for a description of these fields, see the <a href="#pdf-os.date"><code>os.date</code></a> function).

<p>The returned value is a number, whose meaning depends on your system.
In POSIX, Windows, and some other systems, this number counts the number
of seconds since some given start time (the "epoch").
In other systems, the meaning is not specified,
and the number returned by <code>time</code> can be used only as an argument to
<code>date</code> and <code>difftime</code>.

<p><a name="pdf-os.tmpname"></a><hr></hr><h3><code>os.tmpname ()</code></h3>

<p>Returns a string with a file name that can
be used for a temporary file.
The file must be explicitly opened before its use
and explicitly removed when no longer needed.

<p><a name="libdebug"></a><a name="5.9"></a><h2>5.9 - The Debug Library</h2>

<p>This library provides
the functionality of the debug interface to Lua programs.
You should exert care when using this library.
The functions provided here should be used exclusively for debugging
and similar tasks, such as profiling.
Please resist the temptation to use them as a
usual programming tool:
They can be very slow.
Moreover, several of its functions
violate some assumptions about Lua code
(e.g., that variables local to a function
cannot be accessed from outside or
that userdata metatables cannot be changed by Lua code)
and therefore can compromise otherwise secure code.

<p>All functions in this library are provided
inside the <code>debug</code> table.

<p><a name="pdf-debug.debug"></a><hr></hr><h3><code>debug.debug ()</code></h3>

<p>Enters an interactive mode with the user,
running each string that the user enters.
Using simple commands and other debug facilities,
the user can inspect global and local variables,
change their values, evaluate expressions, and so on.
A line containing only the word <code>cont</code> finishes this function,
so that the caller continues its execution.

<p>Note that commands for <code>debug.debug</code> are not lexically nested
within any function, and so have no direct access to local variables.

<p><a name="pdf-debug.getfenv"></a><hr></hr><h3><code>debug.getfenv (o)</code></h3>
Returns the environment of object <code>o</code>.

<p><a name="pdf-debug.gethook"></a><hr></hr><h3><code>debug.gethook ()</code></h3>

<p>Returns the current hook settings, as three values:
the current hook function, the current hook mask,
and the current hook count
(as set by the <a href="#pdf-debug.sethook"><code>debug.sethook</code></a> function).

<p><a name="pdf-debug.getinfo"></a><hr></hr><h3><code>debug.getinfo (function [, what])</code></h3>

<p>Returns a table with information about a function.
You can give the function directly,
or you can give a number as the value of <code>function</code>,
which means the function running at level <code>function</code> of the call stack:
Level 0 is the current function (<code>getinfo</code> itself);
level 1 is the function that called <code>getinfo</code>;
and so on.
If <code>function</code> is a number larger than the number of active functions,
then <code>getinfo</code> returns <b>nil</b>.

<p>The returned table contains all the fields returned by <a href="#lua_getinfo"><code>lua_getinfo</code></a>,
with the string <code>what</code> describing which fields to fill in.
The default for <code>what</code> is to get all information available.
If present,
the option `<code>f</code>&acute;
adds a field named <code>func</code> with the function itself.

<p>For instance, the expression <code>debug.getinfo(1,"n").name</code> returns
a name of the current function, if a reasonable name can be found,
and <code>debug.getinfo(print)</code> returns a table with all available information
about the <a href="#pdf-print"><code>print</code></a> function.

<p><a name="pdf-debug.getlocal"></a><hr></hr><h3><code>debug.getlocal (level, local)</code></h3>

<p>This function returns the name and the value of the local variable
with index <code>local</code> of the function at level <code>level</code> of the stack.
(The first parameter or local variable has index 1, and so on,
until the last active local variable.)
The function returns <b>nil</b> if there is no local
variable with the given index,
and raises an error when called with a <code>level</code> out of range.
(You can call <a href="#pdf-debug.getinfo"><code>debug.getinfo</code></a> to check whether the level is valid.)

<p>Variable names starting with `<code>(</code>&acute; (open parentheses)
represent internal variables
(loop control variables, temporaries, and C function locals).

<p><a name="pdf-debug.getmetatable"></a><hr></hr><h3><code>debug.getmetatable (object)</code></h3>

<p>Returns the metatable of the given <code>object</code>
or <b>nil</b> if it does not have a metatable.

<p><a name="pdf-debug.getregistry"></a><hr></hr><h3><code>debug.getregistry ()</code></h3>

<p>Returns the registry table (see <a href="#registry">3.5</a>).

<p><a name="pdf-debug.getupvalue"></a><hr></hr><h3><code>debug.getupvalue (func, up)</code></h3>

<p>This function returns the name and the value of the upvalue
with index <code>up</code> of the function <code>func</code>.
The function returns <b>nil</b> if there is no upvalue with the given index.

<p><a name="pdf-debug.setfenv"></a><hr></hr><h3><code>debug.setfenv (object, table)</code></h3>

<p>Sets the environment of the given <code>object</code> to the given <code>table</code>.

<p><a name="pdf-debug.sethook"></a><hr></hr><h3><code>debug.sethook (hook, mask [, count])</code></h3>

<p>Sets the given function as a hook.
The string <code>mask</code> and the number <code>count</code> describe
when the hook will be called.
The string mask may have the following characters,
with the given meaning:
<ul>
<li><b><code>"c"</code></b> --- The hook is called every time Lua calls a function;
<li><b><code>"r"</code></b> --- The hook is called every time Lua returns from a function;
<li><b><code>"l"</code></b> --- The hook is called every time Lua enters a new line of code.
</ul>
With a <code>count</code> different from zero,
the hook is called after every <code>count</code> instructions.

<p>When called without arguments,
<a href="#pdf-debug.sethook"><code>debug.sethook</code></a> turns off the hook.

<p>When the hook is called, its first parameter is a string
describing the event that has triggered its call:
<code>"call"</code>, <code>"return"</code> (or <code>"tail return"</code>),
<code>"line"</code>, and <code>"count"</code>.
For line events,
the hook also gets the new line number as its second parameter.
Inside a hook,
you can call <code>getinfo</code> with level 2 to get more information about
the running function
(level 0 is the <code>getinfo</code> function,
and level 1 is the hook function),
unless the event is <code>"tail return"</code>.
In this case, Lua is only simulating the return,
and a call to <code>getinfo</code> will return invalid data.

<p><a name="pdf-debug.setlocal"></a><hr></hr><h3><code>debug.setlocal (level, local, value)</code></h3>

<p>This function assigns the value <code>value</code> to the local variable
with index <code>local</code> of the function at level <code>level</code> of the stack.
The function returns <b>nil</b> if there is no local
variable with the given index,
and raises an error when called with a <code>level</code> out of range.
(You can call <code>getinfo</code> to check whether the level is valid.)
Otherwise, it returns the name of the local variable.

<p><a name="pdf-debug.setmetatable"></a><hr></hr><h3><code>debug.setmetatable (object, table)</code></h3>

<p>Sets the metatable for the given <code>object</code> to the given <code>table</code>
(which can be <b>nil</b>).

<p><a name="pdf-debug.setupvalue"></a><hr></hr><h3><code>debug.setupvalue (func, up, value)</code></h3>

<p>This function assigns the value <code>value</code> to the upvalue
with index <code>up</code> of the function <code>func</code>.
The function returns <b>nil</b> if there is no upvalue
with the given index.
Otherwise, it returns the name of the upvalue.

<p><a name="pdf-debug.traceback"></a><hr></hr><h3><code>debug.traceback ([message])</code></h3>

<p>Returns a string with a traceback of the call stack.
An optional <code>message</code> string is appended
at the beginning of the traceback. 
This function is typically used with <a href="#pdf-xpcall"><code>xpcall</code></a> to produce
better error messages.

<p>
<a name="lua-sa"></a><a name="6"></a><h1>6 - Lua Stand-alone</h1>

<p>Although Lua has been designed as an extension language,
to be embedded in a host C program,
it is also frequently used as a stand-alone language.
An interpreter for Lua as a stand-alone language,
called simply <code>lua</code>,
is provided with the standard distribution.
The stand-alone interpreter includes
all standard libraries, including the debug library.
Its usage is:
<pre>
      lua [options] [script [args]]
</pre>
The options are:
<ul>
<li><b><code>-e</code> <em>stat</em></b> executes string <em>stat</em>;
<li><b><code>-l</code> <em>mod</em></b> "requires" <em>mod</em>;
<li><b><code>-i</code></b> enters interactive mode after running <em>script</em>;
<li><b><code>-v</code></b> prints version information;
<li><b><code>--</code></b> stops handling options;
<li><b><code>-</code> </b> executes <code>stdin</code> as a file and stops handling options.
</ul>
After handling its options, <code>lua</code> runs the given <em>script</em>,
passing to it the given <em>args</em> as string arguments.
When called without arguments,
<code>lua</code> behaves as <code>lua -v -i</code>
when the standard input (<code>stdin</code>) is a terminal,
and as <code>lua -</code> otherwise.

<p>Before running any argument,
the interpreter checks for an environment variable <code>LUA_INIT</code>.
If its format is @<em>filename</em>,
then <code>lua</code> executes the file.
Otherwise, <code>lua</code> executes the string itself.

<p>All options are handled in order, except <code>-i</code>.
For instance, an invocation like
<pre>
       $ lua -e'a=1' -e 'print(a)' script.lua
</pre>
will first set <code>a</code> to 1, then print the value of <code>a</code> (which is `<code>1</code>&acute;),
and finally run the file <code>script.lua</code> with no arguments.
(Here <code>$</code> is the shell prompt. Your prompt may be different.)

<p>Before starting to run the script,
<code>lua</code> collects all arguments in the command line
in a global table called <code>arg</code>.
The script name is stored at index 0,
the first argument after the script name goes to index 1,
and so on.
Any arguments before the script name
(that is, the interpreter name plus the options)
go to negative indices.
For instance, in the call
<pre>
       $ lua -la b.lua t1 t2
</pre>
the interpreter first runs the file <code>a.lua</code>,
then creates a table
<pre>
       arg = { [-2] = "lua", [-1] = "-la",
               [0] = "b.lua",
               [1] = "t1", [2] = "t2" }
</pre>
and finally runs the file <code>b.lua</code>.
The script is called with <code>arg[1]</code>, <code>arg[2]</code>, ...
as arguments;
it can also access these arguments with the vararg expression `<code>...</code>&acute;.

<p>In interactive mode,
if you write an incomplete statement,
the interpreter waits for its completion
by issuing a different prompt.

<p>If the global variable <code>_PROMPT</code> contains a string,
then its value is used as the prompt.
Similarly, if the global variable <code>_PROMPT2</code> contains a string,
its value is used as the secondary prompt
(issued during incomplete statements).
Therefore, both prompts can be changed directly on the command line.
For instance,
<pre>
       $ lua -e"_PROMPT='myprompt> '" -i
</pre>
(the outer pair of quotes is for the shell,
the inner pair is for Lua),
or in any Lua programs by assigning to <code>_PROMPT</code>.
Note the use of <code>-i</code> to enter interactive mode; otherwise,
the program would just end silently right after the assignment to <code>_PROMPT</code>.

<p>To allow the use of Lua as a
script interpreter in Unix systems,
the stand-alone interpreter skips
the first line of a chunk if it starts with <code>#</code>.
Therefore, Lua scripts can be made into executable programs
by using <code>chmod +x</code> and the <code>#!</code> form,
as in
<pre>
#!/usr/local/bin/lua
</pre>
(Of course,
the location of the Lua interpreter may be different in your machine.
If <code>lua</code> is in your <code>PATH</code>,
then 
<pre>
#!/usr/bin/env lua
</pre>
is a more portable solution.) 

<p><hr></hr>

<p><a name="incompat"></a><h1>Incompatibilities with the Previous Version</h1>


<p>Here we list the incompatibilities that may be found when moving a program
from Lua 5.0 to Lua 5.1.
You can avoid most of the incompatibilities compiling Lua with
appropriate options (see file <code>luaconf.h</code>).
However,
all these compatibility options will be removed in the next version of Lua.

<p><h2>Incompatibilities with version 5.0</h2>

<p><h3>Changes in the Language</h3>
<ul>
<li>
The vararg system changed from the pseudo-argument <code>arg</code> with a
table with the extra arguments to the vararg expression.
(Option <code>LUA_COMPAT_VARARG</code> in <code>luaconf.h</code>.)

<p><li>
There was a subtle change in the scope of the implicit
variables of the <b>for</b> statement and for the <b>repeat</b> statement.

<p><li>
The long string/long comment syntax (<code>[[...]]</code>) does not allow nesting.
You can use the new syntax (<code>[=[...]=]</code>) in these cases.
(Option <code>LUA_COMPAT_LSTR</code> in <code>luaconf.h</code>.)

<p></ul>

<p><h3>Changes in the Libraries</h3>
<ul>

<p><li> 
Function <code>string.gfind</code> was renamed <a href="#pdf-string.gmatch"><code>string.gmatch</code></a>.
(Option <code>LUA_COMPAT_GFIND</code>)

<p><li>
When <a href="#pdf-string.gsub"><code>string.gsub</code></a> is called with a function as its
third argument,
whenever this function returns <b>nil</b> or <b>false</b> the
replacement string is the whole match,
instead of the empty string.

<p><li>
Function <code>table.setn</code> was deprecated.
Function <code>table.getn</code> corresponds
to the new length operator (<code>#</code>);
use the operator instead of the function.
(Option <code>LUA_COMPAT_GETN</code>)

<p><li> 
Function <code>loadlib</code> was renamed <a href="#pdf-package.loadlib"><code>package.loadlib</code></a>.
(Option <code>LUA_COMPAT_LOADLIB</code>)

<p><li> 
Function <code>math.mod</code> was renamed <a href="#pdf-math.fmod"><code>math.fmod</code></a>.
(Option <code>LUA_COMPAT_MOD</code>)

<p><li>
Functions <code>table.foreach</code> and <code>table.foreachi</code> are deprecated.
You can use a for loop with <code>pairs</code> or <code>ipairs</code> instead.

<p><li>
There were substantial changes in function <a href="#pdf-require"><code>require</code></a> due to
the new module system.
However, the new behavior is mostly compatible with the old,
but <code>require</code> gets the path from <a href="#pdf-package.path"><code>package.path</code></a> instead
of from <code>LUA_PATH</code>.

<p><li>
Function <a href="#pdf-collectgarbage"><code>collectgarbage</code></a> has different arguments.
Function <code>gcinfo</code> is deprecated;
use <code>collectgarbage("count")</code> instead.

<p></ul>

<p><h3>Changes in the API</h3>
<ul>

<p><li>
The <code>luaopen_*</code> functions (to open libraries)
cannot be called directly,
like a regular C function.
They must be called through Lua,
like a Lua function.

<p><li>
Function <code>lua_open</code> was replaced by <a href="#lua_newstate"><code>lua_newstate</code></a> to
allow the user to set a memory allocation function.
You can use <a href="#luaL_newstate"><code>luaL_newstate</code></a> from the standard library to
create a state with a standard allocation function
(based on <code>realloc</code>).

<p><li>
Functions <code>luaL_getn</code> and <code>luaL_setn</code>
(from the auxiliary library) are deprecated.
Use <a href="#lua_objlen"><code>lua_objlen</code></a> instead of <code>luaL_getn</code>
and nothing instead of <code>luaL_setn</code>.

<p><li>
Function <code>luaL_openlib</code> was replaced by <a href="#luaL_register"><code>luaL_register</code></a>.

<p></ul>

<p>

<a name="BNF"></a><h1>The Complete Syntax of Lua</h1>


<p>Here is the complete syntax of Lua in extended BNF.
It does not describe operator priorities or some syntactical restrictions,
such as <b>return</b> and <b>break</b> statements
can only appear as the <em>last</em> statement of a block.

<p>

<p><pre>

	chunk ::= {stat [`<b>;</b>&acute;]} [laststat[`<b>;</b>&acute;]]

	block ::= chunk

	stat ::=  varlist1 `<b>=</b>&acute; explist1  | 
		 functioncall  | 
		 <b>do</b> block <b>end</b>  | 
		 <b>while</b> exp <b>do</b> block <b>end</b>  | 
		 <b>repeat</b> block <b>until</b> exp  | 
		 <b>if</b> exp <b>then</b> block {<b>elseif</b> exp <b>then</b> block} [<b>else</b> block] <b>end</b>  | 
		 <b>for</b> Name `<b>=</b>&acute; exp `<b>,</b>&acute; exp [`<b>,</b>&acute; exp] <b>do</b> block <b>end</b>  | 
		 <b>for</b> namelist <b>in</b> explist1 <b>do</b> block <b>end</b>  | 
		 <b>function</b> funcname funcbody  | 
		 <b>local</b> <b>function</b> Name funcbody  | 
		 <b>local</b> namelist [`<b>=</b>&acute; explist1] 

	laststat ::= <b>return</b> [explist1]  |  <b>break</b>

	funcname ::= Name {`<b>.</b>&acute; Name} [`<b>:</b>&acute; Name]

	varlist1 ::= var {`<b>,</b>&acute; var}

	var ::=  Name  |  prefixexp `<b>[</b>&acute; exp `<b>]</b>&acute;  |  prefixexp `<b>.</b>&acute; Name 

	namelist ::= Name {`<b>,</b>&acute; Name}

	explist1 ::= {exp `<b>,</b>&acute;} exp

	exp ::=  <b>nil</b>  |  <b>false</b>  |  <b>true</b>  |  Number  |  String  |  `<b>...</b>&acute;  | 
		 function  |  prefixexp  |  tableconstructor  |  exp binop exp  |  unop exp 

	prefixexp ::= var  |  functioncall  |  `<b>(</b>&acute; exp `<b>)</b>&acute;

	functioncall ::=  prefixexp args  |  prefixexp `<b>:</b>&acute; Name args 

	args ::=  `<b>(</b>&acute; [explist1] `<b>)</b>&acute;  |  tableconstructor  |  String 

	function ::= <b>function</b> funcbody

	funcbody ::= `<b>(</b>&acute; [parlist1] `<b>)</b>&acute; block <b>end</b>

	parlist1 ::= namelist [`<b>,</b>&acute; `<b>...</b>&acute;]  |  `<b>...</b>&acute;

	tableconstructor ::= `<b>{</b>&acute; [fieldlist] `<b>}</b>&acute;

	fieldlist ::= field {fieldsep field} [fieldsep]

	field ::= `<b>[</b>&acute; exp `<b>]</b>&acute; `<b>=</b>&acute; exp  |  Name `<b>=</b>&acute; exp  |  exp

	fieldsep ::= `<b>,</b>&acute;  |  `<b>;</b>&acute;

	binop ::= `<b>+</b>&acute;  |  `<b>-</b>&acute;  |  `<b>*</b>&acute;  |  `<b>/</b>&acute;  |  `<b>^</b>&acute;  |  `<b>%</b>&acute;  |  `<b>..</b>&acute;  | 
		 `<b>&#060;</b>&acute;  |  `<b>&#060;=</b>&acute;  |  `<b>></b>&acute;  |  `<b>>=</b>&acute;  |  `<b>==</b>&acute;  |  `<b>~=</b>&acute;  | 
		 <b>and</b>  |  <b>or</b>

	unop ::= `<b>-</b>&acute;  |  <b>not</b>  |  `<b>#</b>&acute;

</pre>

<p>

<p>

</body></html>

