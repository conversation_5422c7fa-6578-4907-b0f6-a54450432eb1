<html>
<head>
<title>pcre_assign_jit_stack specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_assign_jit_stack man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>void pcre_assign_jit_stack(pcre_extra *<i>extra</i>,</b>
<b>     pcre_jit_callback <i>callback</i>, void *<i>data</i>);</b>
<br>
<br>
<b>void pcre16_assign_jit_stack(pcre16_extra *<i>extra</i>,</b>
<b>     pcre16_jit_callback <i>callback</i>, void *<i>data</i>);</b>
<br>
<br>
<b>void pcre32_assign_jit_stack(pcre32_extra *<i>extra</i>,</b>
<b>     pcre32_jit_callback <i>callback</i>, void *<i>data</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function provides control over the memory used as a stack at run-time by a
call to <b>pcre[16|32]_exec()</b> with a pattern that has been successfully
compiled with JIT optimization. The arguments are:
<pre>
  extra     the data pointer returned by <b>pcre[16|32]_study()</b>
  callback  a callback function
  data      a JIT stack or a value to be passed to the callback
              function
</PRE>
</P>
<P>
If <i>callback</i> is NULL and <i>data</i> is NULL, an internal 32K block on
the machine stack is used.
</P>
<P>
If <i>callback</i> is NULL and <i>data</i> is not NULL, <i>data</i> must
be a valid JIT stack, the result of calling <b>pcre[16|32]_jit_stack_alloc()</b>.
</P>
<P>
If <i>callback</i> not NULL, it is called with <i>data</i> as an argument at
the start of matching, in order to set up a JIT stack. If the result is NULL,
the internal 32K stack is used; otherwise the return value must be a valid JIT
stack, the result of calling <b>pcre[16|32]_jit_stack_alloc()</b>.
</P>
<P>
You may safely assign the same JIT stack to multiple patterns, as long as they
are all matched in the same thread. In a multithread application, each thread
must use its own JIT stack. For more details, see the
<a href="pcrejit.html"><b>pcrejit</b></a>
page.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
