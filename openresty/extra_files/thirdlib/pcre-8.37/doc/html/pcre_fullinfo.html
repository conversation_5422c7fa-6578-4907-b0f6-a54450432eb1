<html>
<head>
<title>pcre_fullinfo specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_fullinfo man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>int pcre_fullinfo(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     int <i>what</i>, void *<i>where</i>);</b>
<br>
<br>
<b>int pcre16_fullinfo(const pcre16 *<i>code</i>, const pcre16_extra *<i>extra</i>,</b>
<b>     int <i>what</i>, void *<i>where</i>);</b>
<br>
<br>
<b>int pcre32_fullinfo(const pcre32 *<i>code</i>, const pcre32_extra *<i>extra</i>,</b>
<b>     int <i>what</i>, void *<i>where</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function returns information about a compiled pattern. Its arguments are:
<pre>
  <i>code</i>                      Compiled regular expression
  <i>extra</i>                     Result of <b>pcre[16|32]_study()</b> or NULL
  <i>what</i>                      What information is required
  <i>where</i>                     Where to put the information
</pre>
The following information is available:
<pre>
  PCRE_INFO_BACKREFMAX      Number of highest back reference
  PCRE_INFO_CAPTURECOUNT    Number of capturing subpatterns
  PCRE_INFO_DEFAULT_TABLES  Pointer to default tables
  PCRE_INFO_FIRSTBYTE       Fixed first data unit for a match, or
                              -1 for start of string
                                 or after newline, or
                              -2 otherwise
  PCRE_INFO_FIRSTTABLE      Table of first data units (after studying)
  PCRE_INFO_HASCRORLF       Return 1 if explicit CR or LF matches exist
  PCRE_INFO_JCHANGED        Return 1 if (?J) or (?-J) was used
  PCRE_INFO_JIT             Return 1 after successful JIT compilation
  PCRE_INFO_JITSIZE         Size of JIT compiled code
  PCRE_INFO_LASTLITERAL     Literal last data unit required
  PCRE_INFO_MINLENGTH       Lower bound length of matching strings
  PCRE_INFO_MATCHEMPTY      Return 1 if the pattern can match an empty string,
                               0 otherwise
  PCRE_INFO_MATCHLIMIT      Match limit if set, otherwise PCRE_RROR_UNSET
  PCRE_INFO_MAXLOOKBEHIND   Length (in characters) of the longest lookbehind assertion
  PCRE_INFO_NAMECOUNT       Number of named subpatterns
  PCRE_INFO_NAMEENTRYSIZE   Size of name table entry
  PCRE_INFO_NAMETABLE       Pointer to name table
  PCRE_INFO_OKPARTIAL       Return 1 if partial matching can be tried
                              (always returns 1 after release 8.00)
  PCRE_INFO_OPTIONS         Option bits used for compilation
  PCRE_INFO_SIZE            Size of compiled pattern
  PCRE_INFO_STUDYSIZE       Size of study data
  PCRE_INFO_FIRSTCHARACTER      Fixed first data unit for a match
  PCRE_INFO_FIRSTCHARACTERFLAGS Returns
                                  1 if there is a first data character set, which can
                                    then be retrieved using PCRE_INFO_FIRSTCHARACTER,
                                  2 if the first character is at the start of the data
                                    string or after a newline, and
                                  0 otherwise
  PCRE_INFO_RECURSIONLIMIT    Recursion limit if set, otherwise PCRE_ERROR_UNSET
  PCRE_INFO_REQUIREDCHAR      Literal last data unit required
  PCRE_INFO_REQUIREDCHARFLAGS Returns 1 if the last data character is set (which can then
                              be retrieved using PCRE_INFO_REQUIREDCHAR); 0 otherwise
</pre>
The <i>where</i> argument must point to an integer variable, except for the
following <i>what</i> values:
<pre>
  PCRE_INFO_DEFAULT_TABLES  const uint8_t *
  PCRE_INFO_FIRSTCHARACTER  uint32_t
  PCRE_INFO_FIRSTTABLE      const uint8_t *
  PCRE_INFO_JITSIZE         size_t
  PCRE_INFO_MATCHLIMIT      uint32_t
  PCRE_INFO_NAMETABLE       PCRE_SPTR16           (16-bit library)
  PCRE_INFO_NAMETABLE       PCRE_SPTR32           (32-bit library)
  PCRE_INFO_NAMETABLE       const unsigned char * (8-bit library)
  PCRE_INFO_OPTIONS         unsigned long int
  PCRE_INFO_SIZE            size_t
  PCRE_INFO_STUDYSIZE       size_t
  PCRE_INFO_RECURSIONLIMIT  uint32_t
  PCRE_INFO_REQUIREDCHAR    uint32_t
</pre>
The yield of the function is zero on success or:
<pre>
  PCRE_ERROR_NULL           the argument <i>code</i> was NULL
                            the argument <i>where</i> was NULL
  PCRE_ERROR_BADMAGIC       the "magic number" was not found
  PCRE_ERROR_BADOPTION      the value of <i>what</i> was invalid
  PCRE_ERROR_UNSET          the option was not set
</PRE>
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
