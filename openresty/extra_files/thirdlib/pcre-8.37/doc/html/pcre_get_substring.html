<html>
<head>
<title>pcre_get_substring specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_get_substring man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>int pcre_get_substring(const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>,</b>
<b>     const char **<i>stringptr</i>);</b>
<br>
<br>
<b>int pcre16_get_substring(PCRE_SPTR16 <i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>,</b>
<b>     PCRE_SPTR16 *<i>stringptr</i>);</b>
<br>
<br>
<b>int pcre32_get_substring(PCRE_SPTR32 <i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>,</b>
<b>     PCRE_SPTR32 *<i>stringptr</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This is a convenience function for extracting a captured substring. The
arguments are:
<pre>
  <i>subject</i>       Subject that has been successfully matched
  <i>ovector</i>       Offset vector that <b>pcre[16|32]_exec()</b> used
  <i>stringcount</i>   Value returned by <b>pcre[16|32]_exec()</b>
  <i>stringnumber</i>  Number of the required substring
  <i>stringptr</i>     Where to put the string pointer
</pre>
The memory in which the substring is placed is obtained by calling
<b>pcre[16|32]_malloc()</b>. The convenience function
<b>pcre[16|32]_free_substring()</b> can be used to free it when it is no longer
needed. The yield of the function is the length of the substring,
PCRE_ERROR_NOMEMORY if sufficient memory could not be obtained, or
PCRE_ERROR_NOSUBSTRING if the string number is invalid.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
