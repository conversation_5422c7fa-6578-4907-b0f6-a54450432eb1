<html>
<head>
<title>pcre_jit_stack_alloc specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_jit_stack_alloc man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>pcre_jit_stack *pcre_jit_stack_alloc(int <i>startsize</i>,</b>
<b>     int <i>maxsize</i>);</b>
<br>
<br>
<b>pcre16_jit_stack *pcre16_jit_stack_alloc(int <i>startsize</i>,</b>
<b>     int <i>maxsize</i>);</b>
<br>
<br>
<b>pcre32_jit_stack *pcre32_jit_stack_alloc(int <i>startsize</i>,</b>
<b>     int <i>maxsize</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function is used to create a stack for use by the code compiled by the JIT
optimization of <b>pcre[16|32]_study()</b>. The arguments are a starting size for
the stack, and a maximum size to which it is allowed to grow. The result can be
passed to the JIT run-time code by <b>pcre[16|32]_assign_jit_stack()</b>, or that
function can set up a callback for obtaining a stack. A maximum stack size of
512K to 1M should be more than enough for any pattern. For more details, see
the
<a href="pcrejit.html"><b>pcrejit</b></a>
page.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
