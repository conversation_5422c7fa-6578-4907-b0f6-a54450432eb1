<html>
<head>
<title>pcre_jit_stack_free specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcre_jit_stack_free man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
SYNOPSIS
</b><br>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<P>
<b>void pcre_jit_stack_free(pcre_jit_stack *<i>stack</i>);</b>
</P>
<P>
<b>void pcre16_jit_stack_free(pcre16_jit_stack *<i>stack</i>);</b>
</P>
<P>
<b>void pcre32_jit_stack_free(pcre32_jit_stack *<i>stack</i>);</b>
</P>
<br><b>
DESCRIPTION
</b><br>
<P>
This function is used to free a JIT stack that was created by
<b>pcre[16|32]_jit_stack_alloc()</b> when it is no longer needed. For more details,
see the
<a href="pcrejit.html"><b>pcrejit</b></a>
page.
</P>
<P>
There is a complete description of the PCRE native API in the
<a href="pcreapi.html"><b>pcreapi</b></a>
page and a description of the POSIX API in the
<a href="pcreposix.html"><b>pcreposix</b></a>
page.
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
