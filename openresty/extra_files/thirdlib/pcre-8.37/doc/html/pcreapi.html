<html>
<head>
<title>pcreapi specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcreapi man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<ul>
<li><a name="TOC1" href="#SEC1">PCRE NATIVE API BASIC FUNCTIONS</a>
<li><a name="TOC2" href="#SEC2">PCRE NATIVE API STRING EXTRACTION FUNCTIONS</a>
<li><a name="TOC3" href="#SEC3">PCRE NATIVE API AUXILIARY FUNCTIONS</a>
<li><a name="TOC4" href="#SEC4">PCRE NATIVE API INDIRECTED FUNCTIONS</a>
<li><a name="TOC5" href="#SEC5">PCRE 8-BIT, 16-BIT, AND 32-BIT LIBRARIES</a>
<li><a name="TOC6" href="#SEC6">PCRE API OVERVIEW</a>
<li><a name="TOC7" href="#SEC7">NEWLINES</a>
<li><a name="TOC8" href="#SEC8">MULTITHREADING</a>
<li><a name="TOC9" href="#SEC9">SAVING PRECOMPILED PATTERNS FOR LATER USE</a>
<li><a name="TOC10" href="#SEC10">CHECKING BUILD-TIME OPTIONS</a>
<li><a name="TOC11" href="#SEC11">COMPILING A PATTERN</a>
<li><a name="TOC12" href="#SEC12">COMPILATION ERROR CODES</a>
<li><a name="TOC13" href="#SEC13">STUDYING A PATTERN</a>
<li><a name="TOC14" href="#SEC14">LOCALE SUPPORT</a>
<li><a name="TOC15" href="#SEC15">INFORMATION ABOUT A PATTERN</a>
<li><a name="TOC16" href="#SEC16">REFERENCE COUNTS</a>
<li><a name="TOC17" href="#SEC17">MATCHING A PATTERN: THE TRADITIONAL FUNCTION</a>
<li><a name="TOC18" href="#SEC18">EXTRACTING CAPTURED SUBSTRINGS BY NUMBER</a>
<li><a name="TOC19" href="#SEC19">EXTRACTING CAPTURED SUBSTRINGS BY NAME</a>
<li><a name="TOC20" href="#SEC20">DUPLICATE SUBPATTERN NAMES</a>
<li><a name="TOC21" href="#SEC21">FINDING ALL POSSIBLE MATCHES</a>
<li><a name="TOC22" href="#SEC22">OBTAINING AN ESTIMATE OF STACK USAGE</a>
<li><a name="TOC23" href="#SEC23">MATCHING A PATTERN: THE ALTERNATIVE FUNCTION</a>
<li><a name="TOC24" href="#SEC24">SEE ALSO</a>
<li><a name="TOC25" href="#SEC25">AUTHOR</a>
<li><a name="TOC26" href="#SEC26">REVISION</a>
</ul>
<P>
<b>#include &#60;pcre.h&#62;</b>
</P>
<br><a name="SEC1" href="#TOC1">PCRE NATIVE API BASIC FUNCTIONS</a><br>
<P>
<b>pcre *pcre_compile(const char *<i>pattern</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
<br>
<br>
<b>pcre *pcre_compile2(const char *<i>pattern</i>, int <i>options</i>,</b>
<b>     int *<i>errorcodeptr</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
<br>
<br>
<b>pcre_extra *pcre_study(const pcre *<i>code</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>);</b>
<br>
<br>
<b>void pcre_free_study(pcre_extra *<i>extra</i>);</b>
<br>
<br>
<b>int pcre_exec(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     const char *<i>subject</i>, int <i>length</i>, int <i>startoffset</i>,</b>
<b>     int <i>options</i>, int *<i>ovector</i>, int <i>ovecsize</i>);</b>
<br>
<br>
<b>int pcre_dfa_exec(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     const char *<i>subject</i>, int <i>length</i>, int <i>startoffset</i>,</b>
<b>     int <i>options</i>, int *<i>ovector</i>, int <i>ovecsize</i>,</b>
<b>     int *<i>workspace</i>, int <i>wscount</i>);</b>
</P>
<br><a name="SEC2" href="#TOC1">PCRE NATIVE API STRING EXTRACTION FUNCTIONS</a><br>
<P>
<b>int pcre_copy_named_substring(const pcre *<i>code</i>,</b>
<b>     const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, const char *<i>stringname</i>,</b>
<b>     char *<i>buffer</i>, int <i>buffersize</i>);</b>
<br>
<br>
<b>int pcre_copy_substring(const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>, char *<i>buffer</i>,</b>
<b>     int <i>buffersize</i>);</b>
<br>
<br>
<b>int pcre_get_named_substring(const pcre *<i>code</i>,</b>
<b>     const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, const char *<i>stringname</i>,</b>
<b>     const char **<i>stringptr</i>);</b>
<br>
<br>
<b>int pcre_get_stringnumber(const pcre *<i>code</i>,</b>
<b>     const char *<i>name</i>);</b>
<br>
<br>
<b>int pcre_get_stringtable_entries(const pcre *<i>code</i>,</b>
<b>     const char *<i>name</i>, char **<i>first</i>, char **<i>last</i>);</b>
<br>
<br>
<b>int pcre_get_substring(const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>,</b>
<b>     const char **<i>stringptr</i>);</b>
<br>
<br>
<b>int pcre_get_substring_list(const char *<i>subject</i>,</b>
<b>     int *<i>ovector</i>, int <i>stringcount</i>, const char ***<i>listptr</i>);</b>
<br>
<br>
<b>void pcre_free_substring(const char *<i>stringptr</i>);</b>
<br>
<br>
<b>void pcre_free_substring_list(const char **<i>stringptr</i>);</b>
</P>
<br><a name="SEC3" href="#TOC1">PCRE NATIVE API AUXILIARY FUNCTIONS</a><br>
<P>
<b>int pcre_jit_exec(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     const char *<i>subject</i>, int <i>length</i>, int <i>startoffset</i>,</b>
<b>     int <i>options</i>, int *<i>ovector</i>, int <i>ovecsize</i>,</b>
<b>     pcre_jit_stack *<i>jstack</i>);</b>
<br>
<br>
<b>pcre_jit_stack *pcre_jit_stack_alloc(int <i>startsize</i>, int <i>maxsize</i>);</b>
<br>
<br>
<b>void pcre_jit_stack_free(pcre_jit_stack *<i>stack</i>);</b>
<br>
<br>
<b>void pcre_assign_jit_stack(pcre_extra *<i>extra</i>,</b>
<b>     pcre_jit_callback <i>callback</i>, void *<i>data</i>);</b>
<br>
<br>
<b>const unsigned char *pcre_maketables(void);</b>
<br>
<br>
<b>int pcre_fullinfo(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     int <i>what</i>, void *<i>where</i>);</b>
<br>
<br>
<b>int pcre_refcount(pcre *<i>code</i>, int <i>adjust</i>);</b>
<br>
<br>
<b>int pcre_config(int <i>what</i>, void *<i>where</i>);</b>
<br>
<br>
<b>const char *pcre_version(void);</b>
<br>
<br>
<b>int pcre_pattern_to_host_byte_order(pcre *<i>code</i>,</b>
<b>     pcre_extra *<i>extra</i>, const unsigned char *<i>tables</i>);</b>
</P>
<br><a name="SEC4" href="#TOC1">PCRE NATIVE API INDIRECTED FUNCTIONS</a><br>
<P>
<b>void *(*pcre_malloc)(size_t);</b>
<br>
<br>
<b>void (*pcre_free)(void *);</b>
<br>
<br>
<b>void *(*pcre_stack_malloc)(size_t);</b>
<br>
<br>
<b>void (*pcre_stack_free)(void *);</b>
<br>
<br>
<b>int (*pcre_callout)(pcre_callout_block *);</b>
<br>
<br>
<b>int (*pcre_stack_guard)(void);</b>
</P>
<br><a name="SEC5" href="#TOC1">PCRE 8-BIT, 16-BIT, AND 32-BIT LIBRARIES</a><br>
<P>
As well as support for 8-bit character strings, PCRE also supports 16-bit
strings (from release 8.30) and 32-bit strings (from release 8.32), by means of
two additional libraries. They can be built as well as, or instead of, the
8-bit library. To avoid too much complication, this document describes the
8-bit versions of the functions, with only occasional references to the 16-bit
and 32-bit libraries.
</P>
<P>
The 16-bit and 32-bit functions operate in the same way as their 8-bit
counterparts; they just use different data types for their arguments and
results, and their names start with <b>pcre16_</b> or <b>pcre32_</b> instead of
<b>pcre_</b>. For every option that has UTF8 in its name (for example,
PCRE_UTF8), there are corresponding 16-bit and 32-bit names with UTF8 replaced
by UTF16 or UTF32, respectively. This facility is in fact just cosmetic; the
16-bit and 32-bit option names define the same bit values.
</P>
<P>
References to bytes and UTF-8 in this document should be read as references to
16-bit data units and UTF-16 when using the 16-bit library, or 32-bit data
units and UTF-32 when using the 32-bit library, unless specified otherwise.
More details of the specific differences for the 16-bit and 32-bit libraries
are given in the
<a href="pcre16.html"><b>pcre16</b></a>
and
<a href="pcre32.html"><b>pcre32</b></a>
pages.
</P>
<br><a name="SEC6" href="#TOC1">PCRE API OVERVIEW</a><br>
<P>
PCRE has its own native API, which is described in this document. There are
also some wrapper functions (for the 8-bit library only) that correspond to the
POSIX regular expression API, but they do not give access to all the
functionality. They are described in the
<a href="pcreposix.html"><b>pcreposix</b></a>
documentation. Both of these APIs define a set of C function calls. A C++
wrapper (again for the 8-bit library only) is also distributed with PCRE. It is
documented in the
<a href="pcrecpp.html"><b>pcrecpp</b></a>
page.
</P>
<P>
The native API C function prototypes are defined in the header file
<b>pcre.h</b>, and on Unix-like systems the (8-bit) library itself is called
<b>libpcre</b>. It can normally be accessed by adding <b>-lpcre</b> to the
command for linking an application that uses PCRE. The header file defines the
macros PCRE_MAJOR and PCRE_MINOR to contain the major and minor release numbers
for the library. Applications can use these to include support for different
releases of PCRE.
</P>
<P>
In a Windows environment, if you want to statically link an application program
against a non-dll <b>pcre.a</b> file, you must define PCRE_STATIC before
including <b>pcre.h</b> or <b>pcrecpp.h</b>, because otherwise the
<b>pcre_malloc()</b> and <b>pcre_free()</b> exported functions will be declared
<b>__declspec(dllimport)</b>, with unwanted results.
</P>
<P>
The functions <b>pcre_compile()</b>, <b>pcre_compile2()</b>, <b>pcre_study()</b>,
and <b>pcre_exec()</b> are used for compiling and matching regular expressions
in a Perl-compatible manner. A sample program that demonstrates the simplest
way of using them is provided in the file called <i>pcredemo.c</i> in the PCRE
source distribution. A listing of this program is given in the
<a href="pcredemo.html"><b>pcredemo</b></a>
documentation, and the
<a href="pcresample.html"><b>pcresample</b></a>
documentation describes how to compile and run it.
</P>
<P>
Just-in-time compiler support is an optional feature of PCRE that can be built
in appropriate hardware environments. It greatly speeds up the matching
performance of many patterns. Simple programs can easily request that it be
used if available, by setting an option that is ignored when it is not
relevant. More complicated programs might need to make use of the functions
<b>pcre_jit_stack_alloc()</b>, <b>pcre_jit_stack_free()</b>, and
<b>pcre_assign_jit_stack()</b> in order to control the JIT code's memory usage.
</P>
<P>
From release 8.32 there is also a direct interface for JIT execution, which
gives improved performance. The JIT-specific functions are discussed in the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation.
</P>
<P>
A second matching function, <b>pcre_dfa_exec()</b>, which is not
Perl-compatible, is also provided. This uses a different algorithm for the
matching. The alternative algorithm finds all possible matches (at a given
point in the subject), and scans the subject just once (unless there are
lookbehind assertions). However, this algorithm does not return captured
substrings. A description of the two matching algorithms and their advantages
and disadvantages is given in the
<a href="pcrematching.html"><b>pcrematching</b></a>
documentation.
</P>
<P>
In addition to the main compiling and matching functions, there are convenience
functions for extracting captured substrings from a subject string that is
matched by <b>pcre_exec()</b>. They are:
<pre>
  <b>pcre_copy_substring()</b>
  <b>pcre_copy_named_substring()</b>
  <b>pcre_get_substring()</b>
  <b>pcre_get_named_substring()</b>
  <b>pcre_get_substring_list()</b>
  <b>pcre_get_stringnumber()</b>
  <b>pcre_get_stringtable_entries()</b>
</pre>
<b>pcre_free_substring()</b> and <b>pcre_free_substring_list()</b> are also
provided, to free the memory used for extracted strings.
</P>
<P>
The function <b>pcre_maketables()</b> is used to build a set of character tables
in the current locale for passing to <b>pcre_compile()</b>, <b>pcre_exec()</b>,
or <b>pcre_dfa_exec()</b>. This is an optional facility that is provided for
specialist use. Most commonly, no special tables are passed, in which case
internal tables that are generated when PCRE is built are used.
</P>
<P>
The function <b>pcre_fullinfo()</b> is used to find out information about a
compiled pattern. The function <b>pcre_version()</b> returns a pointer to a
string containing the version of PCRE and its date of release.
</P>
<P>
The function <b>pcre_refcount()</b> maintains a reference count in a data block
containing a compiled pattern. This is provided for the benefit of
object-oriented applications.
</P>
<P>
The global variables <b>pcre_malloc</b> and <b>pcre_free</b> initially contain
the entry points of the standard <b>malloc()</b> and <b>free()</b> functions,
respectively. PCRE calls the memory management functions via these variables,
so a calling program can replace them if it wishes to intercept the calls. This
should be done before calling any PCRE functions.
</P>
<P>
The global variables <b>pcre_stack_malloc</b> and <b>pcre_stack_free</b> are also
indirections to memory management functions. These special functions are used
only when PCRE is compiled to use the heap for remembering data, instead of
recursive function calls, when running the <b>pcre_exec()</b> function. See the
<a href="pcrebuild.html"><b>pcrebuild</b></a>
documentation for details of how to do this. It is a non-standard way of
building PCRE, for use in environments that have limited stacks. Because of the
greater use of memory management, it runs more slowly. Separate functions are
provided so that special-purpose external code can be used for this case. When
used, these functions are always called in a stack-like manner (last obtained,
first freed), and always for memory blocks of the same size. There is a
discussion about PCRE's stack usage in the
<a href="pcrestack.html"><b>pcrestack</b></a>
documentation.
</P>
<P>
The global variable <b>pcre_callout</b> initially contains NULL. It can be set
by the caller to a "callout" function, which PCRE will then call at specified
points during a matching operation. Details are given in the
<a href="pcrecallout.html"><b>pcrecallout</b></a>
documentation.
</P>
<P>
The global variable <b>pcre_stack_guard</b> initially contains NULL. It can be
set by the caller to a function that is called by PCRE whenever it starts
to compile a parenthesized part of a pattern. When parentheses are nested, PCRE
uses recursive function calls, which use up the system stack. This function is
provided so that applications with restricted stacks can force a compilation
error if the stack runs out. The function should return zero if all is well, or
non-zero to force an error.
<a name="newlines"></a></P>
<br><a name="SEC7" href="#TOC1">NEWLINES</a><br>
<P>
PCRE supports five different conventions for indicating line breaks in
strings: a single CR (carriage return) character, a single LF (linefeed)
character, the two-character sequence CRLF, any of the three preceding, or any
Unicode newline sequence. The Unicode newline sequences are the three just
mentioned, plus the single characters VT (vertical tab, U+000B), FF (form feed,
U+000C), NEL (next line, U+0085), LS (line separator, U+2028), and PS
(paragraph separator, U+2029).
</P>
<P>
Each of the first three conventions is used by at least one operating system as
its standard newline sequence. When PCRE is built, a default can be specified.
The default default is LF, which is the Unix standard. When PCRE is run, the
default can be overridden, either when a pattern is compiled, or when it is
matched.
</P>
<P>
At compile time, the newline convention can be specified by the <i>options</i>
argument of <b>pcre_compile()</b>, or it can be specified by special text at the
start of the pattern itself; this overrides any other settings. See the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
page for details of the special character sequences.
</P>
<P>
In the PCRE documentation the word "newline" is used to mean "the character or
pair of characters that indicate a line break". The choice of newline
convention affects the handling of the dot, circumflex, and dollar
metacharacters, the handling of #-comments in /x mode, and, when CRLF is a
recognized line ending sequence, the match position advancement for a
non-anchored pattern. There is more detail about this in the
<a href="#execoptions">section on <b>pcre_exec()</b> options</a>
below.
</P>
<P>
The choice of newline convention does not affect the interpretation of
the \n or \r escape sequences, nor does it affect what \R matches, which is
controlled in a similar way, but by separate options.
</P>
<br><a name="SEC8" href="#TOC1">MULTITHREADING</a><br>
<P>
The PCRE functions can be used in multi-threading applications, with the
proviso that the memory management functions pointed to by <b>pcre_malloc</b>,
<b>pcre_free</b>, <b>pcre_stack_malloc</b>, and <b>pcre_stack_free</b>, and the
callout and stack-checking functions pointed to by <b>pcre_callout</b> and
<b>pcre_stack_guard</b>, are shared by all threads.
</P>
<P>
The compiled form of a regular expression is not altered during matching, so
the same compiled pattern can safely be used by several threads at once.
</P>
<P>
If the just-in-time optimization feature is being used, it needs separate
memory stack areas for each thread. See the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation for more details.
</P>
<br><a name="SEC9" href="#TOC1">SAVING PRECOMPILED PATTERNS FOR LATER USE</a><br>
<P>
The compiled form of a regular expression can be saved and re-used at a later
time, possibly by a different program, and even on a host other than the one on
which it was compiled. Details are given in the
<a href="pcreprecompile.html"><b>pcreprecompile</b></a>
documentation, which includes a description of the
<b>pcre_pattern_to_host_byte_order()</b> function. However, compiling a regular
expression with one version of PCRE for use with a different version is not
guaranteed to work and may cause crashes.
</P>
<br><a name="SEC10" href="#TOC1">CHECKING BUILD-TIME OPTIONS</a><br>
<P>
<b>int pcre_config(int <i>what</i>, void *<i>where</i>);</b>
</P>
<P>
The function <b>pcre_config()</b> makes it possible for a PCRE client to
discover which optional features have been compiled into the PCRE library. The
<a href="pcrebuild.html"><b>pcrebuild</b></a>
documentation has more details about these optional features.
</P>
<P>
The first argument for <b>pcre_config()</b> is an integer, specifying which
information is required; the second argument is a pointer to a variable into
which the information is placed. The returned value is zero on success, or the
negative error code PCRE_ERROR_BADOPTION if the value in the first argument is
not recognized. The following information is available:
<pre>
  PCRE_CONFIG_UTF8
</pre>
The output is an integer that is set to one if UTF-8 support is available;
otherwise it is set to zero. This value should normally be given to the 8-bit
version of this function, <b>pcre_config()</b>. If it is given to the 16-bit
or 32-bit version of this function, the result is PCRE_ERROR_BADOPTION.
<pre>
  PCRE_CONFIG_UTF16
</pre>
The output is an integer that is set to one if UTF-16 support is available;
otherwise it is set to zero. This value should normally be given to the 16-bit
version of this function, <b>pcre16_config()</b>. If it is given to the 8-bit
or 32-bit version of this function, the result is PCRE_ERROR_BADOPTION.
<pre>
  PCRE_CONFIG_UTF32
</pre>
The output is an integer that is set to one if UTF-32 support is available;
otherwise it is set to zero. This value should normally be given to the 32-bit
version of this function, <b>pcre32_config()</b>. If it is given to the 8-bit
or 16-bit version of this function, the result is PCRE_ERROR_BADOPTION.
<pre>
  PCRE_CONFIG_UNICODE_PROPERTIES
</pre>
The output is an integer that is set to one if support for Unicode character
properties is available; otherwise it is set to zero.
<pre>
  PCRE_CONFIG_JIT
</pre>
The output is an integer that is set to one if support for just-in-time
compiling is available; otherwise it is set to zero.
<pre>
  PCRE_CONFIG_JITTARGET
</pre>
The output is a pointer to a zero-terminated "const char *" string. If JIT
support is available, the string contains the name of the architecture for
which the JIT compiler is configured, for example "x86 32bit (little endian +
unaligned)". If JIT support is not available, the result is NULL.
<pre>
  PCRE_CONFIG_NEWLINE
</pre>
The output is an integer whose value specifies the default character sequence
that is recognized as meaning "newline". The values that are supported in
ASCII/Unicode environments are: 10 for LF, 13 for CR, 3338 for CRLF, -2 for
ANYCRLF, and -1 for ANY. In EBCDIC environments, CR, ANYCRLF, and ANY yield the
same values. However, the value for LF is normally 21, though some EBCDIC
environments use 37. The corresponding values for CRLF are 3349 and 3365. The
default should normally correspond to the standard sequence for your operating
system.
<pre>
  PCRE_CONFIG_BSR
</pre>
The output is an integer whose value indicates what character sequences the \R
escape sequence matches by default. A value of 0 means that \R matches any
Unicode line ending sequence; a value of 1 means that \R matches only CR, LF,
or CRLF. The default can be overridden when a pattern is compiled or matched.
<pre>
  PCRE_CONFIG_LINK_SIZE
</pre>
The output is an integer that contains the number of bytes used for internal
linkage in compiled regular expressions. For the 8-bit library, the value can
be 2, 3, or 4. For the 16-bit library, the value is either 2 or 4 and is still
a number of bytes. For the 32-bit library, the value is either 2 or 4 and is
still a number of bytes. The default value of 2 is sufficient for all but the
most massive patterns, since it allows the compiled pattern to be up to 64K in
size. Larger values allow larger regular expressions to be compiled, at the
expense of slower matching.
<pre>
  PCRE_CONFIG_POSIX_MALLOC_THRESHOLD
</pre>
The output is an integer that contains the threshold above which the POSIX
interface uses <b>malloc()</b> for output vectors. Further details are given in
the
<a href="pcreposix.html"><b>pcreposix</b></a>
documentation.
<pre>
  PCRE_CONFIG_PARENS_LIMIT
</pre>
The output is a long integer that gives the maximum depth of nesting of
parentheses (of any kind) in a pattern. This limit is imposed to cap the amount
of system stack used when a pattern is compiled. It is specified when PCRE is
built; the default is 250. This limit does not take into account the stack that
may already be used by the calling application. For finer control over
compilation stack usage, you can set a pointer to an external checking function
in <b>pcre_stack_guard</b>.
<pre>
  PCRE_CONFIG_MATCH_LIMIT
</pre>
The output is a long integer that gives the default limit for the number of
internal matching function calls in a <b>pcre_exec()</b> execution. Further
details are given with <b>pcre_exec()</b> below.
<pre>
  PCRE_CONFIG_MATCH_LIMIT_RECURSION
</pre>
The output is a long integer that gives the default limit for the depth of
recursion when calling the internal matching function in a <b>pcre_exec()</b>
execution. Further details are given with <b>pcre_exec()</b> below.
<pre>
  PCRE_CONFIG_STACKRECURSE
</pre>
The output is an integer that is set to one if internal recursion when running
<b>pcre_exec()</b> is implemented by recursive function calls that use the stack
to remember their state. This is the usual way that PCRE is compiled. The
output is zero if PCRE was compiled to use blocks of data on the heap instead
of recursive function calls. In this case, <b>pcre_stack_malloc</b> and
<b>pcre_stack_free</b> are called to manage memory blocks on the heap, thus
avoiding the use of the stack.
</P>
<br><a name="SEC11" href="#TOC1">COMPILING A PATTERN</a><br>
<P>
<b>pcre *pcre_compile(const char *<i>pattern</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
<br>
<br>
<b>pcre *pcre_compile2(const char *<i>pattern</i>, int <i>options</i>,</b>
<b>     int *<i>errorcodeptr</i>,</b>
<b>     const char **<i>errptr</i>, int *<i>erroffset</i>,</b>
<b>     const unsigned char *<i>tableptr</i>);</b>
</P>
<P>
Either of the functions <b>pcre_compile()</b> or <b>pcre_compile2()</b> can be
called to compile a pattern into an internal form. The only difference between
the two interfaces is that <b>pcre_compile2()</b> has an additional argument,
<i>errorcodeptr</i>, via which a numerical error code can be returned. To avoid
too much repetition, we refer just to <b>pcre_compile()</b> below, but the
information applies equally to <b>pcre_compile2()</b>.
</P>
<P>
The pattern is a C string terminated by a binary zero, and is passed in the
<i>pattern</i> argument. A pointer to a single block of memory that is obtained
via <b>pcre_malloc</b> is returned. This contains the compiled code and related
data. The <b>pcre</b> type is defined for the returned block; this is a typedef
for a structure whose contents are not externally defined. It is up to the
caller to free the memory (via <b>pcre_free</b>) when it is no longer required.
</P>
<P>
Although the compiled code of a PCRE regex is relocatable, that is, it does not
depend on memory location, the complete <b>pcre</b> data block is not
fully relocatable, because it may contain a copy of the <i>tableptr</i>
argument, which is an address (see below).
</P>
<P>
The <i>options</i> argument contains various bit settings that affect the
compilation. It should be zero if no options are required. The available
options are described below. Some of them (in particular, those that are
compatible with Perl, but some others as well) can also be set and unset from
within the pattern (see the detailed description in the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
documentation). For those options that can be different in different parts of
the pattern, the contents of the <i>options</i> argument specifies their
settings at the start of compilation and execution. The PCRE_ANCHORED,
PCRE_BSR_<i>xxx</i>, PCRE_NEWLINE_<i>xxx</i>, PCRE_NO_UTF8_CHECK, and
PCRE_NO_START_OPTIMIZE options can be set at the time of matching as well as at
compile time.
</P>
<P>
If <i>errptr</i> is NULL, <b>pcre_compile()</b> returns NULL immediately.
Otherwise, if compilation of a pattern fails, <b>pcre_compile()</b> returns
NULL, and sets the variable pointed to by <i>errptr</i> to point to a textual
error message. This is a static string that is part of the library. You must
not try to free it. Normally, the offset from the start of the pattern to the
data unit that was being processed when the error was discovered is placed in
the variable pointed to by <i>erroffset</i>, which must not be NULL (if it is,
an immediate error is given). However, for an invalid UTF-8 or UTF-16 string,
the offset is that of the first data unit of the failing character.
</P>
<P>
Some errors are not detected until the whole pattern has been scanned; in these
cases, the offset passed back is the length of the pattern. Note that the
offset is in data units, not characters, even in a UTF mode. It may sometimes
point into the middle of a UTF-8 or UTF-16 character.
</P>
<P>
If <b>pcre_compile2()</b> is used instead of <b>pcre_compile()</b>, and the
<i>errorcodeptr</i> argument is not NULL, a non-zero error code number is
returned via this argument in the event of an error. This is in addition to the
textual error message. Error codes and messages are listed below.
</P>
<P>
If the final argument, <i>tableptr</i>, is NULL, PCRE uses a default set of
character tables that are built when PCRE is compiled, using the default C
locale. Otherwise, <i>tableptr</i> must be an address that is the result of a
call to <b>pcre_maketables()</b>. This value is stored with the compiled
pattern, and used again by <b>pcre_exec()</b> and <b>pcre_dfa_exec()</b> when the
pattern is matched. For more discussion, see the section on locale support
below.
</P>
<P>
This code fragment shows a typical straightforward call to <b>pcre_compile()</b>:
<pre>
  pcre *re;
  const char *error;
  int erroffset;
  re = pcre_compile(
    "^A.*Z",          /* the pattern */
    0,                /* default options */
    &error,           /* for error message */
    &erroffset,       /* for error offset */
    NULL);            /* use default character tables */
</pre>
The following names for option bits are defined in the <b>pcre.h</b> header
file:
<pre>
  PCRE_ANCHORED
</pre>
If this bit is set, the pattern is forced to be "anchored", that is, it is
constrained to match only at the first matching point in the string that is
being searched (the "subject string"). This effect can also be achieved by
appropriate constructs in the pattern itself, which is the only way to do it in
Perl.
<pre>
  PCRE_AUTO_CALLOUT
</pre>
If this bit is set, <b>pcre_compile()</b> automatically inserts callout items,
all with number 255, before each pattern item. For discussion of the callout
facility, see the
<a href="pcrecallout.html"><b>pcrecallout</b></a>
documentation.
<pre>
  PCRE_BSR_ANYCRLF
  PCRE_BSR_UNICODE
</pre>
These options (which are mutually exclusive) control what the \R escape
sequence matches. The choice is either to match only CR, LF, or CRLF, or to
match any Unicode newline sequence. The default is specified when PCRE is
built. It can be overridden from within the pattern, or by setting an option
when a compiled pattern is matched.
<pre>
  PCRE_CASELESS
</pre>
If this bit is set, letters in the pattern match both upper and lower case
letters. It is equivalent to Perl's /i option, and it can be changed within a
pattern by a (?i) option setting. In UTF-8 mode, PCRE always understands the
concept of case for characters whose values are less than 128, so caseless
matching is always possible. For characters with higher values, the concept of
case is supported if PCRE is compiled with Unicode property support, but not
otherwise. If you want to use caseless matching for characters 128 and above,
you must ensure that PCRE is compiled with Unicode property support as well as
with UTF-8 support.
<pre>
  PCRE_DOLLAR_ENDONLY
</pre>
If this bit is set, a dollar metacharacter in the pattern matches only at the
end of the subject string. Without this option, a dollar also matches
immediately before a newline at the end of the string (but not before any other
newlines). The PCRE_DOLLAR_ENDONLY option is ignored if PCRE_MULTILINE is set.
There is no equivalent to this option in Perl, and no way to set it within a
pattern.
<pre>
  PCRE_DOTALL
</pre>
If this bit is set, a dot metacharacter in the pattern matches a character of
any value, including one that indicates a newline. However, it only ever
matches one character, even if newlines are coded as CRLF. Without this option,
a dot does not match when the current position is at a newline. This option is
equivalent to Perl's /s option, and it can be changed within a pattern by a
(?s) option setting. A negative class such as [^a] always matches newline
characters, independent of the setting of this option.
<pre>
  PCRE_DUPNAMES
</pre>
If this bit is set, names used to identify capturing subpatterns need not be
unique. This can be helpful for certain types of pattern when it is known that
only one instance of the named subpattern can ever be matched. There are more
details of named subpatterns below; see also the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
documentation.
<pre>
  PCRE_EXTENDED
</pre>
If this bit is set, most white space characters in the pattern are totally
ignored except when escaped or inside a character class. However, white space
is not allowed within sequences such as (?&#62; that introduce various
parenthesized subpatterns, nor within a numerical quantifier such as {1,3}.
However, ignorable white space is permitted between an item and a following
quantifier and between a quantifier and a following + that indicates
possessiveness.
</P>
<P>
White space did not used to include the VT character (code 11), because Perl
did not treat this character as white space. However, Perl changed at release
5.18, so PCRE followed at release 8.34, and VT is now treated as white space.
</P>
<P>
PCRE_EXTENDED also causes characters between an unescaped # outside a character
class and the next newline, inclusive, to be ignored. PCRE_EXTENDED is
equivalent to Perl's /x option, and it can be changed within a pattern by a
(?x) option setting.
</P>
<P>
Which characters are interpreted as newlines is controlled by the options
passed to <b>pcre_compile()</b> or by a special sequence at the start of the
pattern, as described in the section entitled
<a href="pcrepattern.html#newlines">"Newline conventions"</a>
in the <b>pcrepattern</b> documentation. Note that the end of this type of
comment is a literal newline sequence in the pattern; escape sequences that
happen to represent a newline do not count.
</P>
<P>
This option makes it possible to include comments inside complicated patterns.
Note, however, that this applies only to data characters. White space characters
may never appear within special character sequences in a pattern, for example
within the sequence (?( that introduces a conditional subpattern.
<pre>
  PCRE_EXTRA
</pre>
This option was invented in order to turn on additional functionality of PCRE
that is incompatible with Perl, but it is currently of very little use. When
set, any backslash in a pattern that is followed by a letter that has no
special meaning causes an error, thus reserving these combinations for future
expansion. By default, as in Perl, a backslash followed by a letter with no
special meaning is treated as a literal. (Perl can, however, be persuaded to
give an error for this, by running it with the -w option.) There are at present
no other features controlled by this option. It can also be set by a (?X)
option setting within a pattern.
<pre>
  PCRE_FIRSTLINE
</pre>
If this option is set, an unanchored pattern is required to match before or at
the first newline in the subject string, though the matched text may continue
over the newline.
<pre>
  PCRE_JAVASCRIPT_COMPAT
</pre>
If this option is set, PCRE's behaviour is changed in some ways so that it is
compatible with JavaScript rather than Perl. The changes are as follows:
</P>
<P>
(1) A lone closing square bracket in a pattern causes a compile-time error,
because this is illegal in JavaScript (by default it is treated as a data
character). Thus, the pattern AB]CD becomes illegal when this option is set.
</P>
<P>
(2) At run time, a back reference to an unset subpattern group matches an empty
string (by default this causes the current matching alternative to fail). A
pattern such as (\1)(a) succeeds when this option is set (assuming it can find
an "a" in the subject), whereas it fails by default, for Perl compatibility.
</P>
<P>
(3) \U matches an upper case "U" character; by default \U causes a compile
time error (Perl uses \U to upper case subsequent characters).
</P>
<P>
(4) \u matches a lower case "u" character unless it is followed by four
hexadecimal digits, in which case the hexadecimal number defines the code point
to match. By default, \u causes a compile time error (Perl uses it to upper
case the following character).
</P>
<P>
(5) \x matches a lower case "x" character unless it is followed by two
hexadecimal digits, in which case the hexadecimal number defines the code point
to match. By default, as in Perl, a hexadecimal number is always expected after
\x, but it may have zero, one, or two digits (so, for example, \xz matches a
binary zero character followed by z).
<pre>
  PCRE_MULTILINE
</pre>
By default, for the purposes of matching "start of line" and "end of line",
PCRE treats the subject string as consisting of a single line of characters,
even if it actually contains newlines. The "start of line" metacharacter (^)
matches only at the start of the string, and the "end of line" metacharacter
($) matches only at the end of the string, or before a terminating newline
(except when PCRE_DOLLAR_ENDONLY is set). Note, however, that unless
PCRE_DOTALL is set, the "any character" metacharacter (.) does not match at a
newline. This behaviour (for ^, $, and dot) is the same as Perl.
</P>
<P>
When PCRE_MULTILINE it is set, the "start of line" and "end of line" constructs
match immediately following or immediately before internal newlines in the
subject string, respectively, as well as at the very start and end. This is
equivalent to Perl's /m option, and it can be changed within a pattern by a
(?m) option setting. If there are no newlines in a subject string, or no
occurrences of ^ or $ in a pattern, setting PCRE_MULTILINE has no effect.
<pre>
  PCRE_NEVER_UTF
</pre>
This option locks out interpretation of the pattern as UTF-8 (or UTF-16 or
UTF-32 in the 16-bit and 32-bit libraries). In particular, it prevents the
creator of the pattern from switching to UTF interpretation by starting the
pattern with (*UTF). This may be useful in applications that process patterns
from external sources. The combination of PCRE_UTF8 and PCRE_NEVER_UTF also
causes an error.
<pre>
  PCRE_NEWLINE_CR
  PCRE_NEWLINE_LF
  PCRE_NEWLINE_CRLF
  PCRE_NEWLINE_ANYCRLF
  PCRE_NEWLINE_ANY
</pre>
These options override the default newline definition that was chosen when PCRE
was built. Setting the first or the second specifies that a newline is
indicated by a single character (CR or LF, respectively). Setting
PCRE_NEWLINE_CRLF specifies that a newline is indicated by the two-character
CRLF sequence. Setting PCRE_NEWLINE_ANYCRLF specifies that any of the three
preceding sequences should be recognized. Setting PCRE_NEWLINE_ANY specifies
that any Unicode newline sequence should be recognized.
</P>
<P>
In an ASCII/Unicode environment, the Unicode newline sequences are the three
just mentioned, plus the single characters VT (vertical tab, U+000B), FF (form
feed, U+000C), NEL (next line, U+0085), LS (line separator, U+2028), and PS
(paragraph separator, U+2029). For the 8-bit library, the last two are
recognized only in UTF-8 mode.
</P>
<P>
When PCRE is compiled to run in an EBCDIC (mainframe) environment, the code for
CR is 0x0d, the same as ASCII. However, the character code for LF is normally
0x15, though in some EBCDIC environments 0x25 is used. Whichever of these is
not LF is made to correspond to Unicode's NEL character. EBCDIC codes are all
less than 256. For more details, see the
<a href="pcrebuild.html"><b>pcrebuild</b></a>
documentation.
</P>
<P>
The newline setting in the options word uses three bits that are treated
as a number, giving eight possibilities. Currently only six are used (default
plus the five values above). This means that if you set more than one newline
option, the combination may or may not be sensible. For example,
PCRE_NEWLINE_CR with PCRE_NEWLINE_LF is equivalent to PCRE_NEWLINE_CRLF, but
other combinations may yield unused numbers and cause an error.
</P>
<P>
The only time that a line break in a pattern is specially recognized when
compiling is when PCRE_EXTENDED is set. CR and LF are white space characters,
and so are ignored in this mode. Also, an unescaped # outside a character class
indicates a comment that lasts until after the next line break sequence. In
other circumstances, line break sequences in patterns are treated as literal
data.
</P>
<P>
The newline option that is set at compile time becomes the default that is used
for <b>pcre_exec()</b> and <b>pcre_dfa_exec()</b>, but it can be overridden.
<pre>
  PCRE_NO_AUTO_CAPTURE
</pre>
If this option is set, it disables the use of numbered capturing parentheses in
the pattern. Any opening parenthesis that is not followed by ? behaves as if it
were followed by ?: but named parentheses can still be used for capturing (and
they acquire numbers in the usual way). There is no equivalent of this option
in Perl.
<pre>
  PCRE_NO_AUTO_POSSESS
</pre>
If this option is set, it disables "auto-possessification". This is an
optimization that, for example, turns a+b into a++b in order to avoid
backtracks into a+ that can never be successful. However, if callouts are in
use, auto-possessification means that some of them are never taken. You can set
this option if you want the matching functions to do a full unoptimized search
and run all the callouts, but it is mainly provided for testing purposes.
<pre>
  PCRE_NO_START_OPTIMIZE
</pre>
This is an option that acts at matching time; that is, it is really an option
for <b>pcre_exec()</b> or <b>pcre_dfa_exec()</b>. If it is set at compile time,
it is remembered with the compiled pattern and assumed at matching time. This
is necessary if you want to use JIT execution, because the JIT compiler needs
to know whether or not this option is set. For details see the discussion of
PCRE_NO_START_OPTIMIZE
<a href="#execoptions">below.</a>
<pre>
  PCRE_UCP
</pre>
This option changes the way PCRE processes \B, \b, \D, \d, \S, \s, \W,
\w, and some of the POSIX character classes. By default, only ASCII characters
are recognized, but if PCRE_UCP is set, Unicode properties are used instead to
classify characters. More details are given in the section on
<a href="pcre.html#genericchartypes">generic character types</a>
in the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
page. If you set PCRE_UCP, matching one of the items it affects takes much
longer. The option is available only if PCRE has been compiled with Unicode
property support.
<pre>
  PCRE_UNGREEDY
</pre>
This option inverts the "greediness" of the quantifiers so that they are not
greedy by default, but become greedy if followed by "?". It is not compatible
with Perl. It can also be set by a (?U) option setting within the pattern.
<pre>
  PCRE_UTF8
</pre>
This option causes PCRE to regard both the pattern and the subject as strings
of UTF-8 characters instead of single-byte strings. However, it is available
only when PCRE is built to include UTF support. If not, the use of this option
provokes an error. Details of how this option changes the behaviour of PCRE are
given in the
<a href="pcreunicode.html"><b>pcreunicode</b></a>
page.
<pre>
  PCRE_NO_UTF8_CHECK
</pre>
When PCRE_UTF8 is set, the validity of the pattern as a UTF-8 string is
automatically checked. There is a discussion about the
<a href="pcreunicode.html#utf8strings">validity of UTF-8 strings</a>
in the
<a href="pcreunicode.html"><b>pcreunicode</b></a>
page. If an invalid UTF-8 sequence is found, <b>pcre_compile()</b> returns an
error. If you already know that your pattern is valid, and you want to skip
this check for performance reasons, you can set the PCRE_NO_UTF8_CHECK option.
When it is set, the effect of passing an invalid UTF-8 string as a pattern is
undefined. It may cause your program to crash or loop. Note that this option
can also be passed to <b>pcre_exec()</b> and <b>pcre_dfa_exec()</b>, to suppress
the validity checking of subject strings only. If the same string is being
matched many times, the option can be safely set for the second and subsequent
matchings to improve performance.
</P>
<br><a name="SEC12" href="#TOC1">COMPILATION ERROR CODES</a><br>
<P>
The following table lists the error codes than may be returned by
<b>pcre_compile2()</b>, along with the error messages that may be returned by
both compiling functions. Note that error messages are always 8-bit ASCII
strings, even in 16-bit or 32-bit mode. As PCRE has developed, some error codes
have fallen out of use. To avoid confusion, they have not been re-used.
<pre>
   0  no error
   1  \ at end of pattern
   2  \c at end of pattern
   3  unrecognized character follows \
   4  numbers out of order in {} quantifier
   5  number too big in {} quantifier
   6  missing terminating ] for character class
   7  invalid escape sequence in character class
   8  range out of order in character class
   9  nothing to repeat
  10  [this code is not in use]
  11  internal error: unexpected repeat
  12  unrecognized character after (? or (?-
  13  POSIX named classes are supported only within a class
  14  missing )
  15  reference to non-existent subpattern
  16  erroffset passed as NULL
  17  unknown option bit(s) set
  18  missing ) after comment
  19  [this code is not in use]
  20  regular expression is too large
  21  failed to get memory
  22  unmatched parentheses
  23  internal error: code overflow
  24  unrecognized character after (?&#60;
  25  lookbehind assertion is not fixed length
  26  malformed number or name after (?(
  27  conditional group contains more than two branches
  28  assertion expected after (?(
  29  (?R or (?[+-]digits must be followed by )
  30  unknown POSIX class name
  31  POSIX collating elements are not supported
  32  this version of PCRE is compiled without UTF support
  33  [this code is not in use]
  34  character value in \x{} or \o{} is too large
  35  invalid condition (?(0)
  36  \C not allowed in lookbehind assertion
  37  PCRE does not support \L, \l, \N{name}, \U, or \u
  38  number after (?C is &#62; 255
  39  closing ) for (?C expected
  40  recursive call could loop indefinitely
  41  unrecognized character after (?P
  42  syntax error in subpattern name (missing terminator)
  43  two named subpatterns have the same name
  44  invalid UTF-8 string (specifically UTF-8)
  45  support for \P, \p, and \X has not been compiled
  46  malformed \P or \p sequence
  47  unknown property name after \P or \p
  48  subpattern name is too long (maximum 32 characters)
  49  too many named subpatterns (maximum 10000)
  50  [this code is not in use]
  51  octal value is greater than \377 in 8-bit non-UTF-8 mode
  52  internal error: overran compiling workspace
  53  internal error: previously-checked referenced subpattern
        not found
  54  DEFINE group contains more than one branch
  55  repeating a DEFINE group is not allowed
  56  inconsistent NEWLINE options
  57  \g is not followed by a braced, angle-bracketed, or quoted
        name/number or by a plain number
  58  a numbered reference must not be zero
  59  an argument is not allowed for (*ACCEPT), (*FAIL), or (*COMMIT)
  60  (*VERB) not recognized or malformed
  61  number is too big
  62  subpattern name expected
  63  digit expected after (?+
  64  ] is an invalid data character in JavaScript compatibility mode
  65  different names for subpatterns of the same number are
        not allowed
  66  (*MARK) must have an argument
  67  this version of PCRE is not compiled with Unicode property
        support
  68  \c must be followed by an ASCII character
  69  \k is not followed by a braced, angle-bracketed, or quoted name
  70  internal error: unknown opcode in find_fixedlength()
  71  \N is not supported in a class
  72  too many forward references
  73  disallowed Unicode code point (&#62;= 0xd800 && &#60;= 0xdfff)
  74  invalid UTF-16 string (specifically UTF-16)
  75  name is too long in (*MARK), (*PRUNE), (*SKIP), or (*THEN)
  76  character value in \u.... sequence is too large
  77  invalid UTF-32 string (specifically UTF-32)
  78  setting UTF is disabled by the application
  79  non-hex character in \x{} (closing brace missing?)
  80  non-octal character in \o{} (closing brace missing?)
  81  missing opening brace after \o
  82  parentheses are too deeply nested
  83  invalid range in character class
  84  group name must start with a non-digit
  85  parentheses are too deeply nested (stack check)
</pre>
The numbers 32 and 10000 in errors 48 and 49 are defaults; different values may
be used if the limits were changed when PCRE was built.
<a name="studyingapattern"></a></P>
<br><a name="SEC13" href="#TOC1">STUDYING A PATTERN</a><br>
<P>
<b>pcre_extra *pcre_study(const pcre *<i>code</i>, int <i>options</i>,</b>
<b>     const char **<i>errptr</i>);</b>
</P>
<P>
If a compiled pattern is going to be used several times, it is worth spending
more time analyzing it in order to speed up the time taken for matching. The
function <b>pcre_study()</b> takes a pointer to a compiled pattern as its first
argument. If studying the pattern produces additional information that will
help speed up matching, <b>pcre_study()</b> returns a pointer to a
<b>pcre_extra</b> block, in which the <i>study_data</i> field points to the
results of the study.
</P>
<P>
The returned value from <b>pcre_study()</b> can be passed directly to
<b>pcre_exec()</b> or <b>pcre_dfa_exec()</b>. However, a <b>pcre_extra</b> block
also contains other fields that can be set by the caller before the block is
passed; these are described
<a href="#extradata">below</a>
in the section on matching a pattern.
</P>
<P>
If studying the pattern does not produce any useful information,
<b>pcre_study()</b> returns NULL by default. In that circumstance, if the
calling program wants to pass any of the other fields to <b>pcre_exec()</b> or
<b>pcre_dfa_exec()</b>, it must set up its own <b>pcre_extra</b> block. However,
if <b>pcre_study()</b> is called with the PCRE_STUDY_EXTRA_NEEDED option, it
returns a <b>pcre_extra</b> block even if studying did not find any additional
information. It may still return NULL, however, if an error occurs in
<b>pcre_study()</b>.
</P>
<P>
The second argument of <b>pcre_study()</b> contains option bits. There are three
further options in addition to PCRE_STUDY_EXTRA_NEEDED:
<pre>
  PCRE_STUDY_JIT_COMPILE
  PCRE_STUDY_JIT_PARTIAL_HARD_COMPILE
  PCRE_STUDY_JIT_PARTIAL_SOFT_COMPILE
</pre>
If any of these are set, and the just-in-time compiler is available, the
pattern is further compiled into machine code that executes much faster than
the <b>pcre_exec()</b> interpretive matching function. If the just-in-time
compiler is not available, these options are ignored. All undefined bits in the
<i>options</i> argument must be zero.
</P>
<P>
JIT compilation is a heavyweight optimization. It can take some time for
patterns to be analyzed, and for one-off matches and simple patterns the
benefit of faster execution might be offset by a much slower study time.
Not all patterns can be optimized by the JIT compiler. For those that cannot be
handled, matching automatically falls back to the <b>pcre_exec()</b>
interpreter. For more details, see the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation.
</P>
<P>
The third argument for <b>pcre_study()</b> is a pointer for an error message. If
studying succeeds (even if no data is returned), the variable it points to is
set to NULL. Otherwise it is set to point to a textual error message. This is a
static string that is part of the library. You must not try to free it. You
should test the error pointer for NULL after calling <b>pcre_study()</b>, to be
sure that it has run successfully.
</P>
<P>
When you are finished with a pattern, you can free the memory used for the
study data by calling <b>pcre_free_study()</b>. This function was added to the
API for release 8.20. For earlier versions, the memory could be freed with
<b>pcre_free()</b>, just like the pattern itself. This will still work in cases
where JIT optimization is not used, but it is advisable to change to the new
function when convenient.
</P>
<P>
This is a typical way in which <b>pcre_study</b>() is used (except that in a
real application there should be tests for errors):
<pre>
  int rc;
  pcre *re;
  pcre_extra *sd;
  re = pcre_compile("pattern", 0, &error, &erroroffset, NULL);
  sd = pcre_study(
    re,             /* result of pcre_compile() */
    0,              /* no options */
    &error);        /* set to NULL or points to a message */
  rc = pcre_exec(   /* see below for details of pcre_exec() options */
    re, sd, "subject", 7, 0, 0, ovector, 30);
  ...
  pcre_free_study(sd);
  pcre_free(re);
</pre>
Studying a pattern does two things: first, a lower bound for the length of
subject string that is needed to match the pattern is computed. This does not
mean that there are any strings of that length that match, but it does
guarantee that no shorter strings match. The value is used to avoid wasting
time by trying to match strings that are shorter than the lower bound. You can
find out the value in a calling program via the <b>pcre_fullinfo()</b> function.
</P>
<P>
Studying a pattern is also useful for non-anchored patterns that do not have a
single fixed starting character. A bitmap of possible starting bytes is
created. This speeds up finding a position in the subject at which to start
matching. (In 16-bit mode, the bitmap is used for 16-bit values less than 256.
In 32-bit mode, the bitmap is used for 32-bit values less than 256.)
</P>
<P>
These two optimizations apply to both <b>pcre_exec()</b> and
<b>pcre_dfa_exec()</b>, and the information is also used by the JIT compiler.
The optimizations can be disabled by setting the PCRE_NO_START_OPTIMIZE option.
You might want to do this if your pattern contains callouts or (*MARK) and you
want to make use of these facilities in cases where matching fails.
</P>
<P>
PCRE_NO_START_OPTIMIZE can be specified at either compile time or execution
time. However, if PCRE_NO_START_OPTIMIZE is passed to <b>pcre_exec()</b>, (that
is, after any JIT compilation has happened) JIT execution is disabled. For JIT
execution to work with PCRE_NO_START_OPTIMIZE, the option must be set at
compile time.
</P>
<P>
There is a longer discussion of PCRE_NO_START_OPTIMIZE
<a href="#execoptions">below.</a>
<a name="localesupport"></a></P>
<br><a name="SEC14" href="#TOC1">LOCALE SUPPORT</a><br>
<P>
PCRE handles caseless matching, and determines whether characters are letters,
digits, or whatever, by reference to a set of tables, indexed by character
code point. When running in UTF-8 mode, or in the 16- or 32-bit libraries, this
applies only to characters with code points less than 256. By default,
higher-valued code points never match escapes such as \w or \d. However, if
PCRE is built with Unicode property support, all characters can be tested with
\p and \P, or, alternatively, the PCRE_UCP option can be set when a pattern
is compiled; this causes \w and friends to use Unicode property support
instead of the built-in tables.
</P>
<P>
The use of locales with Unicode is discouraged. If you are handling characters
with code points greater than 128, you should either use Unicode support, or
use locales, but not try to mix the two.
</P>
<P>
PCRE contains an internal set of tables that are used when the final argument
of <b>pcre_compile()</b> is NULL. These are sufficient for many applications.
Normally, the internal tables recognize only ASCII characters. However, when
PCRE is built, it is possible to cause the internal tables to be rebuilt in the
default "C" locale of the local system, which may cause them to be different.
</P>
<P>
The internal tables can always be overridden by tables supplied by the
application that calls PCRE. These may be created in a different locale from
the default. As more and more applications change to using Unicode, the need
for this locale support is expected to die away.
</P>
<P>
External tables are built by calling the <b>pcre_maketables()</b> function,
which has no arguments, in the relevant locale. The result can then be passed
to <b>pcre_compile()</b> as often as necessary. For example, to build and use
tables that are appropriate for the French locale (where accented characters
with values greater than 128 are treated as letters), the following code could
be used:
<pre>
  setlocale(LC_CTYPE, "fr_FR");
  tables = pcre_maketables();
  re = pcre_compile(..., tables);
</pre>
The locale name "fr_FR" is used on Linux and other Unix-like systems; if you
are using Windows, the name for the French locale is "french".
</P>
<P>
When <b>pcre_maketables()</b> runs, the tables are built in memory that is
obtained via <b>pcre_malloc</b>. It is the caller's responsibility to ensure
that the memory containing the tables remains available for as long as it is
needed.
</P>
<P>
The pointer that is passed to <b>pcre_compile()</b> is saved with the compiled
pattern, and the same tables are used via this pointer by <b>pcre_study()</b>
and also by <b>pcre_exec()</b> and <b>pcre_dfa_exec()</b>. Thus, for any single
pattern, compilation, studying and matching all happen in the same locale, but
different patterns can be processed in different locales.
</P>
<P>
It is possible to pass a table pointer or NULL (indicating the use of the
internal tables) to <b>pcre_exec()</b> or <b>pcre_dfa_exec()</b> (see the
discussion below in the section on matching a pattern). This facility is
provided for use with pre-compiled patterns that have been saved and reloaded.
Character tables are not saved with patterns, so if a non-standard table was
used at compile time, it must be provided again when the reloaded pattern is
matched. Attempting to use this facility to match a pattern in a different
locale from the one in which it was compiled is likely to lead to anomalous
(usually incorrect) results.
<a name="infoaboutpattern"></a></P>
<br><a name="SEC15" href="#TOC1">INFORMATION ABOUT A PATTERN</a><br>
<P>
<b>int pcre_fullinfo(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     int <i>what</i>, void *<i>where</i>);</b>
</P>
<P>
The <b>pcre_fullinfo()</b> function returns information about a compiled
pattern. It replaces the <b>pcre_info()</b> function, which was removed from the
library at version 8.30, after more than 10 years of obsolescence.
</P>
<P>
The first argument for <b>pcre_fullinfo()</b> is a pointer to the compiled
pattern. The second argument is the result of <b>pcre_study()</b>, or NULL if
the pattern was not studied. The third argument specifies which piece of
information is required, and the fourth argument is a pointer to a variable
to receive the data. The yield of the function is zero for success, or one of
the following negative numbers:
<pre>
  PCRE_ERROR_NULL           the argument <i>code</i> was NULL
                            the argument <i>where</i> was NULL
  PCRE_ERROR_BADMAGIC       the "magic number" was not found
  PCRE_ERROR_BADENDIANNESS  the pattern was compiled with different
                            endianness
  PCRE_ERROR_BADOPTION      the value of <i>what</i> was invalid
  PCRE_ERROR_UNSET          the requested field is not set
</pre>
The "magic number" is placed at the start of each compiled pattern as an simple
check against passing an arbitrary memory pointer. The endianness error can
occur if a compiled pattern is saved and reloaded on a different host. Here is
a typical call of <b>pcre_fullinfo()</b>, to obtain the length of the compiled
pattern:
<pre>
  int rc;
  size_t length;
  rc = pcre_fullinfo(
    re,               /* result of pcre_compile() */
    sd,               /* result of pcre_study(), or NULL */
    PCRE_INFO_SIZE,   /* what is required */
    &length);         /* where to put the data */
</pre>
The possible values for the third argument are defined in <b>pcre.h</b>, and are
as follows:
<pre>
  PCRE_INFO_BACKREFMAX
</pre>
Return the number of the highest back reference in the pattern. The fourth
argument should point to an <b>int</b> variable. Zero is returned if there are
no back references.
<pre>
  PCRE_INFO_CAPTURECOUNT
</pre>
Return the number of capturing subpatterns in the pattern. The fourth argument
should point to an <b>int</b> variable.
<pre>
  PCRE_INFO_DEFAULT_TABLES
</pre>
Return a pointer to the internal default character tables within PCRE. The
fourth argument should point to an <b>unsigned char *</b> variable. This
information call is provided for internal use by the <b>pcre_study()</b>
function. External callers can cause PCRE to use its internal tables by passing
a NULL table pointer.
<pre>
  PCRE_INFO_FIRSTBYTE (deprecated)
</pre>
Return information about the first data unit of any matched string, for a
non-anchored pattern. The name of this option refers to the 8-bit library,
where data units are bytes. The fourth argument should point to an <b>int</b>
variable. Negative values are used for special cases. However, this means that
when the 32-bit library is in non-UTF-32 mode, the full 32-bit range of
characters cannot be returned. For this reason, this value is deprecated; use
PCRE_INFO_FIRSTCHARACTERFLAGS and PCRE_INFO_FIRSTCHARACTER instead.
</P>
<P>
If there is a fixed first value, for example, the letter "c" from a pattern
such as (cat|cow|coyote), its value is returned. In the 8-bit library, the
value is always less than 256. In the 16-bit library the value can be up to
0xffff. In the 32-bit library the value can be up to 0x10ffff.
</P>
<P>
If there is no fixed first value, and if either
<br>
<br>
(a) the pattern was compiled with the PCRE_MULTILINE option, and every branch
starts with "^", or
<br>
<br>
(b) every branch of the pattern starts with ".*" and PCRE_DOTALL is not set
(if it were set, the pattern would be anchored),
<br>
<br>
-1 is returned, indicating that the pattern matches only at the start of a
subject string or after any newline within the string. Otherwise -2 is
returned. For anchored patterns, -2 is returned.
<pre>
  PCRE_INFO_FIRSTCHARACTER
</pre>
Return the value of the first data unit (non-UTF character) of any matched
string in the situation where PCRE_INFO_FIRSTCHARACTERFLAGS returns 1;
otherwise return 0. The fourth argument should point to an <b>uint_t</b>
variable.
</P>
<P>
In the 8-bit library, the value is always less than 256. In the 16-bit library
the value can be up to 0xffff. In the 32-bit library in UTF-32 mode the value
can be up to 0x10ffff, and up to 0xffffffff when not using UTF-32 mode.
<pre>
  PCRE_INFO_FIRSTCHARACTERFLAGS
</pre>
Return information about the first data unit of any matched string, for a
non-anchored pattern. The fourth argument should point to an <b>int</b>
variable.
</P>
<P>
If there is a fixed first value, for example, the letter "c" from a pattern
such as (cat|cow|coyote), 1 is returned, and the character value can be
retrieved using PCRE_INFO_FIRSTCHARACTER. If there is no fixed first value, and
if either
<br>
<br>
(a) the pattern was compiled with the PCRE_MULTILINE option, and every branch
starts with "^", or
<br>
<br>
(b) every branch of the pattern starts with ".*" and PCRE_DOTALL is not set
(if it were set, the pattern would be anchored),
<br>
<br>
2 is returned, indicating that the pattern matches only at the start of a
subject string or after any newline within the string. Otherwise 0 is
returned. For anchored patterns, 0 is returned.
<pre>
  PCRE_INFO_FIRSTTABLE
</pre>
If the pattern was studied, and this resulted in the construction of a 256-bit
table indicating a fixed set of values for the first data unit in any matching
string, a pointer to the table is returned. Otherwise NULL is returned. The
fourth argument should point to an <b>unsigned char *</b> variable.
<pre>
  PCRE_INFO_HASCRORLF
</pre>
Return 1 if the pattern contains any explicit matches for CR or LF characters,
otherwise 0. The fourth argument should point to an <b>int</b> variable. An
explicit match is either a literal CR or LF character, or \r or \n.
<pre>
  PCRE_INFO_JCHANGED
</pre>
Return 1 if the (?J) or (?-J) option setting is used in the pattern, otherwise
0. The fourth argument should point to an <b>int</b> variable. (?J) and
(?-J) set and unset the local PCRE_DUPNAMES option, respectively.
<pre>
  PCRE_INFO_JIT
</pre>
Return 1 if the pattern was studied with one of the JIT options, and
just-in-time compiling was successful. The fourth argument should point to an
<b>int</b> variable. A return value of 0 means that JIT support is not available
in this version of PCRE, or that the pattern was not studied with a JIT option,
or that the JIT compiler could not handle this particular pattern. See the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation for details of what can and cannot be handled.
<pre>
  PCRE_INFO_JITSIZE
</pre>
If the pattern was successfully studied with a JIT option, return the size of
the JIT compiled code, otherwise return zero. The fourth argument should point
to a <b>size_t</b> variable.
<pre>
  PCRE_INFO_LASTLITERAL
</pre>
Return the value of the rightmost literal data unit that must exist in any
matched string, other than at its start, if such a value has been recorded. The
fourth argument should point to an <b>int</b> variable. If there is no such
value, -1 is returned. For anchored patterns, a last literal value is recorded
only if it follows something of variable length. For example, for the pattern
/^a\d+z\d+/ the returned value is "z", but for /^a\dz\d/ the returned value
is -1.
</P>
<P>
Since for the 32-bit library using the non-UTF-32 mode, this function is unable
to return the full 32-bit range of characters, this value is deprecated;
instead the PCRE_INFO_REQUIREDCHARFLAGS and PCRE_INFO_REQUIREDCHAR values should
be used.
<pre>
  PCRE_INFO_MATCH_EMPTY
</pre>
Return 1 if the pattern can match an empty string, otherwise 0. The fourth
argument should point to an <b>int</b> variable.
<pre>
  PCRE_INFO_MATCHLIMIT
</pre>
If the pattern set a match limit by including an item of the form
(*LIMIT_MATCH=nnnn) at the start, the value is returned. The fourth argument
should point to an unsigned 32-bit integer. If no such value has been set, the
call to <b>pcre_fullinfo()</b> returns the error PCRE_ERROR_UNSET.
<pre>
  PCRE_INFO_MAXLOOKBEHIND
</pre>
Return the number of characters (NB not data units) in the longest lookbehind
assertion in the pattern. This information is useful when doing multi-segment
matching using the partial matching facilities. Note that the simple assertions
\b and \B require a one-character lookbehind. \A also registers a
one-character lookbehind, though it does not actually inspect the previous
character. This is to ensure that at least one character from the old segment
is retained when a new segment is processed. Otherwise, if there are no
lookbehinds in the pattern, \A might match incorrectly at the start of a new
segment.
<pre>
  PCRE_INFO_MINLENGTH
</pre>
If the pattern was studied and a minimum length for matching subject strings
was computed, its value is returned. Otherwise the returned value is -1. The
value is a number of characters, which in UTF mode may be different from the
number of data units. The fourth argument should point to an <b>int</b>
variable. A non-negative value is a lower bound to the length of any matching
string. There may not be any strings of that length that do actually match, but
every string that does match is at least that long.
<pre>
  PCRE_INFO_NAMECOUNT
  PCRE_INFO_NAMEENTRYSIZE
  PCRE_INFO_NAMETABLE
</pre>
PCRE supports the use of named as well as numbered capturing parentheses. The
names are just an additional way of identifying the parentheses, which still
acquire numbers. Several convenience functions such as
<b>pcre_get_named_substring()</b> are provided for extracting captured
substrings by name. It is also possible to extract the data directly, by first
converting the name to a number in order to access the correct pointers in the
output vector (described with <b>pcre_exec()</b> below). To do the conversion,
you need to use the name-to-number map, which is described by these three
values.
</P>
<P>
The map consists of a number of fixed-size entries. PCRE_INFO_NAMECOUNT gives
the number of entries, and PCRE_INFO_NAMEENTRYSIZE gives the size of each
entry; both of these return an <b>int</b> value. The entry size depends on the
length of the longest name. PCRE_INFO_NAMETABLE returns a pointer to the first
entry of the table. This is a pointer to <b>char</b> in the 8-bit library, where
the first two bytes of each entry are the number of the capturing parenthesis,
most significant byte first. In the 16-bit library, the pointer points to
16-bit data units, the first of which contains the parenthesis number. In the
32-bit library, the pointer points to 32-bit data units, the first of which
contains the parenthesis number. The rest of the entry is the corresponding
name, zero terminated.
</P>
<P>
The names are in alphabetical order. If (?| is used to create multiple groups
with the same number, as described in the
<a href="pcrepattern.html#dupsubpatternnumber">section on duplicate subpattern numbers</a>
in the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
page, the groups may be given the same name, but there is only one entry in the
table. Different names for groups of the same number are not permitted.
Duplicate names for subpatterns with different numbers are permitted,
but only if PCRE_DUPNAMES is set. They appear in the table in the order in
which they were found in the pattern. In the absence of (?| this is the order
of increasing number; when (?| is used this is not necessarily the case because
later subpatterns may have lower numbers.
</P>
<P>
As a simple example of the name/number table, consider the following pattern
after compilation by the 8-bit library (assume PCRE_EXTENDED is set, so white
space - including newlines - is ignored):
<pre>
  (?&#60;date&#62; (?&#60;year&#62;(\d\d)?\d\d) - (?&#60;month&#62;\d\d) - (?&#60;day&#62;\d\d) )
</pre>
There are four named subpatterns, so the table has four entries, and each entry
in the table is eight bytes long. The table is as follows, with non-printing
bytes shows in hexadecimal, and undefined bytes shown as ??:
<pre>
  00 01 d  a  t  e  00 ??
  00 05 d  a  y  00 ?? ??
  00 04 m  o  n  t  h  00
  00 02 y  e  a  r  00 ??
</pre>
When writing code to extract data from named subpatterns using the
name-to-number map, remember that the length of the entries is likely to be
different for each compiled pattern.
<pre>
  PCRE_INFO_OKPARTIAL
</pre>
Return 1 if the pattern can be used for partial matching with
<b>pcre_exec()</b>, otherwise 0. The fourth argument should point to an
<b>int</b> variable. From release 8.00, this always returns 1, because the
restrictions that previously applied to partial matching have been lifted. The
<a href="pcrepartial.html"><b>pcrepartial</b></a>
documentation gives details of partial matching.
<pre>
  PCRE_INFO_OPTIONS
</pre>
Return a copy of the options with which the pattern was compiled. The fourth
argument should point to an <b>unsigned long int</b> variable. These option bits
are those specified in the call to <b>pcre_compile()</b>, modified by any
top-level option settings at the start of the pattern itself. In other words,
they are the options that will be in force when matching starts. For example,
if the pattern /(?im)abc(?-i)d/ is compiled with the PCRE_EXTENDED option, the
result is PCRE_CASELESS, PCRE_MULTILINE, and PCRE_EXTENDED.
</P>
<P>
A pattern is automatically anchored by PCRE if all of its top-level
alternatives begin with one of the following:
<pre>
  ^     unless PCRE_MULTILINE is set
  \A    always
  \G    always
  .*    if PCRE_DOTALL is set and there are no back references to the subpattern in which .* appears
</pre>
For such patterns, the PCRE_ANCHORED bit is set in the options returned by
<b>pcre_fullinfo()</b>.
<pre>
  PCRE_INFO_RECURSIONLIMIT
</pre>
If the pattern set a recursion limit by including an item of the form
(*LIMIT_RECURSION=nnnn) at the start, the value is returned. The fourth
argument should point to an unsigned 32-bit integer. If no such value has been
set, the call to <b>pcre_fullinfo()</b> returns the error PCRE_ERROR_UNSET.
<pre>
  PCRE_INFO_SIZE
</pre>
Return the size of the compiled pattern in bytes (for all three libraries). The
fourth argument should point to a <b>size_t</b> variable. This value does not
include the size of the <b>pcre</b> structure that is returned by
<b>pcre_compile()</b>. The value that is passed as the argument to
<b>pcre_malloc()</b> when <b>pcre_compile()</b> is getting memory in which to
place the compiled data is the value returned by this option plus the size of
the <b>pcre</b> structure. Studying a compiled pattern, with or without JIT,
does not alter the value returned by this option.
<pre>
  PCRE_INFO_STUDYSIZE
</pre>
Return the size in bytes (for all three libraries) of the data block pointed to
by the <i>study_data</i> field in a <b>pcre_extra</b> block. If <b>pcre_extra</b>
is NULL, or there is no study data, zero is returned. The fourth argument
should point to a <b>size_t</b> variable. The <i>study_data</i> field is set by
<b>pcre_study()</b> to record information that will speed up matching (see the
section entitled
<a href="#studyingapattern">"Studying a pattern"</a>
above). The format of the <i>study_data</i> block is private, but its length
is made available via this option so that it can be saved and restored (see the
<a href="pcreprecompile.html"><b>pcreprecompile</b></a>
documentation for details).
<pre>
  PCRE_INFO_REQUIREDCHARFLAGS
</pre>
Returns 1 if there is a rightmost literal data unit that must exist in any
matched string, other than at its start. The fourth argument should  point to
an <b>int</b> variable. If there is no such value, 0 is returned. If returning
1, the character value itself can be retrieved using PCRE_INFO_REQUIREDCHAR.
</P>
<P>
For anchored patterns, a last literal value is recorded only if it follows
something of variable length. For example, for the pattern /^a\d+z\d+/ the
returned value 1 (with "z" returned from PCRE_INFO_REQUIREDCHAR), but for
/^a\dz\d/ the returned value is 0.
<pre>
  PCRE_INFO_REQUIREDCHAR
</pre>
Return the value of the rightmost literal data unit that must exist in any
matched string, other than at its start, if such a value has been recorded. The
fourth argument should point to an <b>uint32_t</b> variable. If there is no such
value, 0 is returned.
</P>
<br><a name="SEC16" href="#TOC1">REFERENCE COUNTS</a><br>
<P>
<b>int pcre_refcount(pcre *<i>code</i>, int <i>adjust</i>);</b>
</P>
<P>
The <b>pcre_refcount()</b> function is used to maintain a reference count in the
data block that contains a compiled pattern. It is provided for the benefit of
applications that operate in an object-oriented manner, where different parts
of the application may be using the same compiled pattern, but you want to free
the block when they are all done.
</P>
<P>
When a pattern is compiled, the reference count field is initialized to zero.
It is changed only by calling this function, whose action is to add the
<i>adjust</i> value (which may be positive or negative) to it. The yield of the
function is the new value. However, the value of the count is constrained to
lie between 0 and 65535, inclusive. If the new value is outside these limits,
it is forced to the appropriate limit value.
</P>
<P>
Except when it is zero, the reference count is not correctly preserved if a
pattern is compiled on one host and then transferred to a host whose byte-order
is different. (This seems a highly unlikely scenario.)
</P>
<br><a name="SEC17" href="#TOC1">MATCHING A PATTERN: THE TRADITIONAL FUNCTION</a><br>
<P>
<b>int pcre_exec(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     const char *<i>subject</i>, int <i>length</i>, int <i>startoffset</i>,</b>
<b>     int <i>options</i>, int *<i>ovector</i>, int <i>ovecsize</i>);</b>
</P>
<P>
The function <b>pcre_exec()</b> is called to match a subject string against a
compiled pattern, which is passed in the <i>code</i> argument. If the
pattern was studied, the result of the study should be passed in the
<i>extra</i> argument. You can call <b>pcre_exec()</b> with the same <i>code</i>
and <i>extra</i> arguments as many times as you like, in order to match
different subject strings with the same pattern.
</P>
<P>
This function is the main matching facility of the library, and it operates in
a Perl-like manner. For specialist use there is also an alternative matching
function, which is described
<a href="#dfamatch">below</a>
in the section about the <b>pcre_dfa_exec()</b> function.
</P>
<P>
In most applications, the pattern will have been compiled (and optionally
studied) in the same process that calls <b>pcre_exec()</b>. However, it is
possible to save compiled patterns and study data, and then use them later
in different processes, possibly even on different hosts. For a discussion
about this, see the
<a href="pcreprecompile.html"><b>pcreprecompile</b></a>
documentation.
</P>
<P>
Here is an example of a simple call to <b>pcre_exec()</b>:
<pre>
  int rc;
  int ovector[30];
  rc = pcre_exec(
    re,             /* result of pcre_compile() */
    NULL,           /* we didn't study the pattern */
    "some string",  /* the subject string */
    11,             /* the length of the subject string */
    0,              /* start at offset 0 in the subject */
    0,              /* default options */
    ovector,        /* vector of integers for substring information */
    30);            /* number of elements (NOT size in bytes) */
<a name="extradata"></a></PRE>
</P>
<br><b>
Extra data for <b>pcre_exec()</b>
</b><br>
<P>
If the <i>extra</i> argument is not NULL, it must point to a <b>pcre_extra</b>
data block. The <b>pcre_study()</b> function returns such a block (when it
doesn't return NULL), but you can also create one for yourself, and pass
additional information in it. The <b>pcre_extra</b> block contains the following
fields (not necessarily in this order):
<pre>
  unsigned long int <i>flags</i>;
  void *<i>study_data</i>;
  void *<i>executable_jit</i>;
  unsigned long int <i>match_limit</i>;
  unsigned long int <i>match_limit_recursion</i>;
  void *<i>callout_data</i>;
  const unsigned char *<i>tables</i>;
  unsigned char **<i>mark</i>;
</pre>
In the 16-bit version of this structure, the <i>mark</i> field has type
"PCRE_UCHAR16 **".
<br>
<br>
In the 32-bit version of this structure, the <i>mark</i> field has type
"PCRE_UCHAR32 **".
</P>
<P>
The <i>flags</i> field is used to specify which of the other fields are set. The
flag bits are:
<pre>
  PCRE_EXTRA_CALLOUT_DATA
  PCRE_EXTRA_EXECUTABLE_JIT
  PCRE_EXTRA_MARK
  PCRE_EXTRA_MATCH_LIMIT
  PCRE_EXTRA_MATCH_LIMIT_RECURSION
  PCRE_EXTRA_STUDY_DATA
  PCRE_EXTRA_TABLES
</pre>
Other flag bits should be set to zero. The <i>study_data</i> field and sometimes
the <i>executable_jit</i> field are set in the <b>pcre_extra</b> block that is
returned by <b>pcre_study()</b>, together with the appropriate flag bits. You
should not set these yourself, but you may add to the block by setting other
fields and their corresponding flag bits.
</P>
<P>
The <i>match_limit</i> field provides a means of preventing PCRE from using up a
vast amount of resources when running patterns that are not going to match,
but which have a very large number of possibilities in their search trees. The
classic example is a pattern that uses nested unlimited repeats.
</P>
<P>
Internally, <b>pcre_exec()</b> uses a function called <b>match()</b>, which it
calls repeatedly (sometimes recursively). The limit set by <i>match_limit</i> is
imposed on the number of times this function is called during a match, which
has the effect of limiting the amount of backtracking that can take place. For
patterns that are not anchored, the count restarts from zero for each position
in the subject string.
</P>
<P>
When <b>pcre_exec()</b> is called with a pattern that was successfully studied
with a JIT option, the way that the matching is executed is entirely different.
However, there is still the possibility of runaway matching that goes on for a
very long time, and so the <i>match_limit</i> value is also used in this case
(but in a different way) to limit how long the matching can continue.
</P>
<P>
The default value for the limit can be set when PCRE is built; the default
default is 10 million, which handles all but the most extreme cases. You can
override the default by suppling <b>pcre_exec()</b> with a <b>pcre_extra</b>
block in which <i>match_limit</i> is set, and PCRE_EXTRA_MATCH_LIMIT is set in
the <i>flags</i> field. If the limit is exceeded, <b>pcre_exec()</b> returns
PCRE_ERROR_MATCHLIMIT.
</P>
<P>
A value for the match limit may also be supplied by an item at the start of a
pattern of the form
<pre>
  (*LIMIT_MATCH=d)
</pre>
where d is a decimal number. However, such a setting is ignored unless d is
less than the limit set by the caller of <b>pcre_exec()</b> or, if no such limit
is set, less than the default.
</P>
<P>
The <i>match_limit_recursion</i> field is similar to <i>match_limit</i>, but
instead of limiting the total number of times that <b>match()</b> is called, it
limits the depth of recursion. The recursion depth is a smaller number than the
total number of calls, because not all calls to <b>match()</b> are recursive.
This limit is of use only if it is set smaller than <i>match_limit</i>.
</P>
<P>
Limiting the recursion depth limits the amount of machine stack that can be
used, or, when PCRE has been compiled to use memory on the heap instead of the
stack, the amount of heap memory that can be used. This limit is not relevant,
and is ignored, when matching is done using JIT compiled code.
</P>
<P>
The default value for <i>match_limit_recursion</i> can be set when PCRE is
built; the default default is the same value as the default for
<i>match_limit</i>. You can override the default by suppling <b>pcre_exec()</b>
with a <b>pcre_extra</b> block in which <i>match_limit_recursion</i> is set, and
PCRE_EXTRA_MATCH_LIMIT_RECURSION is set in the <i>flags</i> field. If the limit
is exceeded, <b>pcre_exec()</b> returns PCRE_ERROR_RECURSIONLIMIT.
</P>
<P>
A value for the recursion limit may also be supplied by an item at the start of
a pattern of the form
<pre>
  (*LIMIT_RECURSION=d)
</pre>
where d is a decimal number. However, such a setting is ignored unless d is
less than the limit set by the caller of <b>pcre_exec()</b> or, if no such limit
is set, less than the default.
</P>
<P>
The <i>callout_data</i> field is used in conjunction with the "callout" feature,
and is described in the
<a href="pcrecallout.html"><b>pcrecallout</b></a>
documentation.
</P>
<P>
The <i>tables</i> field is provided for use with patterns that have been
pre-compiled using custom character tables, saved to disc or elsewhere, and
then reloaded, because the tables that were used to compile a pattern are not
saved with it. See the
<a href="pcreprecompile.html"><b>pcreprecompile</b></a>
documentation for a discussion of saving compiled patterns for later use. If
NULL is passed using this mechanism, it forces PCRE's internal tables to be
used.
</P>
<P>
<b>Warning:</b> The tables that <b>pcre_exec()</b> uses must be the same as those
that were used when the pattern was compiled. If this is not the case, the
behaviour of <b>pcre_exec()</b> is undefined. Therefore, when a pattern is
compiled and matched in the same process, this field should never be set. In
this (the most common) case, the correct table pointer is automatically passed
with the compiled pattern from <b>pcre_compile()</b> to <b>pcre_exec()</b>.
</P>
<P>
If PCRE_EXTRA_MARK is set in the <i>flags</i> field, the <i>mark</i> field must
be set to point to a suitable variable. If the pattern contains any
backtracking control verbs such as (*MARK:NAME), and the execution ends up with
a name to pass back, a pointer to the name string (zero terminated) is placed
in the variable pointed to by the <i>mark</i> field. The names are within the
compiled pattern; if you wish to retain such a name you must copy it before
freeing the memory of a compiled pattern. If there is no name to pass back, the
variable pointed to by the <i>mark</i> field is set to NULL. For details of the
backtracking control verbs, see the section entitled
<a href="pcrepattern#backtrackcontrol">"Backtracking control"</a>
in the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
documentation.
<a name="execoptions"></a></P>
<br><b>
Option bits for <b>pcre_exec()</b>
</b><br>
<P>
The unused bits of the <i>options</i> argument for <b>pcre_exec()</b> must be
zero. The only bits that may be set are PCRE_ANCHORED, PCRE_NEWLINE_<i>xxx</i>,
PCRE_NOTBOL, PCRE_NOTEOL, PCRE_NOTEMPTY, PCRE_NOTEMPTY_ATSTART,
PCRE_NO_START_OPTIMIZE, PCRE_NO_UTF8_CHECK, PCRE_PARTIAL_HARD, and
PCRE_PARTIAL_SOFT.
</P>
<P>
If the pattern was successfully studied with one of the just-in-time (JIT)
compile options, the only supported options for JIT execution are
PCRE_NO_UTF8_CHECK, PCRE_NOTBOL, PCRE_NOTEOL, PCRE_NOTEMPTY,
PCRE_NOTEMPTY_ATSTART, PCRE_PARTIAL_HARD, and PCRE_PARTIAL_SOFT. If an
unsupported option is used, JIT execution is disabled and the normal
interpretive code in <b>pcre_exec()</b> is run.
<pre>
  PCRE_ANCHORED
</pre>
The PCRE_ANCHORED option limits <b>pcre_exec()</b> to matching at the first
matching position. If a pattern was compiled with PCRE_ANCHORED, or turned out
to be anchored by virtue of its contents, it cannot be made unachored at
matching time.
<pre>
  PCRE_BSR_ANYCRLF
  PCRE_BSR_UNICODE
</pre>
These options (which are mutually exclusive) control what the \R escape
sequence matches. The choice is either to match only CR, LF, or CRLF, or to
match any Unicode newline sequence. These options override the choice that was
made or defaulted when the pattern was compiled.
<pre>
  PCRE_NEWLINE_CR
  PCRE_NEWLINE_LF
  PCRE_NEWLINE_CRLF
  PCRE_NEWLINE_ANYCRLF
  PCRE_NEWLINE_ANY
</pre>
These options override the newline definition that was chosen or defaulted when
the pattern was compiled. For details, see the description of
<b>pcre_compile()</b> above. During matching, the newline choice affects the
behaviour of the dot, circumflex, and dollar metacharacters. It may also alter
the way the match position is advanced after a match failure for an unanchored
pattern.
</P>
<P>
When PCRE_NEWLINE_CRLF, PCRE_NEWLINE_ANYCRLF, or PCRE_NEWLINE_ANY is set, and a
match attempt for an unanchored pattern fails when the current position is at a
CRLF sequence, and the pattern contains no explicit matches for CR or LF
characters, the match position is advanced by two characters instead of one, in
other words, to after the CRLF.
</P>
<P>
The above rule is a compromise that makes the most common cases work as
expected. For example, if the pattern is .+A (and the PCRE_DOTALL option is not
set), it does not match the string "\r\nA" because, after failing at the
start, it skips both the CR and the LF before retrying. However, the pattern
[\r\n]A does match that string, because it contains an explicit CR or LF
reference, and so advances only by one character after the first failure.
</P>
<P>
An explicit match for CR of LF is either a literal appearance of one of those
characters, or one of the \r or \n escape sequences. Implicit matches such as
[^X] do not count, nor does \s (which includes CR and LF in the characters
that it matches).
</P>
<P>
Notwithstanding the above, anomalous effects may still occur when CRLF is a
valid newline sequence and explicit \r or \n escapes appear in the pattern.
<pre>
  PCRE_NOTBOL
</pre>
This option specifies that first character of the subject string is not the
beginning of a line, so the circumflex metacharacter should not match before
it. Setting this without PCRE_MULTILINE (at compile time) causes circumflex
never to match. This option affects only the behaviour of the circumflex
metacharacter. It does not affect \A.
<pre>
  PCRE_NOTEOL
</pre>
This option specifies that the end of the subject string is not the end of a
line, so the dollar metacharacter should not match it nor (except in multiline
mode) a newline immediately before it. Setting this without PCRE_MULTILINE (at
compile time) causes dollar never to match. This option affects only the
behaviour of the dollar metacharacter. It does not affect \Z or \z.
<pre>
  PCRE_NOTEMPTY
</pre>
An empty string is not considered to be a valid match if this option is set. If
there are alternatives in the pattern, they are tried. If all the alternatives
match the empty string, the entire match fails. For example, if the pattern
<pre>
  a?b?
</pre>
is applied to a string not beginning with "a" or "b", it matches an empty
string at the start of the subject. With PCRE_NOTEMPTY set, this match is not
valid, so PCRE searches further into the string for occurrences of "a" or "b".
<pre>
  PCRE_NOTEMPTY_ATSTART
</pre>
This is like PCRE_NOTEMPTY, except that an empty string match that is not at
the start of the subject is permitted. If the pattern is anchored, such a match
can occur only if the pattern contains \K.
</P>
<P>
Perl has no direct equivalent of PCRE_NOTEMPTY or PCRE_NOTEMPTY_ATSTART, but it
does make a special case of a pattern match of the empty string within its
<b>split()</b> function, and when using the /g modifier. It is possible to
emulate Perl's behaviour after matching a null string by first trying the match
again at the same offset with PCRE_NOTEMPTY_ATSTART and PCRE_ANCHORED, and then
if that fails, by advancing the starting offset (see below) and trying an
ordinary match again. There is some code that demonstrates how to do this in
the
<a href="pcredemo.html"><b>pcredemo</b></a>
sample program. In the most general case, you have to check to see if the
newline convention recognizes CRLF as a newline, and if so, and the current
character is CR followed by LF, advance the starting offset by two characters
instead of one.
<pre>
  PCRE_NO_START_OPTIMIZE
</pre>
There are a number of optimizations that <b>pcre_exec()</b> uses at the start of
a match, in order to speed up the process. For example, if it is known that an
unanchored match must start with a specific character, it searches the subject
for that character, and fails immediately if it cannot find it, without
actually running the main matching function. This means that a special item
such as (*COMMIT) at the start of a pattern is not considered until after a
suitable starting point for the match has been found. Also, when callouts or
(*MARK) items are in use, these "start-up" optimizations can cause them to be
skipped if the pattern is never actually used. The start-up optimizations are
in effect a pre-scan of the subject that takes place before the pattern is run.
</P>
<P>
The PCRE_NO_START_OPTIMIZE option disables the start-up optimizations, possibly
causing performance to suffer, but ensuring that in cases where the result is
"no match", the callouts do occur, and that items such as (*COMMIT) and (*MARK)
are considered at every possible starting position in the subject string. If
PCRE_NO_START_OPTIMIZE is set at compile time, it cannot be unset at matching
time. The use of PCRE_NO_START_OPTIMIZE at matching time (that is, passing it
to <b>pcre_exec()</b>) disables JIT execution; in this situation, matching is
always done using interpretively.
</P>
<P>
Setting PCRE_NO_START_OPTIMIZE can change the outcome of a matching operation.
Consider the pattern
<pre>
  (*COMMIT)ABC
</pre>
When this is compiled, PCRE records the fact that a match must start with the
character "A". Suppose the subject string is "DEFABC". The start-up
optimization scans along the subject, finds "A" and runs the first match
attempt from there. The (*COMMIT) item means that the pattern must match the
current starting position, which in this case, it does. However, if the same
match is run with PCRE_NO_START_OPTIMIZE set, the initial scan along the
subject string does not happen. The first match attempt is run starting from
"D" and when this fails, (*COMMIT) prevents any further matches being tried, so
the overall result is "no match". If the pattern is studied, more start-up
optimizations may be used. For example, a minimum length for the subject may be
recorded. Consider the pattern
<pre>
  (*MARK:A)(X|Y)
</pre>
The minimum length for a match is one character. If the subject is "ABC", there
will be attempts to match "ABC", "BC", "C", and then finally an empty string.
If the pattern is studied, the final attempt does not take place, because PCRE
knows that the subject is too short, and so the (*MARK) is never encountered.
In this case, studying the pattern does not affect the overall match result,
which is still "no match", but it does affect the auxiliary information that is
returned.
<pre>
  PCRE_NO_UTF8_CHECK
</pre>
When PCRE_UTF8 is set at compile time, the validity of the subject as a UTF-8
string is automatically checked when <b>pcre_exec()</b> is subsequently called.
The entire string is checked before any other processing takes place. The value
of <i>startoffset</i> is also checked to ensure that it points to the start of a
UTF-8 character. There is a discussion about the
<a href="pcreunicode.html#utf8strings">validity of UTF-8 strings</a>
in the
<a href="pcreunicode.html"><b>pcreunicode</b></a>
page. If an invalid sequence of bytes is found, <b>pcre_exec()</b> returns the
error PCRE_ERROR_BADUTF8 or, if PCRE_PARTIAL_HARD is set and the problem is a
truncated character at the end of the subject, PCRE_ERROR_SHORTUTF8. In both
cases, information about the precise nature of the error may also be returned
(see the descriptions of these errors in the section entitled \fIError return
values from\fP <b>pcre_exec()</b>
<a href="#errorlist">below).</a>
If <i>startoffset</i> contains a value that does not point to the start of a
UTF-8 character (or to the end of the subject), PCRE_ERROR_BADUTF8_OFFSET is
returned.
</P>
<P>
If you already know that your subject is valid, and you want to skip these
checks for performance reasons, you can set the PCRE_NO_UTF8_CHECK option when
calling <b>pcre_exec()</b>. You might want to do this for the second and
subsequent calls to <b>pcre_exec()</b> if you are making repeated calls to find
all the matches in a single subject string. However, you should be sure that
the value of <i>startoffset</i> points to the start of a character (or the end
of the subject). When PCRE_NO_UTF8_CHECK is set, the effect of passing an
invalid string as a subject or an invalid value of <i>startoffset</i> is
undefined. Your program may crash or loop.
<pre>
  PCRE_PARTIAL_HARD
  PCRE_PARTIAL_SOFT
</pre>
These options turn on the partial matching feature. For backwards
compatibility, PCRE_PARTIAL is a synonym for PCRE_PARTIAL_SOFT. A partial match
occurs if the end of the subject string is reached successfully, but there are
not enough subject characters to complete the match. If this happens when
PCRE_PARTIAL_SOFT (but not PCRE_PARTIAL_HARD) is set, matching continues by
testing any remaining alternatives. Only if no complete match can be found is
PCRE_ERROR_PARTIAL returned instead of PCRE_ERROR_NOMATCH. In other words,
PCRE_PARTIAL_SOFT says that the caller is prepared to handle a partial match,
but only if no complete match can be found.
</P>
<P>
If PCRE_PARTIAL_HARD is set, it overrides PCRE_PARTIAL_SOFT. In this case, if a
partial match is found, <b>pcre_exec()</b> immediately returns
PCRE_ERROR_PARTIAL, without considering any other alternatives. In other words,
when PCRE_PARTIAL_HARD is set, a partial match is considered to be more
important that an alternative complete match.
</P>
<P>
In both cases, the portion of the string that was inspected when the partial
match was found is set as the first matching string. There is a more detailed
discussion of partial and multi-segment matching, with examples, in the
<a href="pcrepartial.html"><b>pcrepartial</b></a>
documentation.
</P>
<br><b>
The string to be matched by <b>pcre_exec()</b>
</b><br>
<P>
The subject string is passed to <b>pcre_exec()</b> as a pointer in
<i>subject</i>, a length in <i>length</i>, and a starting offset in
<i>startoffset</i>. The units for <i>length</i> and <i>startoffset</i> are bytes
for the 8-bit library, 16-bit data items for the 16-bit library, and 32-bit
data items for the 32-bit library.
</P>
<P>
If <i>startoffset</i> is negative or greater than the length of the subject,
<b>pcre_exec()</b> returns PCRE_ERROR_BADOFFSET. When the starting offset is
zero, the search for a match starts at the beginning of the subject, and this
is by far the most common case. In UTF-8 or UTF-16 mode, the offset must point
to the start of a character, or the end of the subject (in UTF-32 mode, one
data unit equals one character, so all offsets are valid). Unlike the pattern
string, the subject may contain binary zeroes.
</P>
<P>
A non-zero starting offset is useful when searching for another match in the
same subject by calling <b>pcre_exec()</b> again after a previous success.
Setting <i>startoffset</i> differs from just passing over a shortened string and
setting PCRE_NOTBOL in the case of a pattern that begins with any kind of
lookbehind. For example, consider the pattern
<pre>
  \Biss\B
</pre>
which finds occurrences of "iss" in the middle of words. (\B matches only if
the current position in the subject is not a word boundary.) When applied to
the string "Mississipi" the first call to <b>pcre_exec()</b> finds the first
occurrence. If <b>pcre_exec()</b> is called again with just the remainder of the
subject, namely "issipi", it does not match, because \B is always false at the
start of the subject, which is deemed to be a word boundary. However, if
<b>pcre_exec()</b> is passed the entire string again, but with <i>startoffset</i>
set to 4, it finds the second occurrence of "iss" because it is able to look
behind the starting point to discover that it is preceded by a letter.
</P>
<P>
Finding all the matches in a subject is tricky when the pattern can match an
empty string. It is possible to emulate Perl's /g behaviour by first trying the
match again at the same offset, with the PCRE_NOTEMPTY_ATSTART and
PCRE_ANCHORED options, and then if that fails, advancing the starting offset
and trying an ordinary match again. There is some code that demonstrates how to
do this in the
<a href="pcredemo.html"><b>pcredemo</b></a>
sample program. In the most general case, you have to check to see if the
newline convention recognizes CRLF as a newline, and if so, and the current
character is CR followed by LF, advance the starting offset by two characters
instead of one.
</P>
<P>
If a non-zero starting offset is passed when the pattern is anchored, one
attempt to match at the given offset is made. This can only succeed if the
pattern does not require the match to be at the start of the subject.
</P>
<br><b>
How <b>pcre_exec()</b> returns captured substrings
</b><br>
<P>
In general, a pattern matches a certain portion of the subject, and in
addition, further substrings from the subject may be picked out by parts of the
pattern. Following the usage in Jeffrey Friedl's book, this is called
"capturing" in what follows, and the phrase "capturing subpattern" is used for
a fragment of a pattern that picks out a substring. PCRE supports several other
kinds of parenthesized subpattern that do not cause substrings to be captured.
</P>
<P>
Captured substrings are returned to the caller via a vector of integers whose
address is passed in <i>ovector</i>. The number of elements in the vector is
passed in <i>ovecsize</i>, which must be a non-negative number. <b>Note</b>: this
argument is NOT the size of <i>ovector</i> in bytes.
</P>
<P>
The first two-thirds of the vector is used to pass back captured substrings,
each substring using a pair of integers. The remaining third of the vector is
used as workspace by <b>pcre_exec()</b> while matching capturing subpatterns,
and is not available for passing back information. The number passed in
<i>ovecsize</i> should always be a multiple of three. If it is not, it is
rounded down.
</P>
<P>
When a match is successful, information about captured substrings is returned
in pairs of integers, starting at the beginning of <i>ovector</i>, and
continuing up to two-thirds of its length at the most. The first element of
each pair is set to the offset of the first character in a substring, and the
second is set to the offset of the first character after the end of a
substring. These values are always data unit offsets, even in UTF mode. They
are byte offsets in the 8-bit library, 16-bit data item offsets in the 16-bit
library, and 32-bit data item offsets in the 32-bit library. <b>Note</b>: they
are not character counts.
</P>
<P>
The first pair of integers, <i>ovector[0]</i> and <i>ovector[1]</i>, identify the
portion of the subject string matched by the entire pattern. The next pair is
used for the first capturing subpattern, and so on. The value returned by
<b>pcre_exec()</b> is one more than the highest numbered pair that has been set.
For example, if two substrings have been captured, the returned value is 3. If
there are no capturing subpatterns, the return value from a successful match is
1, indicating that just the first pair of offsets has been set.
</P>
<P>
If a capturing subpattern is matched repeatedly, it is the last portion of the
string that it matched that is returned.
</P>
<P>
If the vector is too small to hold all the captured substring offsets, it is
used as far as possible (up to two-thirds of its length), and the function
returns a value of zero. If neither the actual string matched nor any captured
substrings are of interest, <b>pcre_exec()</b> may be called with <i>ovector</i>
passed as NULL and <i>ovecsize</i> as zero. However, if the pattern contains
back references and the <i>ovector</i> is not big enough to remember the related
substrings, PCRE has to get additional memory for use during matching. Thus it
is usually advisable to supply an <i>ovector</i> of reasonable size.
</P>
<P>
There are some cases where zero is returned (indicating vector overflow) when
in fact the vector is exactly the right size for the final match. For example,
consider the pattern
<pre>
  (a)(?:(b)c|bd)
</pre>
If a vector of 6 elements (allowing for only 1 captured substring) is given
with subject string "abd", <b>pcre_exec()</b> will try to set the second
captured string, thereby recording a vector overflow, before failing to match
"c" and backing up to try the second alternative. The zero return, however,
does correctly indicate that the maximum number of slots (namely 2) have been
filled. In similar cases where there is temporary overflow, but the final
number of used slots is actually less than the maximum, a non-zero value is
returned.
</P>
<P>
The <b>pcre_fullinfo()</b> function can be used to find out how many capturing
subpatterns there are in a compiled pattern. The smallest size for
<i>ovector</i> that will allow for <i>n</i> captured substrings, in addition to
the offsets of the substring matched by the whole pattern, is (<i>n</i>+1)*3.
</P>
<P>
It is possible for capturing subpattern number <i>n+1</i> to match some part of
the subject when subpattern <i>n</i> has not been used at all. For example, if
the string "abc" is matched against the pattern (a|(z))(bc) the return from the
function is 4, and subpatterns 1 and 3 are matched, but 2 is not. When this
happens, both values in the offset pairs corresponding to unused subpatterns
are set to -1.
</P>
<P>
Offset values that correspond to unused subpatterns at the end of the
expression are also set to -1. For example, if the string "abc" is matched
against the pattern (abc)(x(yz)?)? subpatterns 2 and 3 are not matched. The
return from the function is 2, because the highest used capturing subpattern
number is 1, and the offsets for for the second and third capturing subpatterns
(assuming the vector is large enough, of course) are set to -1.
</P>
<P>
<b>Note</b>: Elements in the first two-thirds of <i>ovector</i> that do not
correspond to capturing parentheses in the pattern are never changed. That is,
if a pattern contains <i>n</i> capturing parentheses, no more than
<i>ovector[0]</i> to <i>ovector[2n+1]</i> are set by <b>pcre_exec()</b>. The other
elements (in the first two-thirds) retain whatever values they previously had.
</P>
<P>
Some convenience functions are provided for extracting the captured substrings
as separate strings. These are described below.
<a name="errorlist"></a></P>
<br><b>
Error return values from <b>pcre_exec()</b>
</b><br>
<P>
If <b>pcre_exec()</b> fails, it returns a negative number. The following are
defined in the header file:
<pre>
  PCRE_ERROR_NOMATCH        (-1)
</pre>
The subject string did not match the pattern.
<pre>
  PCRE_ERROR_NULL           (-2)
</pre>
Either <i>code</i> or <i>subject</i> was passed as NULL, or <i>ovector</i> was
NULL and <i>ovecsize</i> was not zero.
<pre>
  PCRE_ERROR_BADOPTION      (-3)
</pre>
An unrecognized bit was set in the <i>options</i> argument.
<pre>
  PCRE_ERROR_BADMAGIC       (-4)
</pre>
PCRE stores a 4-byte "magic number" at the start of the compiled code, to catch
the case when it is passed a junk pointer and to detect when a pattern that was
compiled in an environment of one endianness is run in an environment with the
other endianness. This is the error that PCRE gives when the magic number is
not present.
<pre>
  PCRE_ERROR_UNKNOWN_OPCODE (-5)
</pre>
While running the pattern match, an unknown item was encountered in the
compiled pattern. This error could be caused by a bug in PCRE or by overwriting
of the compiled pattern.
<pre>
  PCRE_ERROR_NOMEMORY       (-6)
</pre>
If a pattern contains back references, but the <i>ovector</i> that is passed to
<b>pcre_exec()</b> is not big enough to remember the referenced substrings, PCRE
gets a block of memory at the start of matching to use for this purpose. If the
call via <b>pcre_malloc()</b> fails, this error is given. The memory is
automatically freed at the end of matching.
</P>
<P>
This error is also given if <b>pcre_stack_malloc()</b> fails in
<b>pcre_exec()</b>. This can happen only when PCRE has been compiled with
<b>--disable-stack-for-recursion</b>.
<pre>
  PCRE_ERROR_NOSUBSTRING    (-7)
</pre>
This error is used by the <b>pcre_copy_substring()</b>,
<b>pcre_get_substring()</b>, and <b>pcre_get_substring_list()</b> functions (see
below). It is never returned by <b>pcre_exec()</b>.
<pre>
  PCRE_ERROR_MATCHLIMIT     (-8)
</pre>
The backtracking limit, as specified by the <i>match_limit</i> field in a
<b>pcre_extra</b> structure (or defaulted) was reached. See the description
above.
<pre>
  PCRE_ERROR_CALLOUT        (-9)
</pre>
This error is never generated by <b>pcre_exec()</b> itself. It is provided for
use by callout functions that want to yield a distinctive error code. See the
<a href="pcrecallout.html"><b>pcrecallout</b></a>
documentation for details.
<pre>
  PCRE_ERROR_BADUTF8        (-10)
</pre>
A string that contains an invalid UTF-8 byte sequence was passed as a subject,
and the PCRE_NO_UTF8_CHECK option was not set. If the size of the output vector
(<i>ovecsize</i>) is at least 2, the byte offset to the start of the the invalid
UTF-8 character is placed in the first element, and a reason code is placed in
the second element. The reason codes are listed in the
<a href="#badutf8reasons">following section.</a>
For backward compatibility, if PCRE_PARTIAL_HARD is set and the problem is a
truncated UTF-8 character at the end of the subject (reason codes 1 to 5),
PCRE_ERROR_SHORTUTF8 is returned instead of PCRE_ERROR_BADUTF8.
<pre>
  PCRE_ERROR_BADUTF8_OFFSET (-11)
</pre>
The UTF-8 byte sequence that was passed as a subject was checked and found to
be valid (the PCRE_NO_UTF8_CHECK option was not set), but the value of
<i>startoffset</i> did not point to the beginning of a UTF-8 character or the
end of the subject.
<pre>
  PCRE_ERROR_PARTIAL        (-12)
</pre>
The subject string did not match, but it did match partially. See the
<a href="pcrepartial.html"><b>pcrepartial</b></a>
documentation for details of partial matching.
<pre>
  PCRE_ERROR_BADPARTIAL     (-13)
</pre>
This code is no longer in use. It was formerly returned when the PCRE_PARTIAL
option was used with a compiled pattern containing items that were not
supported for partial matching. From release 8.00 onwards, there are no
restrictions on partial matching.
<pre>
  PCRE_ERROR_INTERNAL       (-14)
</pre>
An unexpected internal error has occurred. This error could be caused by a bug
in PCRE or by overwriting of the compiled pattern.
<pre>
  PCRE_ERROR_BADCOUNT       (-15)
</pre>
This error is given if the value of the <i>ovecsize</i> argument is negative.
<pre>
  PCRE_ERROR_RECURSIONLIMIT (-21)
</pre>
The internal recursion limit, as specified by the <i>match_limit_recursion</i>
field in a <b>pcre_extra</b> structure (or defaulted) was reached. See the
description above.
<pre>
  PCRE_ERROR_BADNEWLINE     (-23)
</pre>
An invalid combination of PCRE_NEWLINE_<i>xxx</i> options was given.
<pre>
  PCRE_ERROR_BADOFFSET      (-24)
</pre>
The value of <i>startoffset</i> was negative or greater than the length of the
subject, that is, the value in <i>length</i>.
<pre>
  PCRE_ERROR_SHORTUTF8      (-25)
</pre>
This error is returned instead of PCRE_ERROR_BADUTF8 when the subject string
ends with a truncated UTF-8 character and the PCRE_PARTIAL_HARD option is set.
Information about the failure is returned as for PCRE_ERROR_BADUTF8. It is in
fact sufficient to detect this case, but this special error code for
PCRE_PARTIAL_HARD precedes the implementation of returned information; it is
retained for backwards compatibility.
<pre>
  PCRE_ERROR_RECURSELOOP    (-26)
</pre>
This error is returned when <b>pcre_exec()</b> detects a recursion loop within
the pattern. Specifically, it means that either the whole pattern or a
subpattern has been called recursively for the second time at the same position
in the subject string. Some simple patterns that might do this are detected and
faulted at compile time, but more complicated cases, in particular mutual
recursions between two different subpatterns, cannot be detected until run
time.
<pre>
  PCRE_ERROR_JIT_STACKLIMIT (-27)
</pre>
This error is returned when a pattern that was successfully studied using a
JIT compile option is being matched, but the memory available for the
just-in-time processing stack is not large enough. See the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation for more details.
<pre>
  PCRE_ERROR_BADMODE        (-28)
</pre>
This error is given if a pattern that was compiled by the 8-bit library is
passed to a 16-bit or 32-bit library function, or vice versa.
<pre>
  PCRE_ERROR_BADENDIANNESS  (-29)
</pre>
This error is given if a pattern that was compiled and saved is reloaded on a
host with different endianness. The utility function
<b>pcre_pattern_to_host_byte_order()</b> can be used to convert such a pattern
so that it runs on the new host.
<pre>
  PCRE_ERROR_JIT_BADOPTION
</pre>
This error is returned when a pattern that was successfully studied using a JIT
compile option is being matched, but the matching mode (partial or complete
match) does not correspond to any JIT compilation mode. When the JIT fast path
function is used, this error may be also given for invalid options. See the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation for more details.
<pre>
  PCRE_ERROR_BADLENGTH      (-32)
</pre>
This error is given if <b>pcre_exec()</b> is called with a negative value for
the <i>length</i> argument.
</P>
<P>
Error numbers -16 to -20, -22, and 30 are not used by <b>pcre_exec()</b>.
<a name="badutf8reasons"></a></P>
<br><b>
Reason codes for invalid UTF-8 strings
</b><br>
<P>
This section applies only to the 8-bit library. The corresponding information
for the 16-bit and 32-bit libraries is given in the
<a href="pcre16.html"><b>pcre16</b></a>
and
<a href="pcre32.html"><b>pcre32</b></a>
pages.
</P>
<P>
When <b>pcre_exec()</b> returns either PCRE_ERROR_BADUTF8 or
PCRE_ERROR_SHORTUTF8, and the size of the output vector (<i>ovecsize</i>) is at
least 2, the offset of the start of the invalid UTF-8 character is placed in
the first output vector element (<i>ovector[0]</i>) and a reason code is placed
in the second element (<i>ovector[1]</i>). The reason codes are given names in
the <b>pcre.h</b> header file:
<pre>
  PCRE_UTF8_ERR1
  PCRE_UTF8_ERR2
  PCRE_UTF8_ERR3
  PCRE_UTF8_ERR4
  PCRE_UTF8_ERR5
</pre>
The string ends with a truncated UTF-8 character; the code specifies how many
bytes are missing (1 to 5). Although RFC 3629 restricts UTF-8 characters to be
no longer than 4 bytes, the encoding scheme (originally defined by RFC 2279)
allows for up to 6 bytes, and this is checked first; hence the possibility of
4 or 5 missing bytes.
<pre>
  PCRE_UTF8_ERR6
  PCRE_UTF8_ERR7
  PCRE_UTF8_ERR8
  PCRE_UTF8_ERR9
  PCRE_UTF8_ERR10
</pre>
The two most significant bits of the 2nd, 3rd, 4th, 5th, or 6th byte of the
character do not have the binary value 0b10 (that is, either the most
significant bit is 0, or the next bit is 1).
<pre>
  PCRE_UTF8_ERR11
  PCRE_UTF8_ERR12
</pre>
A character that is valid by the RFC 2279 rules is either 5 or 6 bytes long;
these code points are excluded by RFC 3629.
<pre>
  PCRE_UTF8_ERR13
</pre>
A 4-byte character has a value greater than 0x10fff; these code points are
excluded by RFC 3629.
<pre>
  PCRE_UTF8_ERR14
</pre>
A 3-byte character has a value in the range 0xd800 to 0xdfff; this range of
code points are reserved by RFC 3629 for use with UTF-16, and so are excluded
from UTF-8.
<pre>
  PCRE_UTF8_ERR15
  PCRE_UTF8_ERR16
  PCRE_UTF8_ERR17
  PCRE_UTF8_ERR18
  PCRE_UTF8_ERR19
</pre>
A 2-, 3-, 4-, 5-, or 6-byte character is "overlong", that is, it codes for a
value that can be represented by fewer bytes, which is invalid. For example,
the two bytes 0xc0, 0xae give the value 0x2e, whose correct coding uses just
one byte.
<pre>
  PCRE_UTF8_ERR20
</pre>
The two most significant bits of the first byte of a character have the binary
value 0b10 (that is, the most significant bit is 1 and the second is 0). Such a
byte can only validly occur as the second or subsequent byte of a multi-byte
character.
<pre>
  PCRE_UTF8_ERR21
</pre>
The first byte of a character has the value 0xfe or 0xff. These values can
never occur in a valid UTF-8 string.
<pre>
  PCRE_UTF8_ERR22
</pre>
This error code was formerly used when the presence of a so-called
"non-character" caused an error. Unicode corrigendum #9 makes it clear that
such characters should not cause a string to be rejected, and so this code is
no longer in use and is never returned.
</P>
<br><a name="SEC18" href="#TOC1">EXTRACTING CAPTURED SUBSTRINGS BY NUMBER</a><br>
<P>
<b>int pcre_copy_substring(const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>, char *<i>buffer</i>,</b>
<b>     int <i>buffersize</i>);</b>
<br>
<br>
<b>int pcre_get_substring(const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, int <i>stringnumber</i>,</b>
<b>     const char **<i>stringptr</i>);</b>
<br>
<br>
<b>int pcre_get_substring_list(const char *<i>subject</i>,</b>
<b>     int *<i>ovector</i>, int <i>stringcount</i>, const char ***<i>listptr</i>);</b>
</P>
<P>
Captured substrings can be accessed directly by using the offsets returned by
<b>pcre_exec()</b> in <i>ovector</i>. For convenience, the functions
<b>pcre_copy_substring()</b>, <b>pcre_get_substring()</b>, and
<b>pcre_get_substring_list()</b> are provided for extracting captured substrings
as new, separate, zero-terminated strings. These functions identify substrings
by number. The next section describes functions for extracting named
substrings.
</P>
<P>
A substring that contains a binary zero is correctly extracted and has a
further zero added on the end, but the result is not, of course, a C string.
However, you can process such a string by referring to the length that is
returned by <b>pcre_copy_substring()</b> and <b>pcre_get_substring()</b>.
Unfortunately, the interface to <b>pcre_get_substring_list()</b> is not adequate
for handling strings containing binary zeros, because the end of the final
string is not independently indicated.
</P>
<P>
The first three arguments are the same for all three of these functions:
<i>subject</i> is the subject string that has just been successfully matched,
<i>ovector</i> is a pointer to the vector of integer offsets that was passed to
<b>pcre_exec()</b>, and <i>stringcount</i> is the number of substrings that were
captured by the match, including the substring that matched the entire regular
expression. This is the value returned by <b>pcre_exec()</b> if it is greater
than zero. If <b>pcre_exec()</b> returned zero, indicating that it ran out of
space in <i>ovector</i>, the value passed as <i>stringcount</i> should be the
number of elements in the vector divided by three.
</P>
<P>
The functions <b>pcre_copy_substring()</b> and <b>pcre_get_substring()</b>
extract a single substring, whose number is given as <i>stringnumber</i>. A
value of zero extracts the substring that matched the entire pattern, whereas
higher values extract the captured substrings. For <b>pcre_copy_substring()</b>,
the string is placed in <i>buffer</i>, whose length is given by
<i>buffersize</i>, while for <b>pcre_get_substring()</b> a new block of memory is
obtained via <b>pcre_malloc</b>, and its address is returned via
<i>stringptr</i>. The yield of the function is the length of the string, not
including the terminating zero, or one of these error codes:
<pre>
  PCRE_ERROR_NOMEMORY       (-6)
</pre>
The buffer was too small for <b>pcre_copy_substring()</b>, or the attempt to get
memory failed for <b>pcre_get_substring()</b>.
<pre>
  PCRE_ERROR_NOSUBSTRING    (-7)
</pre>
There is no substring whose number is <i>stringnumber</i>.
</P>
<P>
The <b>pcre_get_substring_list()</b> function extracts all available substrings
and builds a list of pointers to them. All this is done in a single block of
memory that is obtained via <b>pcre_malloc</b>. The address of the memory block
is returned via <i>listptr</i>, which is also the start of the list of string
pointers. The end of the list is marked by a NULL pointer. The yield of the
function is zero if all went well, or the error code
<pre>
  PCRE_ERROR_NOMEMORY       (-6)
</pre>
if the attempt to get the memory block failed.
</P>
<P>
When any of these functions encounter a substring that is unset, which can
happen when capturing subpattern number <i>n+1</i> matches some part of the
subject, but subpattern <i>n</i> has not been used at all, they return an empty
string. This can be distinguished from a genuine zero-length substring by
inspecting the appropriate offset in <i>ovector</i>, which is negative for unset
substrings.
</P>
<P>
The two convenience functions <b>pcre_free_substring()</b> and
<b>pcre_free_substring_list()</b> can be used to free the memory returned by
a previous call of <b>pcre_get_substring()</b> or
<b>pcre_get_substring_list()</b>, respectively. They do nothing more than call
the function pointed to by <b>pcre_free</b>, which of course could be called
directly from a C program. However, PCRE is used in some situations where it is
linked via a special interface to another programming language that cannot use
<b>pcre_free</b> directly; it is for these cases that the functions are
provided.
</P>
<br><a name="SEC19" href="#TOC1">EXTRACTING CAPTURED SUBSTRINGS BY NAME</a><br>
<P>
<b>int pcre_get_stringnumber(const pcre *<i>code</i>,</b>
<b>     const char *<i>name</i>);</b>
<br>
<br>
<b>int pcre_copy_named_substring(const pcre *<i>code</i>,</b>
<b>     const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, const char *<i>stringname</i>,</b>
<b>     char *<i>buffer</i>, int <i>buffersize</i>);</b>
<br>
<br>
<b>int pcre_get_named_substring(const pcre *<i>code</i>,</b>
<b>     const char *<i>subject</i>, int *<i>ovector</i>,</b>
<b>     int <i>stringcount</i>, const char *<i>stringname</i>,</b>
<b>     const char **<i>stringptr</i>);</b>
</P>
<P>
To extract a substring by name, you first have to find associated number.
For example, for this pattern
<pre>
  (a+)b(?&#60;xxx&#62;\d+)...
</pre>
the number of the subpattern called "xxx" is 2. If the name is known to be
unique (PCRE_DUPNAMES was not set), you can find the number from the name by
calling <b>pcre_get_stringnumber()</b>. The first argument is the compiled
pattern, and the second is the name. The yield of the function is the
subpattern number, or PCRE_ERROR_NOSUBSTRING (-7) if there is no subpattern of
that name.
</P>
<P>
Given the number, you can extract the substring directly, or use one of the
functions described in the previous section. For convenience, there are also
two functions that do the whole job.
</P>
<P>
Most of the arguments of <b>pcre_copy_named_substring()</b> and
<b>pcre_get_named_substring()</b> are the same as those for the similarly named
functions that extract by number. As these are described in the previous
section, they are not re-described here. There are just two differences:
</P>
<P>
First, instead of a substring number, a substring name is given. Second, there
is an extra argument, given at the start, which is a pointer to the compiled
pattern. This is needed in order to gain access to the name-to-number
translation table.
</P>
<P>
These functions call <b>pcre_get_stringnumber()</b>, and if it succeeds, they
then call <b>pcre_copy_substring()</b> or <b>pcre_get_substring()</b>, as
appropriate. <b>NOTE:</b> If PCRE_DUPNAMES is set and there are duplicate names,
the behaviour may not be what you want (see the next section).
</P>
<P>
<b>Warning:</b> If the pattern uses the (?| feature to set up multiple
subpatterns with the same number, as described in the
<a href="pcrepattern.html#dupsubpatternnumber">section on duplicate subpattern numbers</a>
in the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
page, you cannot use names to distinguish the different subpatterns, because
names are not included in the compiled code. The matching process uses only
numbers. For this reason, the use of different names for subpatterns of the
same number causes an error at compile time.
</P>
<br><a name="SEC20" href="#TOC1">DUPLICATE SUBPATTERN NAMES</a><br>
<P>
<b>int pcre_get_stringtable_entries(const pcre *<i>code</i>,</b>
<b>     const char *<i>name</i>, char **<i>first</i>, char **<i>last</i>);</b>
</P>
<P>
When a pattern is compiled with the PCRE_DUPNAMES option, names for subpatterns
are not required to be unique. (Duplicate names are always allowed for
subpatterns with the same number, created by using the (?| feature. Indeed, if
such subpatterns are named, they are required to use the same names.)
</P>
<P>
Normally, patterns with duplicate names are such that in any one match, only
one of the named subpatterns participates. An example is shown in the
<a href="pcrepattern.html"><b>pcrepattern</b></a>
documentation.
</P>
<P>
When duplicates are present, <b>pcre_copy_named_substring()</b> and
<b>pcre_get_named_substring()</b> return the first substring corresponding to
the given name that is set. If none are set, PCRE_ERROR_NOSUBSTRING (-7) is
returned; no data is returned. The <b>pcre_get_stringnumber()</b> function
returns one of the numbers that are associated with the name, but it is not
defined which it is.
</P>
<P>
If you want to get full details of all captured substrings for a given name,
you must use the <b>pcre_get_stringtable_entries()</b> function. The first
argument is the compiled pattern, and the second is the name. The third and
fourth are pointers to variables which are updated by the function. After it
has run, they point to the first and last entries in the name-to-number table
for the given name. The function itself returns the length of each entry, or
PCRE_ERROR_NOSUBSTRING (-7) if there are none. The format of the table is
described above in the section entitled <i>Information about a pattern</i>
<a href="#infoaboutpattern">above.</a>
Given all the relevant entries for the name, you can extract each of their
numbers, and hence the captured data, if any.
</P>
<br><a name="SEC21" href="#TOC1">FINDING ALL POSSIBLE MATCHES</a><br>
<P>
The traditional matching function uses a similar algorithm to Perl, which stops
when it finds the first match, starting at a given point in the subject. If you
want to find all possible matches, or the longest possible match, consider
using the alternative matching function (see below) instead. If you cannot use
the alternative function, but still need to find all possible matches, you
can kludge it up by making use of the callout facility, which is described in
the
<a href="pcrecallout.html"><b>pcrecallout</b></a>
documentation.
</P>
<P>
What you have to do is to insert a callout right at the end of the pattern.
When your callout function is called, extract and save the current matched
substring. Then return 1, which forces <b>pcre_exec()</b> to backtrack and try
other alternatives. Ultimately, when it runs out of matches, <b>pcre_exec()</b>
will yield PCRE_ERROR_NOMATCH.
</P>
<br><a name="SEC22" href="#TOC1">OBTAINING AN ESTIMATE OF STACK USAGE</a><br>
<P>
Matching certain patterns using <b>pcre_exec()</b> can use a lot of process
stack, which in certain environments can be rather limited in size. Some users
find it helpful to have an estimate of the amount of stack that is used by
<b>pcre_exec()</b>, to help them set recursion limits, as described in the
<a href="pcrestack.html"><b>pcrestack</b></a>
documentation. The estimate that is output by <b>pcretest</b> when called with
the <b>-m</b> and <b>-C</b> options is obtained by calling <b>pcre_exec</b> with
the values NULL, NULL, NULL, -999, and -999 for its first five arguments.
</P>
<P>
Normally, if its first argument is NULL, <b>pcre_exec()</b> immediately returns
the negative error code PCRE_ERROR_NULL, but with this special combination of
arguments, it returns instead a negative number whose absolute value is the
approximate stack frame size in bytes. (A negative number is used so that it is
clear that no match has happened.) The value is approximate because in some
cases, recursive calls to <b>pcre_exec()</b> occur when there are one or two
additional variables on the stack.
</P>
<P>
If PCRE has been compiled to use the heap instead of the stack for recursion,
the value returned is the size of each block that is obtained from the heap.
<a name="dfamatch"></a></P>
<br><a name="SEC23" href="#TOC1">MATCHING A PATTERN: THE ALTERNATIVE FUNCTION</a><br>
<P>
<b>int pcre_dfa_exec(const pcre *<i>code</i>, const pcre_extra *<i>extra</i>,</b>
<b>     const char *<i>subject</i>, int <i>length</i>, int <i>startoffset</i>,</b>
<b>     int <i>options</i>, int *<i>ovector</i>, int <i>ovecsize</i>,</b>
<b>     int *<i>workspace</i>, int <i>wscount</i>);</b>
</P>
<P>
The function <b>pcre_dfa_exec()</b> is called to match a subject string against
a compiled pattern, using a matching algorithm that scans the subject string
just once, and does not backtrack. This has different characteristics to the
normal algorithm, and is not compatible with Perl. Some of the features of PCRE
patterns are not supported. Nevertheless, there are times when this kind of
matching can be useful. For a discussion of the two matching algorithms, and a
list of features that <b>pcre_dfa_exec()</b> does not support, see the
<a href="pcrematching.html"><b>pcrematching</b></a>
documentation.
</P>
<P>
The arguments for the <b>pcre_dfa_exec()</b> function are the same as for
<b>pcre_exec()</b>, plus two extras. The <i>ovector</i> argument is used in a
different way, and this is described below. The other common arguments are used
in the same way as for <b>pcre_exec()</b>, so their description is not repeated
here.
</P>
<P>
The two additional arguments provide workspace for the function. The workspace
vector should contain at least 20 elements. It is used for keeping track of
multiple paths through the pattern tree. More workspace will be needed for
patterns and subjects where there are a lot of potential matches.
</P>
<P>
Here is an example of a simple call to <b>pcre_dfa_exec()</b>:
<pre>
  int rc;
  int ovector[10];
  int wspace[20];
  rc = pcre_dfa_exec(
    re,             /* result of pcre_compile() */
    NULL,           /* we didn't study the pattern */
    "some string",  /* the subject string */
    11,             /* the length of the subject string */
    0,              /* start at offset 0 in the subject */
    0,              /* default options */
    ovector,        /* vector of integers for substring information */
    10,             /* number of elements (NOT size in bytes) */
    wspace,         /* working space vector */
    20);            /* number of elements (NOT size in bytes) */
</PRE>
</P>
<br><b>
Option bits for <b>pcre_dfa_exec()</b>
</b><br>
<P>
The unused bits of the <i>options</i> argument for <b>pcre_dfa_exec()</b> must be
zero. The only bits that may be set are PCRE_ANCHORED, PCRE_NEWLINE_<i>xxx</i>,
PCRE_NOTBOL, PCRE_NOTEOL, PCRE_NOTEMPTY, PCRE_NOTEMPTY_ATSTART,
PCRE_NO_UTF8_CHECK, PCRE_BSR_ANYCRLF, PCRE_BSR_UNICODE, PCRE_NO_START_OPTIMIZE,
PCRE_PARTIAL_HARD, PCRE_PARTIAL_SOFT, PCRE_DFA_SHORTEST, and PCRE_DFA_RESTART.
All but the last four of these are exactly the same as for <b>pcre_exec()</b>,
so their description is not repeated here.
<pre>
  PCRE_PARTIAL_HARD
  PCRE_PARTIAL_SOFT
</pre>
These have the same general effect as they do for <b>pcre_exec()</b>, but the
details are slightly different. When PCRE_PARTIAL_HARD is set for
<b>pcre_dfa_exec()</b>, it returns PCRE_ERROR_PARTIAL if the end of the subject
is reached and there is still at least one matching possibility that requires
additional characters. This happens even if some complete matches have also
been found. When PCRE_PARTIAL_SOFT is set, the return code PCRE_ERROR_NOMATCH
is converted into PCRE_ERROR_PARTIAL if the end of the subject is reached,
there have been no complete matches, but there is still at least one matching
possibility. The portion of the string that was inspected when the longest
partial match was found is set as the first matching string in both cases.
There is a more detailed discussion of partial and multi-segment matching, with
examples, in the
<a href="pcrepartial.html"><b>pcrepartial</b></a>
documentation.
<pre>
  PCRE_DFA_SHORTEST
</pre>
Setting the PCRE_DFA_SHORTEST option causes the matching algorithm to stop as
soon as it has found one match. Because of the way the alternative algorithm
works, this is necessarily the shortest possible match at the first possible
matching point in the subject string.
<pre>
  PCRE_DFA_RESTART
</pre>
When <b>pcre_dfa_exec()</b> returns a partial match, it is possible to call it
again, with additional subject characters, and have it continue with the same
match. The PCRE_DFA_RESTART option requests this action; when it is set, the
<i>workspace</i> and <i>wscount</i> options must reference the same vector as
before because data about the match so far is left in them after a partial
match. There is more discussion of this facility in the
<a href="pcrepartial.html"><b>pcrepartial</b></a>
documentation.
</P>
<br><b>
Successful returns from <b>pcre_dfa_exec()</b>
</b><br>
<P>
When <b>pcre_dfa_exec()</b> succeeds, it may have matched more than one
substring in the subject. Note, however, that all the matches from one run of
the function start at the same point in the subject. The shorter matches are
all initial substrings of the longer matches. For example, if the pattern
<pre>
  &#60;.*&#62;
</pre>
is matched against the string
<pre>
  This is &#60;something&#62; &#60;something else&#62; &#60;something further&#62; no more
</pre>
the three matched strings are
<pre>
  &#60;something&#62;
  &#60;something&#62; &#60;something else&#62;
  &#60;something&#62; &#60;something else&#62; &#60;something further&#62;
</pre>
On success, the yield of the function is a number greater than zero, which is
the number of matched substrings. The substrings themselves are returned in
<i>ovector</i>. Each string uses two elements; the first is the offset to the
start, and the second is the offset to the end. In fact, all the strings have
the same start offset. (Space could have been saved by giving this only once,
but it was decided to retain some compatibility with the way <b>pcre_exec()</b>
returns data, even though the meaning of the strings is different.)
</P>
<P>
The strings are returned in reverse order of length; that is, the longest
matching string is given first. If there were too many matches to fit into
<i>ovector</i>, the yield of the function is zero, and the vector is filled with
the longest matches. Unlike <b>pcre_exec()</b>, <b>pcre_dfa_exec()</b> can use
the entire <i>ovector</i> for returning matched strings.
</P>
<P>
NOTE: PCRE's "auto-possessification" optimization usually applies to character
repeats at the end of a pattern (as well as internally). For example, the
pattern "a\d+" is compiled as if it were "a\d++" because there is no point
even considering the possibility of backtracking into the repeated digits. For
DFA matching, this means that only one possible match is found. If you really
do want multiple matches in such cases, either use an ungreedy repeat
("a\d+?") or set the PCRE_NO_AUTO_POSSESS option when compiling.
</P>
<br><b>
Error returns from <b>pcre_dfa_exec()</b>
</b><br>
<P>
The <b>pcre_dfa_exec()</b> function returns a negative number when it fails.
Many of the errors are the same as for <b>pcre_exec()</b>, and these are
described
<a href="#errorlist">above.</a>
There are in addition the following errors that are specific to
<b>pcre_dfa_exec()</b>:
<pre>
  PCRE_ERROR_DFA_UITEM      (-16)
</pre>
This return is given if <b>pcre_dfa_exec()</b> encounters an item in the pattern
that it does not support, for instance, the use of \C or a back reference.
<pre>
  PCRE_ERROR_DFA_UCOND      (-17)
</pre>
This return is given if <b>pcre_dfa_exec()</b> encounters a condition item that
uses a back reference for the condition, or a test for recursion in a specific
group. These are not supported.
<pre>
  PCRE_ERROR_DFA_UMLIMIT    (-18)
</pre>
This return is given if <b>pcre_dfa_exec()</b> is called with an <i>extra</i>
block that contains a setting of the <i>match_limit</i> or
<i>match_limit_recursion</i> fields. This is not supported (these fields are
meaningless for DFA matching).
<pre>
  PCRE_ERROR_DFA_WSSIZE     (-19)
</pre>
This return is given if <b>pcre_dfa_exec()</b> runs out of space in the
<i>workspace</i> vector.
<pre>
  PCRE_ERROR_DFA_RECURSE    (-20)
</pre>
When a recursive subpattern is processed, the matching function calls itself
recursively, using private vectors for <i>ovector</i> and <i>workspace</i>. This
error is given if the output vector is not large enough. This should be
extremely rare, as a vector of size 1000 is used.
<pre>
  PCRE_ERROR_DFA_BADRESTART (-30)
</pre>
When <b>pcre_dfa_exec()</b> is called with the <b>PCRE_DFA_RESTART</b> option,
some plausibility checks are made on the contents of the workspace, which
should contain data about the previous partial match. If any of these checks
fail, this error is given.
</P>
<br><a name="SEC24" href="#TOC1">SEE ALSO</a><br>
<P>
<b>pcre16</b>(3), <b>pcre32</b>(3), <b>pcrebuild</b>(3), <b>pcrecallout</b>(3),
<b>pcrecpp(3)</b>(3), <b>pcrematching</b>(3), <b>pcrepartial</b>(3),
<b>pcreposix</b>(3), <b>pcreprecompile</b>(3), <b>pcresample</b>(3),
<b>pcrestack</b>(3).
</P>
<br><a name="SEC25" href="#TOC1">AUTHOR</a><br>
<P>
Philip Hazel
<br>
University Computing Service
<br>
Cambridge CB2 3QH, England.
<br>
</P>
<br><a name="SEC26" href="#TOC1">REVISION</a><br>
<P>
Last updated: 09 February 2014
<br>
Copyright &copy; 1997-2014 University of Cambridge.
<br>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
