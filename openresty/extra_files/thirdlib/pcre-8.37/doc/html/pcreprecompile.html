<html>
<head>
<title>pcreprecompile specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcreprecompile man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<ul>
<li><a name="TOC1" href="#SEC1">SAVING AND RE-USING PRECOMPILED PCRE PATTERNS</a>
<li><a name="TOC2" href="#SEC2">SAVING A COMPILED PATTERN</a>
<li><a name="TOC3" href="#SEC3">RE-USING A PRECOMPILED PATTERN</a>
<li><a name="TOC4" href="#SEC4">COMPATIBILITY WITH DIFFERENT PCRE RELEASES</a>
<li><a name="TOC5" href="#SEC5">AUTHOR</a>
<li><a name="TOC6" href="#SEC6">REVISION</a>
</ul>
<br><a name="SEC1" href="#TOC1">SAVING AND RE-USING PRECOMPILED PCRE PATTERNS</a><br>
<P>
If you are running an application that uses a large number of regular
expression patterns, it may be useful to store them in a precompiled form
instead of having to compile them every time the application is run.
If you are not using any private character tables (see the
<a href="pcre_maketables.html"><b>pcre_maketables()</b></a>
documentation), this is relatively straightforward. If you are using private
tables, it is a little bit more complicated. However, if you are using the
just-in-time optimization feature, it is not possible to save and reload the
JIT data.
</P>
<P>
If you save compiled patterns to a file, you can copy them to a different host
and run them there. If the two hosts have different endianness (byte order),
you should run the <b>pcre[16|32]_pattern_to_host_byte_order()</b> function on the
new host before trying to match the pattern. The matching functions return
PCRE_ERROR_BADENDIANNESS if they detect a pattern with the wrong endianness.
</P>
<P>
Compiling regular expressions with one version of PCRE for use with a different
version is not guaranteed to work and may cause crashes, and saving and
restoring a compiled pattern loses any JIT optimization data.
</P>
<br><a name="SEC2" href="#TOC1">SAVING A COMPILED PATTERN</a><br>
<P>
The value returned by <b>pcre[16|32]_compile()</b> points to a single block of
memory that holds the compiled pattern and associated data. You can find the
length of this block in bytes by calling <b>pcre[16|32]_fullinfo()</b> with an
argument of PCRE_INFO_SIZE. You can then save the data in any appropriate
manner. Here is sample code for the 8-bit library that compiles a pattern and
writes it to a file. It assumes that the variable <i>fd</i> refers to a file
that is open for output:
<pre>
  int erroroffset, rc, size;
  char *error;
  pcre *re;

  re = pcre_compile("my pattern", 0, &error, &erroroffset, NULL);
  if (re == NULL) { ... handle errors ... }
  rc = pcre_fullinfo(re, NULL, PCRE_INFO_SIZE, &size);
  if (rc &#60; 0) { ... handle errors ... }
  rc = fwrite(re, 1, size, fd);
  if (rc != size) { ... handle errors ... }
</pre>
In this example, the bytes that comprise the compiled pattern are copied
exactly. Note that this is binary data that may contain any of the 256 possible
byte values. On systems that make a distinction between binary and non-binary
data, be sure that the file is opened for binary output.
</P>
<P>
If you want to write more than one pattern to a file, you will have to devise a
way of separating them. For binary data, preceding each pattern with its length
is probably the most straightforward approach. Another possibility is to write
out the data in hexadecimal instead of binary, one pattern to a line.
</P>
<P>
Saving compiled patterns in a file is only one possible way of storing them for
later use. They could equally well be saved in a database, or in the memory of
some daemon process that passes them via sockets to the processes that want
them.
</P>
<P>
If the pattern has been studied, it is also possible to save the normal study
data in a similar way to the compiled pattern itself. However, if the
PCRE_STUDY_JIT_COMPILE was used, the just-in-time data that is created cannot
be saved because it is too dependent on the current environment. When studying
generates additional information, <b>pcre[16|32]_study()</b> returns a pointer to a
<b>pcre[16|32]_extra</b> data block. Its format is defined in the
<a href="pcreapi.html#extradata">section on matching a pattern</a>
in the
<a href="pcreapi.html"><b>pcreapi</b></a>
documentation. The <i>study_data</i> field points to the binary study data, and
this is what you must save (not the <b>pcre[16|32]_extra</b> block itself). The
length of the study data can be obtained by calling <b>pcre[16|32]_fullinfo()</b>
with an argument of PCRE_INFO_STUDYSIZE. Remember to check that
<b>pcre[16|32]_study()</b> did return a non-NULL value before trying to save the
study data.
</P>
<br><a name="SEC3" href="#TOC1">RE-USING A PRECOMPILED PATTERN</a><br>
<P>
Re-using a precompiled pattern is straightforward. Having reloaded it into main
memory, called <b>pcre[16|32]_pattern_to_host_byte_order()</b> if necessary, you
pass its pointer to <b>pcre[16|32]_exec()</b> or <b>pcre[16|32]_dfa_exec()</b> in
the usual way.
</P>
<P>
However, if you passed a pointer to custom character tables when the pattern
was compiled (the <i>tableptr</i> argument of <b>pcre[16|32]_compile()</b>), you
must now pass a similar pointer to <b>pcre[16|32]_exec()</b> or
<b>pcre[16|32]_dfa_exec()</b>, because the value saved with the compiled pattern
will obviously be nonsense. A field in a <b>pcre[16|32]_extra()</b> block is used
to pass this data, as described in the
<a href="pcreapi.html#extradata">section on matching a pattern</a>
in the
<a href="pcreapi.html"><b>pcreapi</b></a>
documentation.
</P>
<P>
<b>Warning:</b> The tables that <b>pcre_exec()</b> and <b>pcre_dfa_exec()</b> use
must be the same as those that were used when the pattern was compiled. If this
is not the case, the behaviour is undefined.
</P>
<P>
If you did not provide custom character tables when the pattern was compiled,
the pointer in the compiled pattern is NULL, which causes the matching
functions to use PCRE's internal tables. Thus, you do not need to take any
special action at run time in this case.
</P>
<P>
If you saved study data with the compiled pattern, you need to create your own
<b>pcre[16|32]_extra</b> data block and set the <i>study_data</i> field to point
to the reloaded study data. You must also set the PCRE_EXTRA_STUDY_DATA bit in
the <i>flags</i> field to indicate that study data is present. Then pass the
<b>pcre[16|32]_extra</b> block to the matching function in the usual way. If the
pattern was studied for just-in-time optimization, that data cannot be saved,
and so is lost by a save/restore cycle.
</P>
<br><a name="SEC4" href="#TOC1">COMPATIBILITY WITH DIFFERENT PCRE RELEASES</a><br>
<P>
In general, it is safest to recompile all saved patterns when you update to a
new PCRE release, though not all updates actually require this.
</P>
<br><a name="SEC5" href="#TOC1">AUTHOR</a><br>
<P>
Philip Hazel
<br>
University Computing Service
<br>
Cambridge CB2 3QH, England.
<br>
</P>
<br><a name="SEC6" href="#TOC1">REVISION</a><br>
<P>
Last updated: 12 November 2013
<br>
Copyright &copy; 1997-2013 University of Cambridge.
<br>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
