<html>
<head>
<title>pcrestack specification</title>
</head>
<body bgcolor="#FFFFFF" text="#00005A" link="#0066FF" alink="#3399FF" vlink="#2222BB">
<h1>pcrestack man page</h1>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
<p>
This page is part of the PCRE HTML documentation. It was generated automatically
from the original man page. If there is any nonsense in it, please consult the
man page, in case the conversion went wrong.
<br>
<br><b>
PCRE DISCUSSION OF STACK USAGE
</b><br>
<P>
When you call <b>pcre[16|32]_exec()</b>, it makes use of an internal function
called <b>match()</b>. This calls itself recursively at branch points in the
pattern, in order to remember the state of the match so that it can back up and
try a different alternative if the first one fails. As matching proceeds deeper
and deeper into the tree of possibilities, the recursion depth increases. The
<b>match()</b> function is also called in other circumstances, for example,
whenever a parenthesized sub-pattern is entered, and in certain cases of
repetition.
</P>
<P>
Not all calls of <b>match()</b> increase the recursion depth; for an item such
as a* it may be called several times at the same level, after matching
different numbers of a's. Furthermore, in a number of cases where the result of
the recursive call would immediately be passed back as the result of the
current call (a "tail recursion"), the function is just restarted instead.
</P>
<P>
The above comments apply when <b>pcre[16|32]_exec()</b> is run in its normal
interpretive manner. If the pattern was studied with the
PCRE_STUDY_JIT_COMPILE option, and just-in-time compiling was successful, and
the options passed to <b>pcre[16|32]_exec()</b> were not incompatible, the matching
process uses the JIT-compiled code instead of the <b>match()</b> function. In
this case, the memory requirements are handled entirely differently. See the
<a href="pcrejit.html"><b>pcrejit</b></a>
documentation for details.
</P>
<P>
The <b>pcre[16|32]_dfa_exec()</b> function operates in an entirely different way,
and uses recursion only when there is a regular expression recursion or
subroutine call in the pattern. This includes the processing of assertion and
"once-only" subpatterns, which are handled like subroutine calls. Normally,
these are never very deep, and the limit on the complexity of
<b>pcre[16|32]_dfa_exec()</b> is controlled by the amount of workspace it is given.
However, it is possible to write patterns with runaway infinite recursions;
such patterns will cause <b>pcre[16|32]_dfa_exec()</b> to run out of stack. At
present, there is no protection against this.
</P>
<P>
The comments that follow do NOT apply to <b>pcre[16|32]_dfa_exec()</b>; they are
relevant only for <b>pcre[16|32]_exec()</b> without the JIT optimization.
</P>
<br><b>
Reducing <b>pcre[16|32]_exec()</b>'s stack usage
</b><br>
<P>
Each time that <b>match()</b> is actually called recursively, it uses memory
from the process stack. For certain kinds of pattern and data, very large
amounts of stack may be needed, despite the recognition of "tail recursion".
You can often reduce the amount of recursion, and therefore the amount of stack
used, by modifying the pattern that is being matched. Consider, for example,
this pattern:
<pre>
  ([^&#60;]|&#60;(?!inet))+
</pre>
It matches from wherever it starts until it encounters "&#60;inet" or the end of
the data, and is the kind of pattern that might be used when processing an XML
file. Each iteration of the outer parentheses matches either one character that
is not "&#60;" or a "&#60;" that is not followed by "inet". However, each time a
parenthesis is processed, a recursion occurs, so this formulation uses a stack
frame for each matched character. For a long string, a lot of stack is
required. Consider now this rewritten pattern, which matches exactly the same
strings:
<pre>
  ([^&#60;]++|&#60;(?!inet))+
</pre>
This uses very much less stack, because runs of characters that do not contain
"&#60;" are "swallowed" in one item inside the parentheses. Recursion happens only
when a "&#60;" character that is not followed by "inet" is encountered (and we
assume this is relatively rare). A possessive quantifier is used to stop any
backtracking into the runs of non-"&#60;" characters, but that is not related to
stack usage.
</P>
<P>
This example shows that one way of avoiding stack problems when matching long
subject strings is to write repeated parenthesized subpatterns to match more
than one character whenever possible.
</P>
<br><b>
Compiling PCRE to use heap instead of stack for <b>pcre[16|32]_exec()</b>
</b><br>
<P>
In environments where stack memory is constrained, you might want to compile
PCRE to use heap memory instead of stack for remembering back-up points when
<b>pcre[16|32]_exec()</b> is running. This makes it run a lot more slowly, however.
Details of how to do this are given in the
<a href="pcrebuild.html"><b>pcrebuild</b></a>
documentation. When built in this way, instead of using the stack, PCRE obtains
and frees memory by calling the functions that are pointed to by the
<b>pcre[16|32]_stack_malloc</b> and <b>pcre[16|32]_stack_free</b> variables. By
default, these point to <b>malloc()</b> and <b>free()</b>, but you can replace
the pointers to cause PCRE to use your own functions. Since the block sizes are
always the same, and are always freed in reverse order, it may be possible to
implement customized memory handlers that are more efficient than the standard
functions.
</P>
<br><b>
Limiting <b>pcre[16|32]_exec()</b>'s stack usage
</b><br>
<P>
You can set limits on the number of times that <b>match()</b> is called, both in
total and recursively. If a limit is exceeded, <b>pcre[16|32]_exec()</b> returns an
error code. Setting suitable limits should prevent it from running out of
stack. The default values of the limits are very large, and unlikely ever to
operate. They can be changed when PCRE is built, and they can also be set when
<b>pcre[16|32]_exec()</b> is called. For details of these interfaces, see the
<a href="pcrebuild.html"><b>pcrebuild</b></a>
documentation and the
<a href="pcreapi.html#extradata">section on extra data for <b>pcre[16|32]_exec()</b></a>
in the
<a href="pcreapi.html"><b>pcreapi</b></a>
documentation.
</P>
<P>
As a very rough rule of thumb, you should reckon on about 500 bytes per
recursion. Thus, if you want to limit your stack usage to 8Mb, you should set
the limit at 16000 recursions. A 64Mb stack, on the other hand, can support
around 128000 recursions.
</P>
<P>
In Unix-like environments, the <b>pcretest</b> test program has a command line
option (<b>-S</b>) that can be used to increase the size of its stack. As long
as the stack is large enough, another option (<b>-M</b>) can be used to find the
smallest limits that allow a particular pattern to match a given subject
string. This is done by calling <b>pcre[16|32]_exec()</b> repeatedly with different
limits.
</P>
<br><b>
Obtaining an estimate of stack usage
</b><br>
<P>
The actual amount of stack used per recursion can vary quite a lot, depending
on the compiler that was used to build PCRE and the optimization or debugging
options that were set for it. The rule of thumb value of 500 bytes mentioned
above may be larger or smaller than what is actually needed. A better
approximation can be obtained by running this command:
<pre>
  pcretest -m -C
</pre>
The <b>-C</b> option causes <b>pcretest</b> to output information about the
options with which PCRE was compiled. When <b>-m</b> is also given (before
<b>-C</b>), information about stack use is given in a line like this:
<pre>
  Match recursion uses stack: approximate frame size = 640 bytes
</pre>
The value is approximate because some recursions need a bit more (up to perhaps
16 more bytes).
</P>
<P>
If the above command is given when PCRE is compiled to use the heap instead of
the stack for recursion, the value that is output is the size of each block
that is obtained from the heap.
</P>
<br><b>
Changing stack size in Unix-like systems
</b><br>
<P>
In Unix-like environments, there is not often a problem with the stack unless
very long strings are involved, though the default limit on stack size varies
from system to system. Values from 8Mb to 64Mb are common. You can find your
default limit by running the command:
<pre>
  ulimit -s
</pre>
Unfortunately, the effect of running out of stack is often SIGSEGV, though
sometimes a more explicit error message is given. You can normally increase the
limit on stack size by code such as this:
<pre>
  struct rlimit rlim;
  getrlimit(RLIMIT_STACK, &rlim);
  rlim.rlim_cur = 100*1024*1024;
  setrlimit(RLIMIT_STACK, &rlim);
</pre>
This reads the current limits (soft and hard) using <b>getrlimit()</b>, then
attempts to increase the soft limit to 100Mb using <b>setrlimit()</b>. You must
do this before calling <b>pcre[16|32]_exec()</b>.
</P>
<br><b>
Changing stack size in Mac OS X
</b><br>
<P>
Using <b>setrlimit()</b>, as described above, should also work on Mac OS X. It
is also possible to set a stack size when linking a program. There is a
discussion about stack sizes in Mac OS X at this web site:
<a href="http://developer.apple.com/qa/qa2005/qa1419.html">http://developer.apple.com/qa/qa2005/qa1419.html.</a>
</P>
<br><b>
AUTHOR
</b><br>
<P>
Philip Hazel
<br>
University Computing Service
<br>
Cambridge CB2 3QH, England.
<br>
</P>
<br><b>
REVISION
</b><br>
<P>
Last updated: 24 June 2012
<br>
Copyright &copy; 1997-2012 University of Cambridge.
<br>
<p>
Return to the <a href="index.html">PCRE index page</a>.
</p>
