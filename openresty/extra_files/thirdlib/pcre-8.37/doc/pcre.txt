-----------------------------------------------------------------------------
This file contains a concatenation of the PCRE man pages, converted to plain
text format for ease of searching with a text editor, or for use on systems
that do not have a man page processor. The small individual files that give
synopses of each function in the library have not been included. Neither has
the pcredemo program. There are separate text files for the pcregrep and
pcretest commands.
-----------------------------------------------------------------------------


PCRE(3)                    Library Functions Manual                    PCRE(3)



NAME
       PCRE - Perl-compatible regular expressions (original API)

PLEASE TAKE NOTE

       This  document relates to PCRE releases that use the original API, with
       library names libpcre, libpcre16, and libpcre32. January 2015  saw  the
       first release of a new API, known as PCRE2, with release numbers start-
       ing  at  10.00  and  library   names   libpcre2-8,   libpcre2-16,   and
       libpcre2-32. The old libraries (now called PCRE1) are still being main-
       tained for bug fixes,  but  there  will  be  no  new  development.  New
       projects are advised to use the new PCRE2 libraries.


INTRODUCTION

       The  PCRE  library is a set of functions that implement regular expres-
       sion pattern matching using the same syntax and semantics as Perl, with
       just  a few differences. Some features that appeared in Python and PCRE
       before they appeared in Perl are also available using the  Python  syn-
       tax,  there  is  some  support for one or two .NET and Oniguruma syntax
       items, and there is an option for requesting some  minor  changes  that
       give better JavaScript compatibility.

       Starting with release 8.30, it is possible to compile two separate PCRE
       libraries:  the  original,  which  supports  8-bit  character   strings
       (including  UTF-8  strings),  and a second library that supports 16-bit
       character strings (including UTF-16 strings). The build process  allows
       either  one  or both to be built. The majority of the work to make this
       possible was done by Zoltan Herczeg.

       Starting with release 8.32 it is possible to compile a  third  separate
       PCRE  library  that supports 32-bit character strings (including UTF-32
       strings). The build process allows any combination of the 8-,  16-  and
       32-bit  libraries. The work to make this possible was done by Christian
       Persch.

       The three libraries contain identical sets of  functions,  except  that
       the  names  in  the 16-bit library start with pcre16_ instead of pcre_,
       and the names in the 32-bit  library  start  with  pcre32_  instead  of
       pcre_.  To avoid over-complication and reduce the documentation mainte-
       nance load, most of the documentation describes the 8-bit library, with
       the  differences  for  the  16-bit and 32-bit libraries described sepa-
       rately in the pcre16 and  pcre32  pages.  References  to  functions  or
       structures  of  the  form  pcre[16|32]_xxx  should  be  read as meaning
       "pcre_xxx when using the  8-bit  library,  pcre16_xxx  when  using  the
       16-bit library, or pcre32_xxx when using the 32-bit library".

       The  current implementation of PCRE corresponds approximately with Perl
       5.12, including support for UTF-8/16/32  encoded  strings  and  Unicode
       general  category  properties. However, UTF-8/16/32 and Unicode support
       has to be explicitly enabled; it is not the default. The Unicode tables
       correspond to Unicode release 6.3.0.

       In  addition to the Perl-compatible matching function, PCRE contains an
       alternative function that matches the same compiled patterns in a  dif-
       ferent way. In certain circumstances, the alternative function has some
       advantages.  For a discussion of the two matching algorithms,  see  the
       pcrematching page.

       PCRE  is  written  in C and released as a C library. A number of people
       have written wrappers and interfaces of various kinds.  In  particular,
       Google  Inc.   have  provided a comprehensive C++ wrapper for the 8-bit
       library. This is now included as part of  the  PCRE  distribution.  The
       pcrecpp  page  has  details of this interface. Other people's contribu-
       tions can be found in the Contrib directory at the  primary  FTP  site,
       which is:

       ftp://ftp.csx.cam.ac.uk/pub/software/programming/pcre

       Details  of  exactly which Perl regular expression features are and are
       not supported by PCRE are given in separate documents. See the pcrepat-
       tern  and pcrecompat pages. There is a syntax summary in the pcresyntax
       page.

       Some features of PCRE can be included, excluded, or  changed  when  the
       library  is  built.  The pcre_config() function makes it possible for a
       client to discover which features are  available.  The  features  them-
       selves  are described in the pcrebuild page. Documentation about build-
       ing PCRE for various operating systems can be found in the  README  and
       NON-AUTOTOOLS_BUILD files in the source distribution.

       The  libraries contains a number of undocumented internal functions and
       data tables that are used by more than one  of  the  exported  external
       functions,  but  which  are  not  intended for use by external callers.
       Their names all begin with "_pcre_" or "_pcre16_" or "_pcre32_",  which
       hopefully  will  not provoke any name clashes. In some environments, it
       is possible to control which  external  symbols  are  exported  when  a
       shared  library  is  built, and in these cases the undocumented symbols
       are not exported.


SECURITY CONSIDERATIONS

       If you are using PCRE in a non-UTF application that  permits  users  to
       supply  arbitrary  patterns  for  compilation, you should be aware of a
       feature that allows users to turn on UTF support from within a pattern,
       provided  that  PCRE  was built with UTF support. For example, an 8-bit
       pattern that begins with "(*UTF8)" or "(*UTF)"  turns  on  UTF-8  mode,
       which  interprets  patterns and subjects as strings of UTF-8 characters
       instead of individual 8-bit characters.  This causes both  the  pattern
       and any data against which it is matched to be checked for UTF-8 valid-
       ity. If the data string is very long, such a  check  might  use  suffi-
       ciently  many  resources  as  to cause your application to lose perfor-
       mance.

       One  way  of  guarding  against  this  possibility  is   to   use   the
       pcre_fullinfo()  function  to  check the compiled pattern's options for
       UTF.  Alternatively, from release 8.33, you can set the  PCRE_NEVER_UTF
       option  at compile time. This causes an compile time error if a pattern
       contains a UTF-setting sequence.

       If your application is one that supports UTF, be  aware  that  validity
       checking  can  take time. If the same data string is to be matched many
       times, you can use the PCRE_NO_UTF[8|16|32]_CHECK option for the second
       and subsequent matches to save redundant checks.

       Another  way  that  performance can be hit is by running a pattern that
       has a very large search tree against a string that  will  never  match.
       Nested  unlimited  repeats in a pattern are a common example. PCRE pro-
       vides some protection against this: see the PCRE_EXTRA_MATCH_LIMIT fea-
       ture in the pcreapi page.


USER DOCUMENTATION

       The  user  documentation  for PCRE comprises a number of different sec-
       tions. In the "man" format, each of these is a separate "man page".  In
       the  HTML  format, each is a separate page, linked from the index page.
       In the plain text format, the descriptions of the pcregrep and pcretest
       programs  are  in  files  called pcregrep.txt and pcretest.txt, respec-
       tively. The remaining sections, except for the pcredemo section  (which
       is  a  program  listing),  are  concatenated  in  pcre.txt, for ease of
       searching. The sections are as follows:

         pcre              this document
         pcre-config       show PCRE installation configuration information
         pcre16            details of the 16-bit library
         pcre32            details of the 32-bit library
         pcreapi           details of PCRE's native C API
         pcrebuild         building PCRE
         pcrecallout       details of the callout feature
         pcrecompat        discussion of Perl compatibility
         pcrecpp           details of the C++ wrapper for the 8-bit library
         pcredemo          a demonstration C program that uses PCRE
         pcregrep          description of the pcregrep command (8-bit only)
         pcrejit           discussion of the just-in-time optimization support
         pcrelimits        details of size and other limits
         pcrematching      discussion of the two matching algorithms
         pcrepartial       details of the partial matching facility
         pcrepattern       syntax and semantics of supported
                             regular expressions
         pcreperform       discussion of performance issues
         pcreposix         the POSIX-compatible C API for the 8-bit library
         pcreprecompile    details of saving and re-using precompiled patterns
         pcresample        discussion of the pcredemo program
         pcrestack         discussion of stack usage
         pcresyntax        quick syntax reference
         pcretest          description of the pcretest testing command
         pcreunicode       discussion of Unicode and UTF-8/16/32 support

       In the "man" and HTML formats, there is also a short page  for  each  C
       library function, listing its arguments and results.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.

       Putting  an actual email address here seems to have been a spam magnet,
       so I've taken it away. If you want to email me, use  my  two  initials,
       followed by the two digits 10, at the domain cam.ac.uk.


REVISION

       Last updated: 10 February 2015
       Copyright (c) 1997-2015 University of Cambridge.
------------------------------------------------------------------------------


PCRE(3)                    Library Functions Manual                    PCRE(3)



NAME
       PCRE - Perl-compatible regular expressions

       #include <pcre.h>


PCRE 16-BIT API BASIC FUNCTIONS

       pcre16 *pcre16_compile(PCRE_SPTR16 pattern, int options,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       pcre16 *pcre16_compile2(PCRE_SPTR16 pattern, int options,
            int *errorcodeptr,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       pcre16_extra *pcre16_study(const pcre16 *code, int options,
            const char **errptr);

       void pcre16_free_study(pcre16_extra *extra);

       int pcre16_exec(const pcre16 *code, const pcre16_extra *extra,
            PCRE_SPTR16 subject, int length, int startoffset,
            int options, int *ovector, int ovecsize);

       int pcre16_dfa_exec(const pcre16 *code, const pcre16_extra *extra,
            PCRE_SPTR16 subject, int length, int startoffset,
            int options, int *ovector, int ovecsize,
            int *workspace, int wscount);


PCRE 16-BIT API STRING EXTRACTION FUNCTIONS

       int pcre16_copy_named_substring(const pcre16 *code,
            PCRE_SPTR16 subject, int *ovector,
            int stringcount, PCRE_SPTR16 stringname,
            PCRE_UCHAR16 *buffer, int buffersize);

       int pcre16_copy_substring(PCRE_SPTR16 subject, int *ovector,
            int stringcount, int stringnumber, PCRE_UCHAR16 *buffer,
            int buffersize);

       int pcre16_get_named_substring(const pcre16 *code,
            PCRE_SPTR16 subject, int *ovector,
            int stringcount, PCRE_SPTR16 stringname,
            PCRE_SPTR16 *stringptr);

       int pcre16_get_stringnumber(const pcre16 *code,
            PCRE_SPTR16 name);

       int pcre16_get_stringtable_entries(const pcre16 *code,
            PCRE_SPTR16 name, PCRE_UCHAR16 **first, PCRE_UCHAR16 **last);

       int pcre16_get_substring(PCRE_SPTR16 subject, int *ovector,
            int stringcount, int stringnumber,
            PCRE_SPTR16 *stringptr);

       int pcre16_get_substring_list(PCRE_SPTR16 subject,
            int *ovector, int stringcount, PCRE_SPTR16 **listptr);

       void pcre16_free_substring(PCRE_SPTR16 stringptr);

       void pcre16_free_substring_list(PCRE_SPTR16 *stringptr);


PCRE 16-BIT API AUXILIARY FUNCTIONS

       pcre16_jit_stack *pcre16_jit_stack_alloc(int startsize, int maxsize);

       void pcre16_jit_stack_free(pcre16_jit_stack *stack);

       void pcre16_assign_jit_stack(pcre16_extra *extra,
            pcre16_jit_callback callback, void *data);

       const unsigned char *pcre16_maketables(void);

       int pcre16_fullinfo(const pcre16 *code, const pcre16_extra *extra,
            int what, void *where);

       int pcre16_refcount(pcre16 *code, int adjust);

       int pcre16_config(int what, void *where);

       const char *pcre16_version(void);

       int pcre16_pattern_to_host_byte_order(pcre16 *code,
            pcre16_extra *extra, const unsigned char *tables);


PCRE 16-BIT API INDIRECTED FUNCTIONS

       void *(*pcre16_malloc)(size_t);

       void (*pcre16_free)(void *);

       void *(*pcre16_stack_malloc)(size_t);

       void (*pcre16_stack_free)(void *);

       int (*pcre16_callout)(pcre16_callout_block *);


PCRE 16-BIT API 16-BIT-ONLY FUNCTION

       int pcre16_utf16_to_host_byte_order(PCRE_UCHAR16 *output,
            PCRE_SPTR16 input, int length, int *byte_order,
            int keep_boms);


THE PCRE 16-BIT LIBRARY

       Starting  with  release  8.30, it is possible to compile a PCRE library
       that supports 16-bit character strings, including  UTF-16  strings,  as
       well  as  or instead of the original 8-bit library. The majority of the
       work to make  this  possible  was  done  by  Zoltan  Herczeg.  The  two
       libraries contain identical sets of functions, used in exactly the same
       way. Only the names of the functions and the data types of their  argu-
       ments  and results are different. To avoid over-complication and reduce
       the documentation maintenance load,  most  of  the  PCRE  documentation
       describes  the  8-bit  library,  with only occasional references to the
       16-bit library. This page describes what is different when you use  the
       16-bit library.

       WARNING:  A  single  application can be linked with both libraries, but
       you must take care when processing any particular pattern to use  func-
       tions  from  just one library. For example, if you want to study a pat-
       tern that was compiled with  pcre16_compile(),  you  must  do  so  with
       pcre16_study(), not pcre_study(), and you must free the study data with
       pcre16_free_study().


THE HEADER FILE

       There is only one header file, pcre.h. It contains prototypes  for  all
       the functions in all libraries, as well as definitions of flags, struc-
       tures, error codes, etc.


THE LIBRARY NAME

       In Unix-like systems, the 16-bit library is called libpcre16,  and  can
       normally  be  accesss  by adding -lpcre16 to the command for linking an
       application that uses PCRE.


STRING TYPES

       In the 8-bit library, strings are passed to PCRE library  functions  as
       vectors  of  bytes  with  the  C  type "char *". In the 16-bit library,
       strings are passed as vectors of unsigned 16-bit quantities. The  macro
       PCRE_UCHAR16  specifies  an  appropriate  data type, and PCRE_SPTR16 is
       defined as "const PCRE_UCHAR16 *". In very  many  environments,  "short
       int" is a 16-bit data type. When PCRE is built, it defines PCRE_UCHAR16
       as "unsigned short int", but checks that it really  is  a  16-bit  data
       type.  If  it is not, the build fails with an error message telling the
       maintainer to modify the definition appropriately.


STRUCTURE TYPES

       The types of the opaque structures that are used  for  compiled  16-bit
       patterns  and  JIT stacks are pcre16 and pcre16_jit_stack respectively.
       The  type  of  the  user-accessible  structure  that  is  returned   by
       pcre16_study()  is  pcre16_extra, and the type of the structure that is
       used for passing data to a callout  function  is  pcre16_callout_block.
       These structures contain the same fields, with the same names, as their
       8-bit counterparts. The only difference is that pointers  to  character
       strings are 16-bit instead of 8-bit types.


16-BIT FUNCTIONS

       For  every function in the 8-bit library there is a corresponding func-
       tion in the 16-bit library with a name that starts with pcre16_ instead
       of  pcre_.  The  prototypes are listed above. In addition, there is one
       extra function, pcre16_utf16_to_host_byte_order(). This  is  a  utility
       function  that converts a UTF-16 character string to host byte order if
       necessary. The other 16-bit  functions  expect  the  strings  they  are
       passed to be in host byte order.

       The input and output arguments of pcre16_utf16_to_host_byte_order() may
       point to the same address, that is, conversion in place  is  supported.
       The output buffer must be at least as long as the input.

       The  length  argument  specifies the number of 16-bit data units in the
       input string; a negative value specifies a zero-terminated string.

       If byte_order is NULL, it is assumed that the string starts off in host
       byte  order. This may be changed by byte-order marks (BOMs) anywhere in
       the string (commonly as the first character).

       If byte_order is not NULL, a non-zero value of the integer to which  it
       points  means  that  the input starts off in host byte order, otherwise
       the opposite order is assumed. Again, BOMs in  the  string  can  change
       this. The final byte order is passed back at the end of processing.

       If  keep_boms  is  not  zero,  byte-order  mark characters (0xfeff) are
       copied into the output string. Otherwise they are discarded.

       The result of the function is the number of 16-bit  units  placed  into
       the  output  buffer,  including  the  zero terminator if the string was
       zero-terminated.


SUBJECT STRING OFFSETS

       The lengths and starting offsets of subject strings must  be  specified
       in  16-bit  data units, and the offsets within subject strings that are
       returned by the matching functions are in also 16-bit units rather than
       bytes.


NAMED SUBPATTERNS

       The  name-to-number translation table that is maintained for named sub-
       patterns uses 16-bit characters.  The  pcre16_get_stringtable_entries()
       function returns the length of each entry in the table as the number of
       16-bit data units.


OPTION NAMES

       There   are   two   new   general   option   names,   PCRE_UTF16    and
       PCRE_NO_UTF16_CHECK,     which     correspond    to    PCRE_UTF8    and
       PCRE_NO_UTF8_CHECK in the 8-bit library. In  fact,  these  new  options
       define  the  same bits in the options word. There is a discussion about
       the validity of UTF-16 strings in the pcreunicode page.

       For the pcre16_config() function there is an  option  PCRE_CONFIG_UTF16
       that  returns  1  if UTF-16 support is configured, otherwise 0. If this
       option  is  given  to  pcre_config()  or  pcre32_config(),  or  if  the
       PCRE_CONFIG_UTF8  or  PCRE_CONFIG_UTF32  option is given to pcre16_con-
       fig(), the result is the PCRE_ERROR_BADOPTION error.


CHARACTER CODES

       In 16-bit mode, when  PCRE_UTF16  is  not  set,  character  values  are
       treated in the same way as in 8-bit, non UTF-8 mode, except, of course,
       that they can range from 0 to 0xffff instead of 0  to  0xff.  Character
       types  for characters less than 0xff can therefore be influenced by the
       locale in the same way as before.  Characters greater  than  0xff  have
       only one case, and no "type" (such as letter or digit).

       In  UTF-16  mode,  the  character  code  is  Unicode, in the range 0 to
       0x10ffff, with the exception of values in the range  0xd800  to  0xdfff
       because  those  are "surrogate" values that are used in pairs to encode
       values greater than 0xffff.

       A UTF-16 string can indicate its endianness by special code knows as  a
       byte-order mark (BOM). The PCRE functions do not handle this, expecting
       strings  to  be  in  host  byte  order.  A  utility   function   called
       pcre16_utf16_to_host_byte_order()  is  provided  to help with this (see
       above).


ERROR NAMES

       The errors PCRE_ERROR_BADUTF16_OFFSET and PCRE_ERROR_SHORTUTF16  corre-
       spond  to  their  8-bit  counterparts.  The error PCRE_ERROR_BADMODE is
       given when a compiled pattern is passed to a  function  that  processes
       patterns  in  the  other  mode, for example, if a pattern compiled with
       pcre_compile() is passed to pcre16_exec().

       There are new error codes whose names  begin  with  PCRE_UTF16_ERR  for
       invalid  UTF-16  strings,  corresponding to the PCRE_UTF8_ERR codes for
       UTF-8 strings that are described in the section entitled "Reason  codes
       for  invalid UTF-8 strings" in the main pcreapi page. The UTF-16 errors
       are:

         PCRE_UTF16_ERR1  Missing low surrogate at end of string
         PCRE_UTF16_ERR2  Invalid low surrogate follows high surrogate
         PCRE_UTF16_ERR3  Isolated low surrogate
         PCRE_UTF16_ERR4  Non-character


ERROR TEXTS

       If there is an error while compiling a pattern, the error text that  is
       passed  back by pcre16_compile() or pcre16_compile2() is still an 8-bit
       character string, zero-terminated.


CALLOUTS

       The subject and mark fields in the callout block that is  passed  to  a
       callout function point to 16-bit vectors.


TESTING

       The  pcretest  program continues to operate with 8-bit input and output
       files, but it can be used for testing the 16-bit library. If it is  run
       with the command line option -16, patterns and subject strings are con-
       verted from 8-bit to 16-bit before being passed to PCRE, and the 16-bit
       library  functions  are used instead of the 8-bit ones. Returned 16-bit
       strings are converted to 8-bit for output. If both the  8-bit  and  the
       32-bit libraries were not compiled, pcretest defaults to 16-bit and the
       -16 option is ignored.

       When PCRE is being built, the RunTest script that is  called  by  "make
       check"  uses  the  pcretest  -C  option to discover which of the 8-bit,
       16-bit and 32-bit libraries has been built, and runs the  tests  appro-
       priately.


NOT SUPPORTED IN 16-BIT MODE

       Not all the features of the 8-bit library are available with the 16-bit
       library. The C++ and POSIX wrapper functions  support  only  the  8-bit
       library, and the pcregrep program is at present 8-bit only.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 12 May 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCRE(3)                    Library Functions Manual                    PCRE(3)



NAME
       PCRE - Perl-compatible regular expressions

       #include <pcre.h>


PCRE 32-BIT API BASIC FUNCTIONS

       pcre32 *pcre32_compile(PCRE_SPTR32 pattern, int options,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       pcre32 *pcre32_compile2(PCRE_SPTR32 pattern, int options,
            int *errorcodeptr,
            const unsigned char *tableptr);

       pcre32_extra *pcre32_study(const pcre32 *code, int options,
            const char **errptr);

       void pcre32_free_study(pcre32_extra *extra);

       int pcre32_exec(const pcre32 *code, const pcre32_extra *extra,
            PCRE_SPTR32 subject, int length, int startoffset,
            int options, int *ovector, int ovecsize);

       int pcre32_dfa_exec(const pcre32 *code, const pcre32_extra *extra,
            PCRE_SPTR32 subject, int length, int startoffset,
            int options, int *ovector, int ovecsize,
            int *workspace, int wscount);


PCRE 32-BIT API STRING EXTRACTION FUNCTIONS

       int pcre32_copy_named_substring(const pcre32 *code,
            PCRE_SPTR32 subject, int *ovector,
            int stringcount, PCRE_SPTR32 stringname,
            PCRE_UCHAR32 *buffer, int buffersize);

       int pcre32_copy_substring(PCRE_SPTR32 subject, int *ovector,
            int stringcount, int stringnumber, PCRE_UCHAR32 *buffer,
            int buffersize);

       int pcre32_get_named_substring(const pcre32 *code,
            PCRE_SPTR32 subject, int *ovector,
            int stringcount, PCRE_SPTR32 stringname,
            PCRE_SPTR32 *stringptr);

       int pcre32_get_stringnumber(const pcre32 *code,
            PCRE_SPTR32 name);

       int pcre32_get_stringtable_entries(const pcre32 *code,
            PCRE_SPTR32 name, PCRE_UCHAR32 **first, PCRE_UCHAR32 **last);

       int pcre32_get_substring(PCRE_SPTR32 subject, int *ovector,
            int stringcount, int stringnumber,
            PCRE_SPTR32 *stringptr);

       int pcre32_get_substring_list(PCRE_SPTR32 subject,
            int *ovector, int stringcount, PCRE_SPTR32 **listptr);

       void pcre32_free_substring(PCRE_SPTR32 stringptr);

       void pcre32_free_substring_list(PCRE_SPTR32 *stringptr);


PCRE 32-BIT API AUXILIARY FUNCTIONS

       pcre32_jit_stack *pcre32_jit_stack_alloc(int startsize, int maxsize);

       void pcre32_jit_stack_free(pcre32_jit_stack *stack);

       void pcre32_assign_jit_stack(pcre32_extra *extra,
            pcre32_jit_callback callback, void *data);

       const unsigned char *pcre32_maketables(void);

       int pcre32_fullinfo(const pcre32 *code, const pcre32_extra *extra,
            int what, void *where);

       int pcre32_refcount(pcre32 *code, int adjust);

       int pcre32_config(int what, void *where);

       const char *pcre32_version(void);

       int pcre32_pattern_to_host_byte_order(pcre32 *code,
            pcre32_extra *extra, const unsigned char *tables);


PCRE 32-BIT API INDIRECTED FUNCTIONS

       void *(*pcre32_malloc)(size_t);

       void (*pcre32_free)(void *);

       void *(*pcre32_stack_malloc)(size_t);

       void (*pcre32_stack_free)(void *);

       int (*pcre32_callout)(pcre32_callout_block *);


PCRE 32-BIT API 32-BIT-ONLY FUNCTION

       int pcre32_utf32_to_host_byte_order(PCRE_UCHAR32 *output,
            PCRE_SPTR32 input, int length, int *byte_order,
            int keep_boms);


THE PCRE 32-BIT LIBRARY

       Starting  with  release  8.32, it is possible to compile a PCRE library
       that supports 32-bit character strings, including  UTF-32  strings,  as
       well as or instead of the original 8-bit library. This work was done by
       Christian Persch, based on the work done  by  Zoltan  Herczeg  for  the
       16-bit  library.  All  three  libraries contain identical sets of func-
       tions, used in exactly the same way.  Only the names of  the  functions
       and  the  data  types  of their arguments and results are different. To
       avoid over-complication and reduce the documentation maintenance  load,
       most  of  the PCRE documentation describes the 8-bit library, with only
       occasional references to the 16-bit and  32-bit  libraries.  This  page
       describes what is different when you use the 32-bit library.

       WARNING:  A  single  application  can  be linked with all or any of the
       three libraries, but you must take care when processing any  particular
       pattern  to  use  functions  from just one library. For example, if you
       want to study a pattern that was compiled  with  pcre32_compile(),  you
       must do so with pcre32_study(), not pcre_study(), and you must free the
       study data with pcre32_free_study().


THE HEADER FILE

       There is only one header file, pcre.h. It contains prototypes  for  all
       the functions in all libraries, as well as definitions of flags, struc-
       tures, error codes, etc.


THE LIBRARY NAME

       In Unix-like systems, the 32-bit library is called libpcre32,  and  can
       normally  be  accesss  by adding -lpcre32 to the command for linking an
       application that uses PCRE.


STRING TYPES

       In the 8-bit library, strings are passed to PCRE library  functions  as
       vectors  of  bytes  with  the  C  type "char *". In the 32-bit library,
       strings are passed as vectors of unsigned 32-bit quantities. The  macro
       PCRE_UCHAR32  specifies  an  appropriate  data type, and PCRE_SPTR32 is
       defined as "const PCRE_UCHAR32 *". In very many environments, "unsigned
       int" is a 32-bit data type. When PCRE is built, it defines PCRE_UCHAR32
       as "unsigned int", but checks that it really is a 32-bit data type.  If
       it is not, the build fails with an error message telling the maintainer
       to modify the definition appropriately.


STRUCTURE TYPES

       The types of the opaque structures that are used  for  compiled  32-bit
       patterns  and  JIT stacks are pcre32 and pcre32_jit_stack respectively.
       The  type  of  the  user-accessible  structure  that  is  returned   by
       pcre32_study()  is  pcre32_extra, and the type of the structure that is
       used for passing data to a callout  function  is  pcre32_callout_block.
       These structures contain the same fields, with the same names, as their
       8-bit counterparts. The only difference is that pointers  to  character
       strings are 32-bit instead of 8-bit types.


32-BIT FUNCTIONS

       For  every function in the 8-bit library there is a corresponding func-
       tion in the 32-bit library with a name that starts with pcre32_ instead
       of  pcre_.  The  prototypes are listed above. In addition, there is one
       extra function, pcre32_utf32_to_host_byte_order(). This  is  a  utility
       function  that converts a UTF-32 character string to host byte order if
       necessary. The other 32-bit  functions  expect  the  strings  they  are
       passed to be in host byte order.

       The input and output arguments of pcre32_utf32_to_host_byte_order() may
       point to the same address, that is, conversion in place  is  supported.
       The output buffer must be at least as long as the input.

       The  length  argument  specifies the number of 32-bit data units in the
       input string; a negative value specifies a zero-terminated string.

       If byte_order is NULL, it is assumed that the string starts off in host
       byte  order. This may be changed by byte-order marks (BOMs) anywhere in
       the string (commonly as the first character).

       If byte_order is not NULL, a non-zero value of the integer to which  it
       points  means  that  the input starts off in host byte order, otherwise
       the opposite order is assumed. Again, BOMs in  the  string  can  change
       this. The final byte order is passed back at the end of processing.

       If  keep_boms  is  not  zero,  byte-order  mark characters (0xfeff) are
       copied into the output string. Otherwise they are discarded.

       The result of the function is the number of 32-bit  units  placed  into
       the  output  buffer,  including  the  zero terminator if the string was
       zero-terminated.


SUBJECT STRING OFFSETS

       The lengths and starting offsets of subject strings must  be  specified
       in  32-bit  data units, and the offsets within subject strings that are
       returned by the matching functions are in also 32-bit units rather than
       bytes.


NAMED SUBPATTERNS

       The  name-to-number translation table that is maintained for named sub-
       patterns uses 32-bit characters.  The  pcre32_get_stringtable_entries()
       function returns the length of each entry in the table as the number of
       32-bit data units.


OPTION NAMES

       There   are   two   new   general   option   names,   PCRE_UTF32    and
       PCRE_NO_UTF32_CHECK,     which     correspond    to    PCRE_UTF8    and
       PCRE_NO_UTF8_CHECK in the 8-bit library. In  fact,  these  new  options
       define  the  same bits in the options word. There is a discussion about
       the validity of UTF-32 strings in the pcreunicode page.

       For the pcre32_config() function there is an  option  PCRE_CONFIG_UTF32
       that  returns  1  if UTF-32 support is configured, otherwise 0. If this
       option  is  given  to  pcre_config()  or  pcre16_config(),  or  if  the
       PCRE_CONFIG_UTF8  or  PCRE_CONFIG_UTF16  option is given to pcre32_con-
       fig(), the result is the PCRE_ERROR_BADOPTION error.


CHARACTER CODES

       In 32-bit mode, when  PCRE_UTF32  is  not  set,  character  values  are
       treated in the same way as in 8-bit, non UTF-8 mode, except, of course,
       that they can range from 0 to 0x7fffffff instead of 0 to 0xff.  Charac-
       ter  types for characters less than 0xff can therefore be influenced by
       the locale in the same way as before.   Characters  greater  than  0xff
       have only one case, and no "type" (such as letter or digit).

       In  UTF-32  mode,  the  character  code  is  Unicode, in the range 0 to
       0x10ffff, with the exception of values in the range  0xd800  to  0xdfff
       because those are "surrogate" values that are ill-formed in UTF-32.

       A  UTF-32 string can indicate its endianness by special code knows as a
       byte-order mark (BOM). The PCRE functions do not handle this, expecting
       strings   to   be  in  host  byte  order.  A  utility  function  called
       pcre32_utf32_to_host_byte_order() is provided to help  with  this  (see
       above).


ERROR NAMES

       The  error  PCRE_ERROR_BADUTF32  corresponds  to its 8-bit counterpart.
       The error PCRE_ERROR_BADMODE is given when a compiled pattern is passed
       to  a  function that processes patterns in the other mode, for example,
       if a pattern compiled with pcre_compile() is passed to pcre32_exec().

       There are new error codes whose names  begin  with  PCRE_UTF32_ERR  for
       invalid  UTF-32  strings,  corresponding to the PCRE_UTF8_ERR codes for
       UTF-8 strings that are described in the section entitled "Reason  codes
       for  invalid UTF-8 strings" in the main pcreapi page. The UTF-32 errors
       are:

         PCRE_UTF32_ERR1  Surrogate character (range from 0xd800 to 0xdfff)
         PCRE_UTF32_ERR2  Non-character
         PCRE_UTF32_ERR3  Character > 0x10ffff


ERROR TEXTS

       If there is an error while compiling a pattern, the error text that  is
       passed  back by pcre32_compile() or pcre32_compile2() is still an 8-bit
       character string, zero-terminated.


CALLOUTS

       The subject and mark fields in the callout block that is  passed  to  a
       callout function point to 32-bit vectors.


TESTING

       The  pcretest  program continues to operate with 8-bit input and output
       files, but it can be used for testing the 32-bit library. If it is  run
       with the command line option -32, patterns and subject strings are con-
       verted from 8-bit to 32-bit before being passed to PCRE, and the 32-bit
       library  functions  are used instead of the 8-bit ones. Returned 32-bit
       strings are converted to 8-bit for output. If both the  8-bit  and  the
       16-bit libraries were not compiled, pcretest defaults to 32-bit and the
       -32 option is ignored.

       When PCRE is being built, the RunTest script that is  called  by  "make
       check"  uses  the  pcretest  -C  option to discover which of the 8-bit,
       16-bit and 32-bit libraries has been built, and runs the  tests  appro-
       priately.


NOT SUPPORTED IN 32-BIT MODE

       Not all the features of the 8-bit library are available with the 32-bit
       library. The C++ and POSIX wrapper functions  support  only  the  8-bit
       library, and the pcregrep program is at present 8-bit only.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 12 May 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREBUILD(3)               Library Functions Manual               PCREBUILD(3)



NAME
       PCRE - Perl-compatible regular expressions

BUILDING PCRE

       PCRE  is  distributed with a configure script that can be used to build
       the library in Unix-like environments using the applications  known  as
       Autotools.   Also  in  the  distribution  are files to support building
       using CMake instead of configure. The text file README contains general
       information  about  building  with Autotools (some of which is repeated
       below), and also has some comments about building on various  operating
       systems.  There  is  a lot more information about building PCRE without
       using Autotools (including information about using CMake  and  building
       "by  hand")  in  the  text file called NON-AUTOTOOLS-BUILD.  You should
       consult this file as well as the README file if you are building  in  a
       non-Unix-like environment.


PCRE BUILD-TIME OPTIONS

       The  rest of this document describes the optional features of PCRE that
       can be selected when the library is compiled. It  assumes  use  of  the
       configure  script,  where  the  optional features are selected or dese-
       lected by providing options to configure before running the  make  com-
       mand.  However,  the same options can be selected in both Unix-like and
       non-Unix-like environments using the GUI facility of cmake-gui  if  you
       are using CMake instead of configure to build PCRE.

       If  you  are not using Autotools or CMake, option selection can be done
       by editing the config.h file, or by passing parameter settings  to  the
       compiler, as described in NON-AUTOTOOLS-BUILD.

       The complete list of options for configure (which includes the standard
       ones such as the  selection  of  the  installation  directory)  can  be
       obtained by running

         ./configure --help

       The  following  sections  include  descriptions  of options whose names
       begin with --enable or --disable. These settings specify changes to the
       defaults  for  the configure command. Because of the way that configure
       works, --enable and --disable always come in pairs, so  the  complemen-
       tary  option always exists as well, but as it specifies the default, it
       is not described.


BUILDING 8-BIT, 16-BIT AND 32-BIT LIBRARIES

       By default, a library called libpcre  is  built,  containing  functions
       that  take  string  arguments  contained in vectors of bytes, either as
       single-byte characters, or interpreted as UTF-8 strings. You  can  also
       build  a  separate library, called libpcre16, in which strings are con-
       tained in vectors of 16-bit data units and interpreted either  as  sin-
       gle-unit characters or UTF-16 strings, by adding

         --enable-pcre16

       to  the  configure  command.  You  can  also build yet another separate
       library, called libpcre32, in which strings are contained in vectors of
       32-bit  data  units and interpreted either as single-unit characters or
       UTF-32 strings, by adding

         --enable-pcre32

       to the configure command. If you do not want the 8-bit library, add

         --disable-pcre8

       as well. At least one of the three libraries must be built.  Note  that
       the  C++  and  POSIX  wrappers are for the 8-bit library only, and that
       pcregrep is an 8-bit program. None of these are  built  if  you  select
       only the 16-bit or 32-bit libraries.


BUILDING SHARED AND STATIC LIBRARIES

       The  Autotools  PCRE building process uses libtool to build both shared
       and static libraries by default. You  can  suppress  one  of  these  by
       adding one of

         --disable-shared
         --disable-static

       to the configure command, as required.


C++ SUPPORT

       By  default,  if the 8-bit library is being built, the configure script
       will search for a C++ compiler and C++ header files. If it finds  them,
       it  automatically  builds  the C++ wrapper library (which supports only
       8-bit strings). You can disable this by adding

         --disable-cpp

       to the configure command.


UTF-8, UTF-16 AND UTF-32 SUPPORT

       To build PCRE with support for UTF Unicode character strings, add

         --enable-utf

       to the configure command. This setting applies to all three  libraries,
       adding  support  for  UTF-8 to the 8-bit library, support for UTF-16 to
       the 16-bit library, and  support  for  UTF-32  to  the  to  the  32-bit
       library.  There  are no separate options for enabling UTF-8, UTF-16 and
       UTF-32 independently because that would allow ridiculous settings  such
       as  requesting UTF-16 support while building only the 8-bit library. It
       is not possible to build one library with UTF support and another with-
       out  in the same configuration. (For backwards compatibility, --enable-
       utf8 is a synonym of --enable-utf.)

       Of itself, this setting does not make  PCRE  treat  strings  as  UTF-8,
       UTF-16  or UTF-32. As well as compiling PCRE with this option, you also
       have have to set the PCRE_UTF8, PCRE_UTF16  or  PCRE_UTF32  option  (as
       appropriate) when you call one of the pattern compiling functions.

       If  you  set --enable-utf when compiling in an EBCDIC environment, PCRE
       expects its input to be either ASCII or UTF-8 (depending  on  the  run-
       time option). It is not possible to support both EBCDIC and UTF-8 codes
       in the same version of  the  library.  Consequently,  --enable-utf  and
       --enable-ebcdic are mutually exclusive.


UNICODE CHARACTER PROPERTY SUPPORT

       UTF  support allows the libraries to process character codepoints up to
       0x10ffff in the strings that they handle. On its own, however, it  does
       not provide any facilities for accessing the properties of such charac-
       ters. If you want to be able to use the pattern escapes \P, \p, and \X,
       which refer to Unicode character properties, you must add

         --enable-unicode-properties

       to  the  configure  command. This implies UTF support, even if you have
       not explicitly requested it.

       Including Unicode property support adds around 30K  of  tables  to  the
       PCRE  library.  Only  the general category properties such as Lu and Nd
       are supported. Details are given in the pcrepattern documentation.


JUST-IN-TIME COMPILER SUPPORT

       Just-in-time compiler support is included in the build by specifying

         --enable-jit

       This support is available only for certain hardware  architectures.  If
       this  option  is  set  for  an unsupported architecture, a compile time
       error occurs.  See the pcrejit documentation for a  discussion  of  JIT
       usage. When JIT support is enabled, pcregrep automatically makes use of
       it, unless you add

         --disable-pcregrep-jit

       to the "configure" command.


CODE VALUE OF NEWLINE

       By default, PCRE interprets the linefeed (LF) character  as  indicating
       the  end  of  a line. This is the normal newline character on Unix-like
       systems. You can compile PCRE to use carriage return (CR)  instead,  by
       adding

         --enable-newline-is-cr

       to  the  configure  command.  There  is  also  a --enable-newline-is-lf
       option, which explicitly specifies linefeed as the newline character.

       Alternatively, you can specify that line endings are to be indicated by
       the two character sequence CRLF. If you want this, add

         --enable-newline-is-crlf

       to the configure command. There is a fourth option, specified by

         --enable-newline-is-anycrlf

       which  causes  PCRE  to recognize any of the three sequences CR, LF, or
       CRLF as indicating a line ending. Finally, a fifth option, specified by

         --enable-newline-is-any

       causes PCRE to recognize any Unicode newline sequence.

       Whatever line ending convention is selected when PCRE is built  can  be
       overridden  when  the library functions are called. At build time it is
       conventional to use the standard for your operating system.


WHAT \R MATCHES

       By default, the sequence \R in a pattern matches  any  Unicode  newline
       sequence,  whatever  has  been selected as the line ending sequence. If
       you specify

         --enable-bsr-anycrlf

       the default is changed so that \R matches only CR, LF, or  CRLF.  What-
       ever  is selected when PCRE is built can be overridden when the library
       functions are called.


POSIX MALLOC USAGE

       When the 8-bit library is called through the POSIX interface  (see  the
       pcreposix  documentation),  additional  working storage is required for
       holding the pointers to capturing  substrings,  because  PCRE  requires
       three integers per substring, whereas the POSIX interface provides only
       two. If the number of expected substrings is small, the  wrapper  func-
       tion  uses  space  on the stack, because this is faster than using mal-
       loc() for each call. The default threshold above which the stack is  no
       longer used is 10; it can be changed by adding a setting such as

         --with-posix-malloc-threshold=20

       to the configure command.


HANDLING VERY LARGE PATTERNS

       Within  a  compiled  pattern,  offset values are used to point from one
       part to another (for example, from an opening parenthesis to an  alter-
       nation  metacharacter).  By default, in the 8-bit and 16-bit libraries,
       two-byte values are used for these offsets, leading to a  maximum  size
       for  a compiled pattern of around 64K. This is sufficient to handle all
       but the most gigantic patterns.  Nevertheless, some people do  want  to
       process  truly  enormous patterns, so it is possible to compile PCRE to
       use three-byte or four-byte offsets by adding a setting such as

         --with-link-size=3

       to the configure command. The value given must be 2, 3, or 4.  For  the
       16-bit  library,  a  value of 3 is rounded up to 4. In these libraries,
       using longer offsets slows down the operation of PCRE because it has to
       load  additional  data  when  handling them. For the 32-bit library the
       value is always 4 and cannot be overridden; the value  of  --with-link-
       size is ignored.


AVOIDING EXCESSIVE STACK USAGE

       When matching with the pcre_exec() function, PCRE implements backtrack-
       ing by making recursive calls to an internal function  called  match().
       In  environments  where  the size of the stack is limited, this can se-
       verely limit PCRE's operation. (The Unix environment does  not  usually
       suffer from this problem, but it may sometimes be necessary to increase
       the maximum stack size.  There is a discussion in the  pcrestack  docu-
       mentation.)  An alternative approach to recursion that uses memory from
       the heap to remember data, instead of using recursive  function  calls,
       has  been  implemented to work round the problem of limited stack size.
       If you want to build a version of PCRE that works this way, add

         --disable-stack-for-recursion

       to the configure command. With this configuration, PCRE  will  use  the
       pcre_stack_malloc  and pcre_stack_free variables to call memory manage-
       ment functions. By default these point to malloc() and free(), but  you
       can replace the pointers so that your own functions are used instead.

       Separate  functions  are  provided  rather  than  using pcre_malloc and
       pcre_free because the  usage  is  very  predictable:  the  block  sizes
       requested  are  always  the  same,  and  the blocks are always freed in
       reverse order. A calling program might be able to  implement  optimized
       functions  that  perform  better  than  malloc()  and free(). PCRE runs
       noticeably more slowly when built in this way. This option affects only
       the pcre_exec() function; it is not relevant for pcre_dfa_exec().


LIMITING PCRE RESOURCE USAGE

       Internally,  PCRE has a function called match(), which it calls repeat-
       edly  (sometimes  recursively)  when  matching  a  pattern   with   the
       pcre_exec()  function.  By controlling the maximum number of times this
       function may be called during a single matching operation, a limit  can
       be  placed  on  the resources used by a single call to pcre_exec(). The
       limit can be changed at run time, as described in the pcreapi  documen-
       tation.  The default is 10 million, but this can be changed by adding a
       setting such as

         --with-match-limit=500000

       to  the  configure  command.  This  setting  has  no  effect   on   the
       pcre_dfa_exec() matching function.

       In  some  environments  it is desirable to limit the depth of recursive
       calls of match() more strictly than the total number of calls, in order
       to  restrict  the maximum amount of stack (or heap, if --disable-stack-
       for-recursion is specified) that is used. A second limit controls this;
       it  defaults  to  the  value  that is set for --with-match-limit, which
       imposes no additional constraints. However, you can set a  lower  limit
       by adding, for example,

         --with-match-limit-recursion=10000

       to  the  configure  command.  This  value can also be overridden at run
       time.


CREATING CHARACTER TABLES AT BUILD TIME

       PCRE uses fixed tables for processing characters whose code values  are
       less  than 256. By default, PCRE is built with a set of tables that are
       distributed in the file pcre_chartables.c.dist. These  tables  are  for
       ASCII codes only. If you add

         --enable-rebuild-chartables

       to  the  configure  command, the distributed tables are no longer used.
       Instead, a program called dftables is compiled and  run.  This  outputs
       the source for new set of tables, created in the default locale of your
       C run-time system. (This method of replacing the tables does  not  work
       if  you are cross compiling, because dftables is run on the local host.
       If you need to create alternative tables when cross compiling, you will
       have to do so "by hand".)


USING EBCDIC CODE

       PCRE  assumes  by  default that it will run in an environment where the
       character code is ASCII (or Unicode, which is  a  superset  of  ASCII).
       This  is  the  case for most computer operating systems. PCRE can, how-
       ever, be compiled to run in an EBCDIC environment by adding

         --enable-ebcdic

       to the configure command. This setting implies --enable-rebuild-charta-
       bles.  You  should  only  use  it if you know that you are in an EBCDIC
       environment (for example,  an  IBM  mainframe  operating  system).  The
       --enable-ebcdic option is incompatible with --enable-utf.

       The EBCDIC character that corresponds to an ASCII LF is assumed to have
       the value 0x15 by default. However, in some EBCDIC  environments,  0x25
       is used. In such an environment you should use

         --enable-ebcdic-nl25

       as well as, or instead of, --enable-ebcdic. The EBCDIC character for CR
       has the same value as in ASCII, namely, 0x0d.  Whichever  of  0x15  and
       0x25 is not chosen as LF is made to correspond to the Unicode NEL char-
       acter (which, in Unicode, is 0x85).

       The options that select newline behaviour, such as --enable-newline-is-
       cr, and equivalent run-time options, refer to these character values in
       an EBCDIC environment.


PCREGREP OPTIONS FOR COMPRESSED FILE SUPPORT

       By default, pcregrep reads all files as plain text. You can build it so
       that it recognizes files whose names end in .gz or .bz2, and reads them
       with libz or libbz2, respectively, by adding one or both of

         --enable-pcregrep-libz
         --enable-pcregrep-libbz2

       to the configure command. These options naturally require that the rel-
       evant  libraries  are installed on your system. Configuration will fail
       if they are not.


PCREGREP BUFFER SIZE

       pcregrep uses an internal buffer to hold a "window" on the file  it  is
       scanning, in order to be able to output "before" and "after" lines when
       it finds a match. The size of the buffer is controlled by  a  parameter
       whose default value is 20K. The buffer itself is three times this size,
       but because of the way it is used for holding "before" lines, the long-
       est  line  that  is guaranteed to be processable is the parameter size.
       You can change the default parameter value by adding, for example,

         --with-pcregrep-bufsize=50K

       to the configure command. The caller of pcregrep can, however, override
       this value by specifying a run-time option.


PCRETEST OPTION FOR LIBREADLINE SUPPORT

       If you add

         --enable-pcretest-libreadline

       to  the  configure  command,  pcretest  is  linked with the libreadline
       library, and when its input is from a terminal, it reads it  using  the
       readline() function. This provides line-editing and history facilities.
       Note that libreadline is GPL-licensed, so if you distribute a binary of
       pcretest linked in this way, there may be licensing issues.

       Setting  this  option  causes  the -lreadline option to be added to the
       pcretest build. In many operating environments with  a  sytem-installed
       libreadline this is sufficient. However, in some environments (e.g.  if
       an unmodified distribution version of readline is in use),  some  extra
       configuration  may  be necessary. The INSTALL file for libreadline says
       this:

         "Readline uses the termcap functions, but does not link with the
         termcap or curses library itself, allowing applications which link
         with readline the to choose an appropriate library."

       If your environment has not been set up so that an appropriate  library
       is automatically included, you may need to add something like

         LIBS="-ncurses"

       immediately before the configure command.


DEBUGGING WITH VALGRIND SUPPORT

       By adding the

         --enable-valgrind

       option  to to the configure command, PCRE will use valgrind annotations
       to mark certain memory regions as  unaddressable.  This  allows  it  to
       detect invalid memory accesses, and is mostly useful for debugging PCRE
       itself.


CODE COVERAGE REPORTING

       If your C compiler is gcc, you can build a version  of  PCRE  that  can
       generate a code coverage report for its test suite. To enable this, you
       must install lcov version 1.6 or above. Then specify

         --enable-coverage

       to the configure command and build PCRE in the usual way.

       Note that using ccache (a caching C compiler) is incompatible with code
       coverage  reporting. If you have configured ccache to run automatically
       on your system, you must set the environment variable

         CCACHE_DISABLE=1

       before running make to build PCRE, so that ccache is not used.

       When --enable-coverage is used,  the  following  addition  targets  are
       added to the Makefile:

         make coverage

       This  creates  a  fresh  coverage report for the PCRE test suite. It is
       equivalent to running "make coverage-reset", "make  coverage-baseline",
       "make check", and then "make coverage-report".

         make coverage-reset

       This zeroes the coverage counters, but does nothing else.

         make coverage-baseline

       This captures baseline coverage information.

         make coverage-report

       This creates the coverage report.

         make coverage-clean-report

       This  removes the generated coverage report without cleaning the cover-
       age data itself.

         make coverage-clean-data

       This removes the captured coverage data without removing  the  coverage
       files created at compile time (*.gcno).

         make coverage-clean

       This  cleans all coverage data including the generated coverage report.
       For more information about code coverage, see the gcov and  lcov  docu-
       mentation.


SEE ALSO

       pcreapi(3), pcre16, pcre32, pcre_config(3).


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 12 May 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREMATCHING(3)            Library Functions Manual            PCREMATCHING(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE MATCHING ALGORITHMS

       This document describes the two different algorithms that are available
       in PCRE for matching a compiled regular expression against a given sub-
       ject  string.  The  "standard"  algorithm  is  the  one provided by the
       pcre_exec(), pcre16_exec() and pcre32_exec() functions. These  work  in
       the  same as as Perl's matching function, and provide a Perl-compatible
       matching  operation.   The  just-in-time  (JIT)  optimization  that  is
       described  in  the pcrejit documentation is compatible with these func-
       tions.

       An  alternative  algorithm  is   provided   by   the   pcre_dfa_exec(),
       pcre16_dfa_exec()  and  pcre32_dfa_exec()  functions; they operate in a
       different way, and are not Perl-compatible. This alternative has advan-
       tages and disadvantages compared with the standard algorithm, and these
       are described below.

       When there is only one possible way in which a given subject string can
       match  a pattern, the two algorithms give the same answer. A difference
       arises, however, when there are multiple possibilities. For example, if
       the pattern

         ^<.*>

       is matched against the string

         <something> <something else> <something further>

       there are three possible answers. The standard algorithm finds only one
       of them, whereas the alternative algorithm finds all three.


REGULAR EXPRESSIONS AS TREES

       The set of strings that are matched by a regular expression can be rep-
       resented  as  a  tree structure. An unlimited repetition in the pattern
       makes the tree of infinite size, but it is still a tree.  Matching  the
       pattern  to a given subject string (from a given starting point) can be
       thought of as a search of the tree.  There are two  ways  to  search  a
       tree:  depth-first  and  breadth-first, and these correspond to the two
       matching algorithms provided by PCRE.


THE STANDARD MATCHING ALGORITHM

       In the terminology of Jeffrey Friedl's book "Mastering Regular  Expres-
       sions",  the  standard  algorithm  is an "NFA algorithm". It conducts a
       depth-first search of the pattern tree. That is, it  proceeds  along  a
       single path through the tree, checking that the subject matches what is
       required. When there is a mismatch, the algorithm  tries  any  alterna-
       tives  at  the  current point, and if they all fail, it backs up to the
       previous branch point in the  tree,  and  tries  the  next  alternative
       branch  at  that  level.  This often involves backing up (moving to the
       left) in the subject string as well.  The  order  in  which  repetition
       branches  are  tried  is controlled by the greedy or ungreedy nature of
       the quantifier.

       If a leaf node is reached, a matching string has  been  found,  and  at
       that  point the algorithm stops. Thus, if there is more than one possi-
       ble match, this algorithm returns the first one that it finds.  Whether
       this  is the shortest, the longest, or some intermediate length depends
       on the way the greedy and ungreedy repetition quantifiers are specified
       in the pattern.

       Because  it  ends  up  with a single path through the tree, it is rela-
       tively straightforward for this algorithm to keep  track  of  the  sub-
       strings  that  are  matched  by portions of the pattern in parentheses.
       This provides support for capturing parentheses and back references.


THE ALTERNATIVE MATCHING ALGORITHM

       This algorithm conducts a breadth-first search of  the  tree.  Starting
       from  the  first  matching  point  in the subject, it scans the subject
       string from left to right, once, character by character, and as it does
       this,  it remembers all the paths through the tree that represent valid
       matches. In Friedl's terminology, this is a kind  of  "DFA  algorithm",
       though  it is not implemented as a traditional finite state machine (it
       keeps multiple states active simultaneously).

       Although the general principle of this matching algorithm  is  that  it
       scans  the subject string only once, without backtracking, there is one
       exception: when a lookaround assertion is encountered,  the  characters
       following  or  preceding  the  current  point  have to be independently
       inspected.

       The scan continues until either the end of the subject is  reached,  or
       there  are  no more unterminated paths. At this point, terminated paths
       represent the different matching possibilities (if there are none,  the
       match  has  failed).   Thus,  if there is more than one possible match,
       this algorithm finds all of them, and in particular, it finds the long-
       est.  The  matches are returned in decreasing order of length. There is
       an option to stop the algorithm after the first match (which is  neces-
       sarily the shortest) is found.

       Note that all the matches that are found start at the same point in the
       subject. If the pattern

         cat(er(pillar)?)?

       is matched against the string "the caterpillar catchment",  the  result
       will  be the three strings "caterpillar", "cater", and "cat" that start
       at the fifth character of the subject. The algorithm does not automati-
       cally move on to find matches that start at later positions.

       PCRE's  "auto-possessification" optimization usually applies to charac-
       ter repeats at the end of a pattern (as well as internally). For  exam-
       ple, the pattern "a\d+" is compiled as if it were "a\d++" because there
       is no point even considering the possibility of backtracking  into  the
       repeated  digits.  For  DFA matching, this means that only one possible
       match is found. If you really do want multiple matches in  such  cases,
       either use an ungreedy repeat ("a\d+?") or set the PCRE_NO_AUTO_POSSESS
       option when compiling.

       There are a number of features of PCRE regular expressions that are not
       supported by the alternative matching algorithm. They are as follows:

       1.  Because  the  algorithm  finds  all possible matches, the greedy or
       ungreedy nature of repetition quantifiers is not relevant.  Greedy  and
       ungreedy quantifiers are treated in exactly the same way. However, pos-
       sessive quantifiers can make a difference when what follows could  also
       match what is quantified, for example in a pattern like this:

         ^a++\w!

       This  pattern matches "aaab!" but not "aaa!", which would be matched by
       a non-possessive quantifier. Similarly, if an atomic group is  present,
       it  is matched as if it were a standalone pattern at the current point,
       and the longest match is then "locked in" for the rest of  the  overall
       pattern.

       2. When dealing with multiple paths through the tree simultaneously, it
       is not straightforward to keep track of  captured  substrings  for  the
       different  matching  possibilities,  and  PCRE's implementation of this
       algorithm does not attempt to do this. This means that no captured sub-
       strings are available.

       3.  Because no substrings are captured, back references within the pat-
       tern are not supported, and cause errors if encountered.

       4. For the same reason, conditional expressions that use  a  backrefer-
       ence  as  the  condition or test for a specific group recursion are not
       supported.

       5. Because many paths through the tree may be  active,  the  \K  escape
       sequence, which resets the start of the match when encountered (but may
       be on some paths and not on others), is not  supported.  It  causes  an
       error if encountered.

       6.  Callouts  are  supported, but the value of the capture_top field is
       always 1, and the value of the capture_last field is always -1.

       7. The \C escape sequence, which (in  the  standard  algorithm)  always
       matches  a  single data unit, even in UTF-8, UTF-16 or UTF-32 modes, is
       not supported in these modes, because the alternative  algorithm  moves
       through the subject string one character (not data unit) at a time, for
       all active paths through the tree.

       8. Except for (*FAIL), the backtracking control verbs such as  (*PRUNE)
       are  not  supported.  (*FAIL)  is supported, and behaves like a failing
       negative assertion.


ADVANTAGES OF THE ALTERNATIVE ALGORITHM

       Using the alternative matching algorithm provides the following  advan-
       tages:

       1. All possible matches (at a single point in the subject) are automat-
       ically found, and in particular, the longest match is  found.  To  find
       more than one match using the standard algorithm, you have to do kludgy
       things with callouts.

       2. Because the alternative algorithm  scans  the  subject  string  just
       once, and never needs to backtrack (except for lookbehinds), it is pos-
       sible to pass very long subject strings to  the  matching  function  in
       several pieces, checking for partial matching each time. Although it is
       possible to do multi-segment matching using the standard  algorithm  by
       retaining  partially  matched  substrings,  it is more complicated. The
       pcrepartial documentation gives details of partial  matching  and  dis-
       cusses multi-segment matching.


DISADVANTAGES OF THE ALTERNATIVE ALGORITHM

       The alternative algorithm suffers from a number of disadvantages:

       1.  It  is  substantially  slower  than the standard algorithm. This is
       partly because it has to search for all possible matches, but  is  also
       because it is less susceptible to optimization.

       2. Capturing parentheses and back references are not supported.

       3. Although atomic groups are supported, their use does not provide the
       performance advantage that it does for the standard algorithm.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 12 November 2013
       Copyright (c) 1997-2012 University of Cambridge.
------------------------------------------------------------------------------


PCREAPI(3)                 Library Functions Manual                 PCREAPI(3)



NAME
       PCRE - Perl-compatible regular expressions

       #include <pcre.h>


PCRE NATIVE API BASIC FUNCTIONS

       pcre *pcre_compile(const char *pattern, int options,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       pcre *pcre_compile2(const char *pattern, int options,
            int *errorcodeptr,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       pcre_extra *pcre_study(const pcre *code, int options,
            const char **errptr);

       void pcre_free_study(pcre_extra *extra);

       int pcre_exec(const pcre *code, const pcre_extra *extra,
            const char *subject, int length, int startoffset,
            int options, int *ovector, int ovecsize);

       int pcre_dfa_exec(const pcre *code, const pcre_extra *extra,
            const char *subject, int length, int startoffset,
            int options, int *ovector, int ovecsize,
            int *workspace, int wscount);


PCRE NATIVE API STRING EXTRACTION FUNCTIONS

       int pcre_copy_named_substring(const pcre *code,
            const char *subject, int *ovector,
            int stringcount, const char *stringname,
            char *buffer, int buffersize);

       int pcre_copy_substring(const char *subject, int *ovector,
            int stringcount, int stringnumber, char *buffer,
            int buffersize);

       int pcre_get_named_substring(const pcre *code,
            const char *subject, int *ovector,
            int stringcount, const char *stringname,
            const char **stringptr);

       int pcre_get_stringnumber(const pcre *code,
            const char *name);

       int pcre_get_stringtable_entries(const pcre *code,
            const char *name, char **first, char **last);

       int pcre_get_substring(const char *subject, int *ovector,
            int stringcount, int stringnumber,
            const char **stringptr);

       int pcre_get_substring_list(const char *subject,
            int *ovector, int stringcount, const char ***listptr);

       void pcre_free_substring(const char *stringptr);

       void pcre_free_substring_list(const char **stringptr);


PCRE NATIVE API AUXILIARY FUNCTIONS

       int pcre_jit_exec(const pcre *code, const pcre_extra *extra,
            const char *subject, int length, int startoffset,
            int options, int *ovector, int ovecsize,
            pcre_jit_stack *jstack);

       pcre_jit_stack *pcre_jit_stack_alloc(int startsize, int maxsize);

       void pcre_jit_stack_free(pcre_jit_stack *stack);

       void pcre_assign_jit_stack(pcre_extra *extra,
            pcre_jit_callback callback, void *data);

       const unsigned char *pcre_maketables(void);

       int pcre_fullinfo(const pcre *code, const pcre_extra *extra,
            int what, void *where);

       int pcre_refcount(pcre *code, int adjust);

       int pcre_config(int what, void *where);

       const char *pcre_version(void);

       int pcre_pattern_to_host_byte_order(pcre *code,
            pcre_extra *extra, const unsigned char *tables);


PCRE NATIVE API INDIRECTED FUNCTIONS

       void *(*pcre_malloc)(size_t);

       void (*pcre_free)(void *);

       void *(*pcre_stack_malloc)(size_t);

       void (*pcre_stack_free)(void *);

       int (*pcre_callout)(pcre_callout_block *);

       int (*pcre_stack_guard)(void);


PCRE 8-BIT, 16-BIT, AND 32-BIT LIBRARIES

       As  well  as  support  for  8-bit character strings, PCRE also supports
       16-bit strings (from release 8.30) and  32-bit  strings  (from  release
       8.32),  by means of two additional libraries. They can be built as well
       as, or instead of, the 8-bit library. To avoid too  much  complication,
       this  document describes the 8-bit versions of the functions, with only
       occasional references to the 16-bit and 32-bit libraries.

       The 16-bit and 32-bit functions operate in the same way as their  8-bit
       counterparts;  they  just  use different data types for their arguments
       and results, and their names start with pcre16_ or pcre32_  instead  of
       pcre_.  For  every  option  that  has  UTF8  in  its name (for example,
       PCRE_UTF8), there are corresponding 16-bit and 32-bit names  with  UTF8
       replaced by UTF16 or UTF32, respectively. This facility is in fact just
       cosmetic; the 16-bit and 32-bit option names define the same  bit  val-
       ues.

       References to bytes and UTF-8 in this document should be read as refer-
       ences to 16-bit data units and UTF-16 when using the 16-bit library, or
       32-bit  data  units  and  UTF-32  when using the 32-bit library, unless
       specified otherwise.  More details of the specific differences for  the
       16-bit and 32-bit libraries are given in the pcre16 and pcre32 pages.


PCRE API OVERVIEW

       PCRE has its own native API, which is described in this document. There
       are also some wrapper functions (for the 8-bit library only) that  cor-
       respond  to  the  POSIX  regular  expression  API, but they do not give
       access to all the functionality. They are described  in  the  pcreposix
       documentation.  Both  of these APIs define a set of C function calls. A
       C++ wrapper (again for the 8-bit library only) is also distributed with
       PCRE. It is documented in the pcrecpp page.

       The  native  API  C  function prototypes are defined in the header file
       pcre.h, and on Unix-like systems the (8-bit) library itself  is  called
       libpcre.  It  can  normally be accessed by adding -lpcre to the command
       for linking an application that uses PCRE. The header file defines  the
       macros PCRE_MAJOR and PCRE_MINOR to contain the major and minor release
       numbers for the library. Applications can use these to include  support
       for different releases of PCRE.

       In a Windows environment, if you want to statically link an application
       program against a non-dll pcre.a  file,  you  must  define  PCRE_STATIC
       before  including  pcre.h or pcrecpp.h, because otherwise the pcre_mal-
       loc()   and   pcre_free()   exported   functions   will   be   declared
       __declspec(dllimport), with unwanted results.

       The   functions   pcre_compile(),  pcre_compile2(),  pcre_study(),  and
       pcre_exec() are used for compiling and matching regular expressions  in
       a  Perl-compatible  manner. A sample program that demonstrates the sim-
       plest way of using them is provided in the file  called  pcredemo.c  in
       the PCRE source distribution. A listing of this program is given in the
       pcredemo documentation, and the pcresample documentation describes  how
       to compile and run it.

       Just-in-time  compiler  support is an optional feature of PCRE that can
       be built in appropriate hardware environments. It greatly speeds up the
       matching  performance  of  many  patterns.  Simple  programs can easily
       request that it be used if available, by  setting  an  option  that  is
       ignored  when  it is not relevant. More complicated programs might need
       to    make    use    of    the    functions     pcre_jit_stack_alloc(),
       pcre_jit_stack_free(),  and pcre_assign_jit_stack() in order to control
       the JIT code's memory usage.

       From release 8.32 there is also a direct interface for  JIT  execution,
       which  gives  improved performance. The JIT-specific functions are dis-
       cussed in the pcrejit documentation.

       A second matching function, pcre_dfa_exec(), which is not Perl-compati-
       ble,  is  also provided. This uses a different algorithm for the match-
       ing. The alternative algorithm finds all possible matches (at  a  given
       point  in  the  subject), and scans the subject just once (unless there
       are lookbehind assertions). However, this  algorithm  does  not  return
       captured  substrings.  A description of the two matching algorithms and
       their advantages and disadvantages is given in the  pcrematching  docu-
       mentation.

       In  addition  to  the  main compiling and matching functions, there are
       convenience functions for extracting captured substrings from a subject
       string that is matched by pcre_exec(). They are:

         pcre_copy_substring()
         pcre_copy_named_substring()
         pcre_get_substring()
         pcre_get_named_substring()
         pcre_get_substring_list()
         pcre_get_stringnumber()
         pcre_get_stringtable_entries()

       pcre_free_substring() and pcre_free_substring_list() are also provided,
       to free the memory used for extracted strings.

       The function pcre_maketables() is used to  build  a  set  of  character
       tables   in   the   current   locale  for  passing  to  pcre_compile(),
       pcre_exec(), or pcre_dfa_exec(). This is an optional facility  that  is
       provided  for  specialist  use.  Most  commonly,  no special tables are
       passed, in which case internal tables that are generated when  PCRE  is
       built are used.

       The  function  pcre_fullinfo()  is used to find out information about a
       compiled pattern. The function pcre_version() returns a  pointer  to  a
       string containing the version of PCRE and its date of release.

       The  function  pcre_refcount()  maintains  a  reference count in a data
       block containing a compiled pattern. This is provided for  the  benefit
       of object-oriented applications.

       The  global  variables  pcre_malloc and pcre_free initially contain the
       entry points of the standard malloc()  and  free()  functions,  respec-
       tively. PCRE calls the memory management functions via these variables,
       so a calling program can replace them if it  wishes  to  intercept  the
       calls. This should be done before calling any PCRE functions.

       The  global  variables  pcre_stack_malloc  and pcre_stack_free are also
       indirections to memory management functions.  These  special  functions
       are  used  only  when  PCRE is compiled to use the heap for remembering
       data, instead of recursive function calls, when running the pcre_exec()
       function.  See  the  pcrebuild  documentation  for details of how to do
       this. It is a non-standard way of building PCRE, for  use  in  environ-
       ments  that  have  limited stacks. Because of the greater use of memory
       management, it runs more slowly. Separate  functions  are  provided  so
       that  special-purpose  external  code  can  be used for this case. When
       used, these functions are always called in a  stack-like  manner  (last
       obtained,  first freed), and always for memory blocks of the same size.
       There is a discussion about PCRE's stack usage in the  pcrestack  docu-
       mentation.

       The global variable pcre_callout initially contains NULL. It can be set
       by the caller to a "callout" function, which PCRE  will  then  call  at
       specified  points during a matching operation. Details are given in the
       pcrecallout documentation.

       The global variable pcre_stack_guard initially contains NULL. It can be
       set  by  the  caller  to  a function that is called by PCRE whenever it
       starts to compile a parenthesized part of a pattern.  When  parentheses
       are nested, PCRE uses recursive function calls, which use up the system
       stack. This function is provided so that applications  with  restricted
       stacks  can  force a compilation error if the stack runs out. The func-
       tion should return zero if all is well, or non-zero to force an error.


NEWLINES

       PCRE supports five different conventions for indicating line breaks  in
       strings:  a  single  CR (carriage return) character, a single LF (line-
       feed) character, the two-character sequence CRLF, any of the three pre-
       ceding,  or any Unicode newline sequence. The Unicode newline sequences
       are the three just mentioned, plus the single characters  VT  (vertical
       tab, U+000B), FF (form feed, U+000C), NEL (next line, U+0085), LS (line
       separator, U+2028), and PS (paragraph separator, U+2029).

       Each of the first three conventions is used by at least  one  operating
       system  as its standard newline sequence. When PCRE is built, a default
       can be specified.  The default default is LF, which is the  Unix  stan-
       dard.  When  PCRE  is run, the default can be overridden, either when a
       pattern is compiled, or when it is matched.

       At compile time, the newline convention can be specified by the options
       argument  of  pcre_compile(), or it can be specified by special text at
       the start of the pattern itself; this overrides any other settings. See
       the pcrepattern page for details of the special character sequences.

       In the PCRE documentation the word "newline" is used to mean "the char-
       acter or pair of characters that indicate a line break". The choice  of
       newline  convention  affects  the  handling of the dot, circumflex, and
       dollar metacharacters, the handling of #-comments in /x mode, and, when
       CRLF  is a recognized line ending sequence, the match position advance-
       ment for a non-anchored pattern. There is more detail about this in the
       section on pcre_exec() options below.

       The  choice of newline convention does not affect the interpretation of
       the \n or \r escape sequences, nor does  it  affect  what  \R  matches,
       which is controlled in a similar way, but by separate options.


MULTITHREADING

       The  PCRE  functions  can be used in multi-threading applications, with
       the  proviso  that  the  memory  management  functions  pointed  to  by
       pcre_malloc, pcre_free, pcre_stack_malloc, and pcre_stack_free, and the
       callout and stack-checking functions pointed  to  by  pcre_callout  and
       pcre_stack_guard, are shared by all threads.

       The  compiled form of a regular expression is not altered during match-
       ing, so the same compiled pattern can safely be used by several threads
       at once.

       If  the just-in-time optimization feature is being used, it needs sepa-
       rate memory stack areas for each thread. See the pcrejit  documentation
       for more details.


SAVING PRECOMPILED PATTERNS FOR LATER USE

       The compiled form of a regular expression can be saved and re-used at a
       later time, possibly by a different program, and even on a  host  other
       than  the  one  on  which  it  was  compiled.  Details are given in the
       pcreprecompile documentation,  which  includes  a  description  of  the
       pcre_pattern_to_host_byte_order()  function. However, compiling a regu-
       lar expression with one version of PCRE for use with a  different  ver-
       sion is not guaranteed to work and may cause crashes.


CHECKING BUILD-TIME OPTIONS

       int pcre_config(int what, void *where);

       The  function pcre_config() makes it possible for a PCRE client to dis-
       cover which optional features have been compiled into the PCRE library.
       The  pcrebuild documentation has more details about these optional fea-
       tures.

       The first argument for pcre_config() is an  integer,  specifying  which
       information is required; the second argument is a pointer to a variable
       into which the information is placed. The returned  value  is  zero  on
       success,  or  the negative error code PCRE_ERROR_BADOPTION if the value
       in the first argument is not recognized. The following  information  is
       available:

         PCRE_CONFIG_UTF8

       The  output is an integer that is set to one if UTF-8 support is avail-
       able; otherwise it is set to zero. This value should normally be  given
       to the 8-bit version of this function, pcre_config(). If it is given to
       the  16-bit  or  32-bit  version  of  this  function,  the  result   is
       PCRE_ERROR_BADOPTION.

         PCRE_CONFIG_UTF16

       The output is an integer that is set to one if UTF-16 support is avail-
       able; otherwise it is set to zero. This value should normally be  given
       to the 16-bit version of this function, pcre16_config(). If it is given
       to the 8-bit  or  32-bit  version  of  this  function,  the  result  is
       PCRE_ERROR_BADOPTION.

         PCRE_CONFIG_UTF32

       The output is an integer that is set to one if UTF-32 support is avail-
       able; otherwise it is set to zero. This value should normally be  given
       to the 32-bit version of this function, pcre32_config(). If it is given
       to the 8-bit  or  16-bit  version  of  this  function,  the  result  is
       PCRE_ERROR_BADOPTION.

         PCRE_CONFIG_UNICODE_PROPERTIES

       The  output  is  an  integer  that is set to one if support for Unicode
       character properties is available; otherwise it is set to zero.

         PCRE_CONFIG_JIT

       The output is an integer that is set to one if support for just-in-time
       compiling is available; otherwise it is set to zero.

         PCRE_CONFIG_JITTARGET

       The  output is a pointer to a zero-terminated "const char *" string. If
       JIT support is available, the string contains the name of the architec-
       ture  for  which the JIT compiler is configured, for example "x86 32bit
       (little endian + unaligned)". If JIT  support  is  not  available,  the
       result is NULL.

         PCRE_CONFIG_NEWLINE

       The  output  is  an integer whose value specifies the default character
       sequence that is recognized as meaning "newline". The values  that  are
       supported in ASCII/Unicode environments are: 10 for LF, 13 for CR, 3338
       for CRLF, -2 for ANYCRLF, and -1 for ANY. In EBCDIC  environments,  CR,
       ANYCRLF,  and  ANY  yield the same values. However, the value for LF is
       normally 21, though some EBCDIC environments use 37. The  corresponding
       values  for  CRLF are 3349 and 3365. The default should normally corre-
       spond to the standard sequence for your operating system.

         PCRE_CONFIG_BSR

       The output is an integer whose value indicates what character sequences
       the  \R  escape sequence matches by default. A value of 0 means that \R
       matches any Unicode line ending sequence; a value of 1  means  that  \R
       matches only CR, LF, or CRLF. The default can be overridden when a pat-
       tern is compiled or matched.

         PCRE_CONFIG_LINK_SIZE

       The output is an integer that contains the number  of  bytes  used  for
       internal  linkage  in  compiled  regular  expressions.  For  the  8-bit
       library, the value can be 2, 3, or 4. For the 16-bit library, the value
       is  either  2  or  4  and  is  still  a number of bytes. For the 32-bit
       library, the value is either 2 or 4 and is still a number of bytes. The
       default value of 2 is sufficient for all but the most massive patterns,
       since it allows the compiled pattern to be up to 64K  in  size.  Larger
       values  allow larger regular expressions to be compiled, at the expense
       of slower matching.

         PCRE_CONFIG_POSIX_MALLOC_THRESHOLD

       The output is an integer that contains the threshold  above  which  the
       POSIX  interface  uses malloc() for output vectors. Further details are
       given in the pcreposix documentation.

         PCRE_CONFIG_PARENS_LIMIT

       The output is a long integer that gives the maximum depth of nesting of
       parentheses  (of  any  kind) in a pattern. This limit is imposed to cap
       the amount of system stack used when a pattern is compiled. It is spec-
       ified  when PCRE is built; the default is 250. This limit does not take
       into account the stack that may already be used by the calling applica-
       tion.  For  finer  control  over compilation stack usage, you can set a
       pointer to an external checking function in pcre_stack_guard.

         PCRE_CONFIG_MATCH_LIMIT

       The output is a long integer that gives the default limit for the  num-
       ber  of  internal  matching  function calls in a pcre_exec() execution.
       Further details are given with pcre_exec() below.

         PCRE_CONFIG_MATCH_LIMIT_RECURSION

       The output is a long integer that gives the default limit for the depth
       of   recursion  when  calling  the  internal  matching  function  in  a
       pcre_exec() execution.  Further  details  are  given  with  pcre_exec()
       below.

         PCRE_CONFIG_STACKRECURSE

       The  output is an integer that is set to one if internal recursion when
       running pcre_exec() is implemented by recursive function calls that use
       the  stack  to remember their state. This is the usual way that PCRE is
       compiled. The output is zero if PCRE was compiled to use blocks of data
       on  the  heap  instead  of  recursive  function  calls.  In  this case,
       pcre_stack_malloc and  pcre_stack_free  are  called  to  manage  memory
       blocks on the heap, thus avoiding the use of the stack.


COMPILING A PATTERN

       pcre *pcre_compile(const char *pattern, int options,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       pcre *pcre_compile2(const char *pattern, int options,
            int *errorcodeptr,
            const char **errptr, int *erroffset,
            const unsigned char *tableptr);

       Either of the functions pcre_compile() or pcre_compile2() can be called
       to compile a pattern into an internal form. The only difference between
       the  two interfaces is that pcre_compile2() has an additional argument,
       errorcodeptr, via which a numerical error  code  can  be  returned.  To
       avoid  too  much repetition, we refer just to pcre_compile() below, but
       the information applies equally to pcre_compile2().

       The pattern is a C string terminated by a binary zero, and is passed in
       the  pattern  argument.  A  pointer to a single block of memory that is
       obtained via pcre_malloc is returned. This contains the  compiled  code
       and related data. The pcre type is defined for the returned block; this
       is a typedef for a structure whose contents are not externally defined.
       It is up to the caller to free the memory (via pcre_free) when it is no
       longer required.

       Although the compiled code of a PCRE regex is relocatable, that is,  it
       does not depend on memory location, the complete pcre data block is not
       fully relocatable, because it may contain a copy of the tableptr  argu-
       ment, which is an address (see below).

       The options argument contains various bit settings that affect the com-
       pilation. It should be zero if no options are required.  The  available
       options  are  described  below. Some of them (in particular, those that
       are compatible with Perl, but some others as well) can also be set  and
       unset  from  within  the  pattern  (see the detailed description in the
       pcrepattern documentation). For those options that can be different  in
       different  parts  of  the pattern, the contents of the options argument
       specifies their settings at the start of compilation and execution. The
       PCRE_ANCHORED,  PCRE_BSR_xxx, PCRE_NEWLINE_xxx, PCRE_NO_UTF8_CHECK, and
       PCRE_NO_START_OPTIMIZE options can be set at the time  of  matching  as
       well as at compile time.

       If errptr is NULL, pcre_compile() returns NULL immediately.  Otherwise,
       if compilation of a pattern fails,  pcre_compile()  returns  NULL,  and
       sets the variable pointed to by errptr to point to a textual error mes-
       sage. This is a static string that is part of the library. You must not
       try  to  free it. Normally, the offset from the start of the pattern to
       the data unit that was being processed when the error was discovered is
       placed  in the variable pointed to by erroffset, which must not be NULL
       (if it is, an immediate error is given). However, for an invalid  UTF-8
       or  UTF-16  string,  the  offset  is that of the first data unit of the
       failing character.

       Some errors are not detected until the whole pattern has been  scanned;
       in  these  cases,  the offset passed back is the length of the pattern.
       Note that the offset is in data units, not characters, even  in  a  UTF
       mode. It may sometimes point into the middle of a UTF-8 or UTF-16 char-
       acter.

       If pcre_compile2() is used instead of pcre_compile(),  and  the  error-
       codeptr  argument is not NULL, a non-zero error code number is returned
       via this argument in the event of an error. This is in addition to  the
       textual error message. Error codes and messages are listed below.

       If  the  final  argument, tableptr, is NULL, PCRE uses a default set of
       character tables that are  built  when  PCRE  is  compiled,  using  the
       default  C  locale.  Otherwise, tableptr must be an address that is the
       result of a call to pcre_maketables(). This value is  stored  with  the
       compiled  pattern,  and  used  again by pcre_exec() and pcre_dfa_exec()
       when the pattern is matched. For more discussion, see  the  section  on
       locale support below.

       This  code  fragment  shows a typical straightforward call to pcre_com-
       pile():

         pcre *re;
         const char *error;
         int erroffset;
         re = pcre_compile(
           "^A.*Z",          /* the pattern */
           0,                /* default options */
           &error,           /* for error message */
           &erroffset,       /* for error offset */
           NULL);            /* use default character tables */

       The following names for option bits are defined in  the  pcre.h  header
       file:

         PCRE_ANCHORED

       If this bit is set, the pattern is forced to be "anchored", that is, it
       is constrained to match only at the first matching point in the  string
       that  is being searched (the "subject string"). This effect can also be
       achieved by appropriate constructs in the pattern itself, which is  the
       only way to do it in Perl.

         PCRE_AUTO_CALLOUT

       If this bit is set, pcre_compile() automatically inserts callout items,
       all with number 255, before each pattern item. For  discussion  of  the
       callout facility, see the pcrecallout documentation.

         PCRE_BSR_ANYCRLF
         PCRE_BSR_UNICODE

       These options (which are mutually exclusive) control what the \R escape
       sequence matches. The choice is either to match only CR, LF,  or  CRLF,
       or to match any Unicode newline sequence. The default is specified when
       PCRE is built. It can be overridden from within the pattern, or by set-
       ting an option when a compiled pattern is matched.

         PCRE_CASELESS

       If  this  bit is set, letters in the pattern match both upper and lower
       case letters. It is equivalent to Perl's  /i  option,  and  it  can  be
       changed  within a pattern by a (?i) option setting. In UTF-8 mode, PCRE
       always understands the concept of case for characters whose values  are
       less  than 128, so caseless matching is always possible. For characters
       with higher values, the concept of case is supported if  PCRE  is  com-
       piled  with Unicode property support, but not otherwise. If you want to
       use caseless matching for characters 128 and  above,  you  must  ensure
       that  PCRE  is  compiled  with Unicode property support as well as with
       UTF-8 support.

         PCRE_DOLLAR_ENDONLY

       If this bit is set, a dollar metacharacter in the pattern matches  only
       at  the  end  of the subject string. Without this option, a dollar also
       matches immediately before a newline at the end of the string (but  not
       before  any  other newlines). The PCRE_DOLLAR_ENDONLY option is ignored
       if PCRE_MULTILINE is set.  There is no equivalent  to  this  option  in
       Perl, and no way to set it within a pattern.

         PCRE_DOTALL

       If  this bit is set, a dot metacharacter in the pattern matches a char-
       acter of any value, including one that indicates a newline. However, it
       only  ever  matches  one character, even if newlines are coded as CRLF.
       Without this option, a dot does not match when the current position  is
       at a newline. This option is equivalent to Perl's /s option, and it can
       be changed within a pattern by a (?s) option setting. A negative  class
       such as [^a] always matches newline characters, independent of the set-
       ting of this option.

         PCRE_DUPNAMES

       If this bit is set, names used to identify capturing  subpatterns  need
       not be unique. This can be helpful for certain types of pattern when it
       is known that only one instance of the named  subpattern  can  ever  be
       matched.  There  are  more details of named subpatterns below; see also
       the pcrepattern documentation.

         PCRE_EXTENDED

       If this bit is set, most white space  characters  in  the  pattern  are
       totally  ignored  except when escaped or inside a character class. How-
       ever, white space is not allowed within  sequences  such  as  (?>  that
       introduce  various  parenthesized  subpatterns,  nor within a numerical
       quantifier such as {1,3}.  However, ignorable white space is  permitted
       between an item and a following quantifier and between a quantifier and
       a following + that indicates possessiveness.

       White space did not used to include the VT character (code 11), because
       Perl did not treat this character as white space. However, Perl changed
       at release 5.18, so PCRE followed  at  release  8.34,  and  VT  is  now
       treated as white space.

       PCRE_EXTENDED  also  causes characters between an unescaped # outside a
       character class  and  the  next  newline,  inclusive,  to  be  ignored.
       PCRE_EXTENDED  is equivalent to Perl's /x option, and it can be changed
       within a pattern by a (?x) option setting.

       Which characters are interpreted  as  newlines  is  controlled  by  the
       options  passed to pcre_compile() or by a special sequence at the start
       of the pattern, as described in the section entitled  "Newline  conven-
       tions" in the pcrepattern documentation. Note that the end of this type
       of comment is  a  literal  newline  sequence  in  the  pattern;  escape
       sequences that happen to represent a newline do not count.

       This  option  makes  it possible to include comments inside complicated
       patterns.  Note, however, that this applies only  to  data  characters.
       White  space  characters  may  never  appear  within  special character
       sequences in a pattern, for example within the sequence (?( that intro-
       duces a conditional subpattern.

         PCRE_EXTRA

       This  option  was invented in order to turn on additional functionality
       of PCRE that is incompatible with Perl, but it  is  currently  of  very
       little  use. When set, any backslash in a pattern that is followed by a
       letter that has no special meaning  causes  an  error,  thus  reserving
       these  combinations  for  future  expansion.  By default, as in Perl, a
       backslash followed by a letter with no special meaning is treated as  a
       literal. (Perl can, however, be persuaded to give an error for this, by
       running it with the -w option.) There are at present no other  features
       controlled  by this option. It can also be set by a (?X) option setting
       within a pattern.

         PCRE_FIRSTLINE

       If this option is set, an  unanchored  pattern  is  required  to  match
       before  or  at  the  first  newline  in  the subject string, though the
       matched text may continue over the newline.

         PCRE_JAVASCRIPT_COMPAT

       If this option is set, PCRE's behaviour is changed in some ways so that
       it  is  compatible with JavaScript rather than Perl. The changes are as
       follows:

       (1) A lone closing square bracket in a pattern  causes  a  compile-time
       error,  because this is illegal in JavaScript (by default it is treated
       as a data character). Thus, the pattern AB]CD becomes illegal when this
       option is set.

       (2)  At run time, a back reference to an unset subpattern group matches
       an empty string (by default this causes the current  matching  alterna-
       tive  to  fail). A pattern such as (\1)(a) succeeds when this option is
       set (assuming it can find an "a" in the subject), whereas it  fails  by
       default, for Perl compatibility.

       (3) \U matches an upper case "U" character; by default \U causes a com-
       pile time error (Perl uses \U to upper case subsequent characters).

       (4) \u matches a lower case "u" character unless it is followed by four
       hexadecimal  digits,  in  which case the hexadecimal number defines the
       code point to match. By default, \u causes a compile time  error  (Perl
       uses it to upper case the following character).

       (5)  \x matches a lower case "x" character unless it is followed by two
       hexadecimal digits, in which case the hexadecimal  number  defines  the
       code  point  to  match. By default, as in Perl, a hexadecimal number is
       always expected after \x, but it may have zero, one, or two digits (so,
       for example, \xz matches a binary zero character followed by z).

         PCRE_MULTILINE

       By  default,  for  the purposes of matching "start of line" and "end of
       line", PCRE treats the subject string as consisting of a single line of
       characters,  even if it actually contains newlines. The "start of line"
       metacharacter (^) matches only at the start of the string, and the "end
       of  line"  metacharacter  ($) matches only at the end of the string, or
       before a terminating newline (except when PCRE_DOLLAR_ENDONLY is  set).
       Note,  however,  that  unless  PCRE_DOTALL  is set, the "any character"
       metacharacter (.) does not match at a newline. This behaviour  (for  ^,
       $, and dot) is the same as Perl.

       When  PCRE_MULTILINE  it  is set, the "start of line" and "end of line"
       constructs match immediately following or immediately  before  internal
       newlines  in  the  subject string, respectively, as well as at the very
       start and end. This is equivalent to Perl's /m option, and  it  can  be
       changed within a pattern by a (?m) option setting. If there are no new-
       lines in a subject string, or no occurrences of ^ or $  in  a  pattern,
       setting PCRE_MULTILINE has no effect.

         PCRE_NEVER_UTF

       This option locks out interpretation of the pattern as UTF-8 (or UTF-16
       or UTF-32 in the 16-bit and 32-bit libraries). In particular,  it  pre-
       vents  the  creator of the pattern from switching to UTF interpretation
       by starting the pattern with (*UTF). This may be useful in applications
       that  process  patterns  from  external  sources.  The  combination  of
       PCRE_UTF8 and PCRE_NEVER_UTF also causes an error.

         PCRE_NEWLINE_CR
         PCRE_NEWLINE_LF
         PCRE_NEWLINE_CRLF
         PCRE_NEWLINE_ANYCRLF
         PCRE_NEWLINE_ANY

       These options override the default newline definition that  was  chosen
       when  PCRE  was built. Setting the first or the second specifies that a
       newline is indicated by a single character (CR  or  LF,  respectively).
       Setting  PCRE_NEWLINE_CRLF specifies that a newline is indicated by the
       two-character CRLF  sequence.  Setting  PCRE_NEWLINE_ANYCRLF  specifies
       that any of the three preceding sequences should be recognized. Setting
       PCRE_NEWLINE_ANY specifies that any Unicode newline sequence should  be
       recognized.

       In  an ASCII/Unicode environment, the Unicode newline sequences are the
       three just mentioned, plus the  single  characters  VT  (vertical  tab,
       U+000B), FF (form feed, U+000C), NEL (next line, U+0085), LS (line sep-
       arator, U+2028), and PS (paragraph separator, U+2029).  For  the  8-bit
       library, the last two are recognized only in UTF-8 mode.

       When  PCRE is compiled to run in an EBCDIC (mainframe) environment, the
       code for CR is 0x0d, the same as ASCII. However, the character code for
       LF  is  normally 0x15, though in some EBCDIC environments 0x25 is used.
       Whichever of these is not LF is made to  correspond  to  Unicode's  NEL
       character.  EBCDIC  codes  are all less than 256. For more details, see
       the pcrebuild documentation.

       The newline setting in the  options  word  uses  three  bits  that  are
       treated as a number, giving eight possibilities. Currently only six are
       used (default plus the five values above). This means that if  you  set
       more  than one newline option, the combination may or may not be sensi-
       ble. For example, PCRE_NEWLINE_CR with PCRE_NEWLINE_LF is equivalent to
       PCRE_NEWLINE_CRLF,  but other combinations may yield unused numbers and
       cause an error.

       The only time that a line break in a pattern  is  specially  recognized
       when  compiling is when PCRE_EXTENDED is set. CR and LF are white space
       characters, and so are ignored in this mode. Also, an unescaped #  out-
       side  a  character class indicates a comment that lasts until after the
       next line break sequence. In other circumstances, line break  sequences
       in patterns are treated as literal data.

       The newline option that is set at compile time becomes the default that
       is used for pcre_exec() and pcre_dfa_exec(), but it can be overridden.

         PCRE_NO_AUTO_CAPTURE

       If this option is set, it disables the use of numbered capturing paren-
       theses  in the pattern. Any opening parenthesis that is not followed by
       ? behaves as if it were followed by ?: but named parentheses can  still
       be  used  for  capturing  (and  they acquire numbers in the usual way).
       There is no equivalent of this option in Perl.

         PCRE_NO_AUTO_POSSESS

       If this option is set, it disables "auto-possessification". This is  an
       optimization  that,  for example, turns a+b into a++b in order to avoid
       backtracks into a+ that can never be successful. However,  if  callouts
       are  in  use,  auto-possessification  means that some of them are never
       taken. You can set this option if you want the matching functions to do
       a  full  unoptimized  search and run all the callouts, but it is mainly
       provided for testing purposes.

         PCRE_NO_START_OPTIMIZE

       This is an option that acts at matching time; that is, it is really  an
       option  for  pcre_exec()  or  pcre_dfa_exec().  If it is set at compile
       time, it is remembered with the compiled pattern and assumed at  match-
       ing  time.  This is necessary if you want to use JIT execution, because
       the JIT compiler needs to know whether or not this option is  set.  For
       details see the discussion of PCRE_NO_START_OPTIMIZE below.

         PCRE_UCP

       This  option changes the way PCRE processes \B, \b, \D, \d, \S, \s, \W,
       \w, and some of the POSIX character classes.  By  default,  only  ASCII
       characters  are  recognized, but if PCRE_UCP is set, Unicode properties
       are used instead to classify characters. More details are given in  the
       section  on generic character types in the pcrepattern page. If you set
       PCRE_UCP, matching one of the items it affects takes much  longer.  The
       option  is  available only if PCRE has been compiled with Unicode prop-
       erty support.

         PCRE_UNGREEDY

       This option inverts the "greediness" of the quantifiers  so  that  they
       are  not greedy by default, but become greedy if followed by "?". It is
       not compatible with Perl. It can also be set by a (?U)  option  setting
       within the pattern.

         PCRE_UTF8

       This  option  causes PCRE to regard both the pattern and the subject as
       strings of UTF-8 characters instead of single-byte strings. However, it
       is  available  only  when PCRE is built to include UTF support. If not,
       the use of this option provokes an error. Details of  how  this  option
       changes the behaviour of PCRE are given in the pcreunicode page.

         PCRE_NO_UTF8_CHECK

       When PCRE_UTF8 is set, the validity of the pattern as a UTF-8 string is
       automatically checked. There is a  discussion  about  the  validity  of
       UTF-8  strings in the pcreunicode page. If an invalid UTF-8 sequence is
       found, pcre_compile() returns an error. If you already know  that  your
       pattern  is valid, and you want to skip this check for performance rea-
       sons, you can set the PCRE_NO_UTF8_CHECK option.  When it is  set,  the
       effect of passing an invalid UTF-8 string as a pattern is undefined. It
       may cause your program to crash or loop. Note that this option can also
       be  passed to pcre_exec() and pcre_dfa_exec(), to suppress the validity
       checking of subject strings only. If the same string is  being  matched
       many  times, the option can be safely set for the second and subsequent
       matchings to improve performance.


COMPILATION ERROR CODES

       The following table lists the error  codes  than  may  be  returned  by
       pcre_compile2(),  along with the error messages that may be returned by
       both compiling functions. Note that error  messages  are  always  8-bit
       ASCII  strings,  even  in 16-bit or 32-bit mode. As PCRE has developed,
       some error codes have fallen out of use. To avoid confusion, they  have
       not been re-used.

          0  no error
          1  \ at end of pattern
          2  \c at end of pattern
          3  unrecognized character follows \
          4  numbers out of order in {} quantifier
          5  number too big in {} quantifier
          6  missing terminating ] for character class
          7  invalid escape sequence in character class
          8  range out of order in character class
          9  nothing to repeat
         10  [this code is not in use]
         11  internal error: unexpected repeat
         12  unrecognized character after (? or (?-
         13  POSIX named classes are supported only within a class
         14  missing )
         15  reference to non-existent subpattern
         16  erroffset passed as NULL
         17  unknown option bit(s) set
         18  missing ) after comment
         19  [this code is not in use]
         20  regular expression is too large
         21  failed to get memory
         22  unmatched parentheses
         23  internal error: code overflow
         24  unrecognized character after (?<
         25  lookbehind assertion is not fixed length
         26  malformed number or name after (?(
         27  conditional group contains more than two branches
         28  assertion expected after (?(
         29  (?R or (?[+-]digits must be followed by )
         30  unknown POSIX class name
         31  POSIX collating elements are not supported
         32  this version of PCRE is compiled without UTF support
         33  [this code is not in use]
         34  character value in \x{} or \o{} is too large
         35  invalid condition (?(0)
         36  \C not allowed in lookbehind assertion
         37  PCRE does not support \L, \l, \N{name}, \U, or \u
         38  number after (?C is > 255
         39  closing ) for (?C expected
         40  recursive call could loop indefinitely
         41  unrecognized character after (?P
         42  syntax error in subpattern name (missing terminator)
         43  two named subpatterns have the same name
         44  invalid UTF-8 string (specifically UTF-8)
         45  support for \P, \p, and \X has not been compiled
         46  malformed \P or \p sequence
         47  unknown property name after \P or \p
         48  subpattern name is too long (maximum 32 characters)
         49  too many named subpatterns (maximum 10000)
         50  [this code is not in use]
         51  octal value is greater than \377 in 8-bit non-UTF-8 mode
         52  internal error: overran compiling workspace
         53  internal error: previously-checked referenced subpattern
               not found
         54  DEFINE group contains more than one branch
         55  repeating a DEFINE group is not allowed
         56  inconsistent NEWLINE options
         57  \g is not followed by a braced, angle-bracketed, or quoted
               name/number or by a plain number
         58  a numbered reference must not be zero
         59  an argument is not allowed for (*ACCEPT), (*FAIL), or (*COMMIT)
         60  (*VERB) not recognized or malformed
         61  number is too big
         62  subpattern name expected
         63  digit expected after (?+
         64  ] is an invalid data character in JavaScript compatibility mode
         65  different names for subpatterns of the same number are
               not allowed
         66  (*MARK) must have an argument
         67  this version of PCRE is not compiled with Unicode property
               support
         68  \c must be followed by an ASCII character
         69  \k is not followed by a braced, angle-bracketed, or quoted name
         70  internal error: unknown opcode in find_fixedlength()
         71  \N is not supported in a class
         72  too many forward references
         73  disallowed Unicode code point (>= 0xd800 && <= 0xdfff)
         74  invalid UTF-16 string (specifically UTF-16)
         75  name is too long in (*MARK), (*PRUNE), (*SKIP), or (*THEN)
         76  character value in \u.... sequence is too large
         77  invalid UTF-32 string (specifically UTF-32)
         78  setting UTF is disabled by the application
         79  non-hex character in \x{} (closing brace missing?)
         80  non-octal character in \o{} (closing brace missing?)
         81  missing opening brace after \o
         82  parentheses are too deeply nested
         83  invalid range in character class
         84  group name must start with a non-digit
         85  parentheses are too deeply nested (stack check)

       The  numbers  32  and 10000 in errors 48 and 49 are defaults; different
       values may be used if the limits were changed when PCRE was built.


STUDYING A PATTERN

       pcre_extra *pcre_study(const pcre *code, int options,
            const char **errptr);

       If a compiled pattern is going to be used several times,  it  is  worth
       spending more time analyzing it in order to speed up the time taken for
       matching. The function pcre_study() takes a pointer to a compiled  pat-
       tern as its first argument. If studying the pattern produces additional
       information that will help speed up matching,  pcre_study()  returns  a
       pointer  to a pcre_extra block, in which the study_data field points to
       the results of the study.

       The  returned  value  from  pcre_study()  can  be  passed  directly  to
       pcre_exec()  or  pcre_dfa_exec(). However, a pcre_extra block also con-
       tains other fields that can be set by the caller before  the  block  is
       passed; these are described below in the section on matching a pattern.

       If  studying  the  pattern  does  not  produce  any useful information,
       pcre_study() returns NULL by default.  In  that  circumstance,  if  the
       calling program wants to pass any of the other fields to pcre_exec() or
       pcre_dfa_exec(), it must set up its own pcre_extra block.  However,  if
       pcre_study()  is  called  with  the  PCRE_STUDY_EXTRA_NEEDED option, it
       returns a pcre_extra block even if studying did not find any additional
       information.  It  may still return NULL, however, if an error occurs in
       pcre_study().

       The second argument of pcre_study() contains  option  bits.  There  are
       three further options in addition to PCRE_STUDY_EXTRA_NEEDED:

         PCRE_STUDY_JIT_COMPILE
         PCRE_STUDY_JIT_PARTIAL_HARD_COMPILE
         PCRE_STUDY_JIT_PARTIAL_SOFT_COMPILE

       If  any  of  these are set, and the just-in-time compiler is available,
       the pattern is further compiled into machine code  that  executes  much
       faster  than  the  pcre_exec()  interpretive  matching function. If the
       just-in-time compiler is not available, these options are ignored.  All
       undefined bits in the options argument must be zero.

       JIT  compilation  is  a heavyweight optimization. It can take some time
       for patterns to be analyzed, and for one-off matches  and  simple  pat-
       terns  the benefit of faster execution might be offset by a much slower
       study time.  Not all patterns can be optimized by the JIT compiler. For
       those  that cannot be handled, matching automatically falls back to the
       pcre_exec() interpreter. For more details, see the  pcrejit  documenta-
       tion.

       The  third argument for pcre_study() is a pointer for an error message.
       If studying succeeds (even if no data is  returned),  the  variable  it
       points  to  is  set  to NULL. Otherwise it is set to point to a textual
       error message. This is a static string that is part of the library. You
       must  not  try  to  free it. You should test the error pointer for NULL
       after calling pcre_study(), to be sure that it has run successfully.

       When you are finished with a pattern, you can free the memory used  for
       the study data by calling pcre_free_study(). This function was added to
       the API for release 8.20. For earlier versions,  the  memory  could  be
       freed  with  pcre_free(), just like the pattern itself. This will still
       work in cases where JIT optimization is not used, but it  is  advisable
       to change to the new function when convenient.

       This  is  a typical way in which pcre_study() is used (except that in a
       real application there should be tests for errors):

         int rc;
         pcre *re;
         pcre_extra *sd;
         re = pcre_compile("pattern", 0, &error, &erroroffset, NULL);
         sd = pcre_study(
           re,             /* result of pcre_compile() */
           0,              /* no options */
           &error);        /* set to NULL or points to a message */
         rc = pcre_exec(   /* see below for details of pcre_exec() options */
           re, sd, "subject", 7, 0, 0, ovector, 30);
         ...
         pcre_free_study(sd);
         pcre_free(re);

       Studying a pattern does two things: first, a lower bound for the length
       of subject string that is needed to match the pattern is computed. This
       does not mean that there are any strings of that length that match, but
       it  does  guarantee that no shorter strings match. The value is used to
       avoid wasting time by trying to match strings that are shorter than the
       lower  bound.  You  can find out the value in a calling program via the
       pcre_fullinfo() function.

       Studying a pattern is also useful for non-anchored patterns that do not
       have  a  single fixed starting character. A bitmap of possible starting
       bytes is created. This speeds up finding a position in the  subject  at
       which to start matching. (In 16-bit mode, the bitmap is used for 16-bit
       values less than 256.  In 32-bit mode, the bitmap is  used  for  32-bit
       values less than 256.)

       These  two optimizations apply to both pcre_exec() and pcre_dfa_exec(),
       and the information is also used by the JIT  compiler.   The  optimiza-
       tions  can  be  disabled  by setting the PCRE_NO_START_OPTIMIZE option.
       You might want to do this if your pattern contains callouts or  (*MARK)
       and  you  want  to make use of these facilities in cases where matching
       fails.

       PCRE_NO_START_OPTIMIZE can be specified at either compile time or  exe-
       cution   time.   However,   if   PCRE_NO_START_OPTIMIZE  is  passed  to
       pcre_exec(), (that is, after any JIT compilation has happened) JIT exe-
       cution  is disabled. For JIT execution to work with PCRE_NO_START_OPTI-
       MIZE, the option must be set at compile time.

       There is a longer discussion of PCRE_NO_START_OPTIMIZE below.


LOCALE SUPPORT

       PCRE handles caseless matching, and determines whether  characters  are
       letters,  digits, or whatever, by reference to a set of tables, indexed
       by character code point. When running in UTF-8 mode, or in the  16-  or
       32-bit libraries, this applies only to characters with code points less
       than 256. By default, higher-valued code  points  never  match  escapes
       such  as \w or \d. However, if PCRE is built with Unicode property sup-
       port, all characters can be tested with \p and \P,  or,  alternatively,
       the  PCRE_UCP option can be set when a pattern is compiled; this causes
       \w and friends to use Unicode property support instead of the  built-in
       tables.

       The  use  of  locales  with Unicode is discouraged. If you are handling
       characters with code points greater than 128,  you  should  either  use
       Unicode support, or use locales, but not try to mix the two.

       PCRE  contains  an  internal set of tables that are used when the final
       argument of pcre_compile() is  NULL.  These  are  sufficient  for  many
       applications.  Normally, the internal tables recognize only ASCII char-
       acters. However, when PCRE is built, it is possible to cause the inter-
       nal tables to be rebuilt in the default "C" locale of the local system,
       which may cause them to be different.

       The internal tables can always be overridden by tables supplied by  the
       application that calls PCRE. These may be created in a different locale
       from the default. As more and more applications change  to  using  Uni-
       code, the need for this locale support is expected to die away.

       External  tables  are  built by calling the pcre_maketables() function,
       which has no arguments, in the relevant locale. The result can then  be
       passed  to  pcre_compile() as often as necessary. For example, to build
       and use tables that  are  appropriate  for  the  French  locale  (where
       accented  characters  with  values greater than 128 are treated as let-
       ters), the following code could be used:

         setlocale(LC_CTYPE, "fr_FR");
         tables = pcre_maketables();
         re = pcre_compile(..., tables);

       The locale name "fr_FR" is used on Linux and other  Unix-like  systems;
       if you are using Windows, the name for the French locale is "french".

       When  pcre_maketables()  runs,  the  tables are built in memory that is
       obtained via pcre_malloc. It is the caller's responsibility  to  ensure
       that  the memory containing the tables remains available for as long as
       it is needed.

       The pointer that is passed to pcre_compile() is saved with the compiled
       pattern,  and the same tables are used via this pointer by pcre_study()
       and also by pcre_exec() and pcre_dfa_exec(). Thus, for any single  pat-
       tern, compilation, studying and matching all happen in the same locale,
       but different patterns can be processed in different locales.

       It is possible to pass a table pointer or NULL (indicating the  use  of
       the internal tables) to pcre_exec() or pcre_dfa_exec() (see the discus-
       sion below in the section on matching a pattern). This facility is pro-
       vided  for  use  with  pre-compiled  patterns  that have been saved and
       reloaded.  Character tables are not saved with patterns, so if  a  non-
       standard table was used at compile time, it must be provided again when
       the reloaded pattern is matched. Attempting to  use  this  facility  to
       match a pattern in a different locale from the one in which it was com-
       piled is likely to lead to anomalous (usually incorrect) results.


INFORMATION ABOUT A PATTERN

       int pcre_fullinfo(const pcre *code, const pcre_extra *extra,
            int what, void *where);

       The pcre_fullinfo() function returns information about a compiled  pat-
       tern.  It replaces the pcre_info() function, which was removed from the
       library at version 8.30, after more than 10 years of obsolescence.

       The first argument for pcre_fullinfo() is a  pointer  to  the  compiled
       pattern.  The second argument is the result of pcre_study(), or NULL if
       the pattern was not studied. The third argument specifies  which  piece
       of  information  is required, and the fourth argument is a pointer to a
       variable to receive the data. The yield of the  function  is  zero  for
       success, or one of the following negative numbers:

         PCRE_ERROR_NULL           the argument code was NULL
                                   the argument where was NULL
         PCRE_ERROR_BADMAGIC       the "magic number" was not found
         PCRE_ERROR_BADENDIANNESS  the pattern was compiled with different
                                   endianness
         PCRE_ERROR_BADOPTION      the value of what was invalid
         PCRE_ERROR_UNSET          the requested field is not set

       The  "magic  number" is placed at the start of each compiled pattern as
       an simple check against passing an arbitrary memory pointer. The  endi-
       anness error can occur if a compiled pattern is saved and reloaded on a
       different host. Here is a typical call of  pcre_fullinfo(),  to  obtain
       the length of the compiled pattern:

         int rc;
         size_t length;
         rc = pcre_fullinfo(
           re,               /* result of pcre_compile() */
           sd,               /* result of pcre_study(), or NULL */
           PCRE_INFO_SIZE,   /* what is required */
           &length);         /* where to put the data */

       The  possible  values for the third argument are defined in pcre.h, and
       are as follows:

         PCRE_INFO_BACKREFMAX

       Return the number of the highest back reference  in  the  pattern.  The
       fourth  argument  should  point to an int variable. Zero is returned if
       there are no back references.

         PCRE_INFO_CAPTURECOUNT

       Return the number of capturing subpatterns in the pattern.  The  fourth
       argument should point to an int variable.

         PCRE_INFO_DEFAULT_TABLES

       Return  a pointer to the internal default character tables within PCRE.
       The fourth argument should point to an unsigned char *  variable.  This
       information call is provided for internal use by the pcre_study() func-
       tion. External callers can cause PCRE to use  its  internal  tables  by
       passing a NULL table pointer.

         PCRE_INFO_FIRSTBYTE (deprecated)

       Return information about the first data unit of any matched string, for
       a non-anchored pattern. The name of this option  refers  to  the  8-bit
       library,  where  data units are bytes. The fourth argument should point
       to an int variable. Negative values are used for  special  cases.  How-
       ever,  this  means  that when the 32-bit library is in non-UTF-32 mode,
       the full 32-bit range of characters cannot be returned. For  this  rea-
       son,  this  value  is deprecated; use PCRE_INFO_FIRSTCHARACTERFLAGS and
       PCRE_INFO_FIRSTCHARACTER instead.

       If there is a fixed first value, for example, the  letter  "c"  from  a
       pattern  such  as (cat|cow|coyote), its value is returned. In the 8-bit
       library, the value is always less than 256. In the 16-bit  library  the
       value can be up to 0xffff. In the 32-bit library the value can be up to
       0x10ffff.

       If there is no fixed first value, and if either

       (a) the pattern was compiled with the PCRE_MULTILINE option, and  every
       branch starts with "^", or

       (b) every branch of the pattern starts with ".*" and PCRE_DOTALL is not
       set (if it were set, the pattern would be anchored),

       -1 is returned, indicating that the pattern matches only at  the  start
       of  a  subject string or after any newline within the string. Otherwise
       -2 is returned. For anchored patterns, -2 is returned.

         PCRE_INFO_FIRSTCHARACTER

       Return the value of the first data  unit  (non-UTF  character)  of  any
       matched  string  in  the  situation where PCRE_INFO_FIRSTCHARACTERFLAGS
       returns 1; otherwise return 0. The fourth argument should point  to  an
       uint_t variable.

       In  the 8-bit library, the value is always less than 256. In the 16-bit
       library the value can be up to 0xffff. In the 32-bit library in  UTF-32
       mode  the  value  can  be up to 0x10ffff, and up to 0xffffffff when not
       using UTF-32 mode.

         PCRE_INFO_FIRSTCHARACTERFLAGS

       Return information about the first data unit of any matched string, for
       a  non-anchored  pattern.  The  fourth  argument should point to an int
       variable.

       If there is a fixed first value, for example, the  letter  "c"  from  a
       pattern  such  as  (cat|cow|coyote),  1  is returned, and the character
       value can be retrieved using PCRE_INFO_FIRSTCHARACTER. If there  is  no
       fixed first value, and if either

       (a)  the pattern was compiled with the PCRE_MULTILINE option, and every
       branch starts with "^", or

       (b) every branch of the pattern starts with ".*" and PCRE_DOTALL is not
       set (if it were set, the pattern would be anchored),

       2 is returned, indicating that the pattern matches only at the start of
       a subject string or after any newline within the string. Otherwise 0 is
       returned. For anchored patterns, 0 is returned.

         PCRE_INFO_FIRSTTABLE

       If  the pattern was studied, and this resulted in the construction of a
       256-bit table indicating a fixed set of values for the first data  unit
       in  any  matching string, a pointer to the table is returned. Otherwise
       NULL is returned. The fourth argument should point to an unsigned  char
       * variable.

         PCRE_INFO_HASCRORLF

       Return  1  if  the  pattern  contains any explicit matches for CR or LF
       characters, otherwise 0. The fourth argument should  point  to  an  int
       variable.  An explicit match is either a literal CR or LF character, or
       \r or \n.

         PCRE_INFO_JCHANGED

       Return 1 if the (?J) or (?-J) option setting is used  in  the  pattern,
       otherwise  0. The fourth argument should point to an int variable. (?J)
       and (?-J) set and unset the local PCRE_DUPNAMES option, respectively.

         PCRE_INFO_JIT

       Return 1 if the pattern was studied with one of the  JIT  options,  and
       just-in-time compiling was successful. The fourth argument should point
       to an int variable. A return value of 0 means that JIT support  is  not
       available  in this version of PCRE, or that the pattern was not studied
       with a JIT option, or that the JIT compiler could not handle this  par-
       ticular  pattern. See the pcrejit documentation for details of what can
       and cannot be handled.

         PCRE_INFO_JITSIZE

       If the pattern was successfully studied with a JIT option,  return  the
       size  of the JIT compiled code, otherwise return zero. The fourth argu-
       ment should point to a size_t variable.

         PCRE_INFO_LASTLITERAL

       Return the value of the rightmost literal data unit that must exist  in
       any  matched  string, other than at its start, if such a value has been
       recorded. The fourth argument should point to an int variable. If there
       is no such value, -1 is returned. For anchored patterns, a last literal
       value is recorded only if it follows something of variable length.  For
       example, for the pattern /^a\d+z\d+/ the returned value is "z", but for
       /^a\dz\d/ the returned value is -1.

       Since for the 32-bit library using the non-UTF-32 mode,  this  function
       is  unable to return the full 32-bit range of characters, this value is
       deprecated;     instead     the     PCRE_INFO_REQUIREDCHARFLAGS     and
       PCRE_INFO_REQUIREDCHAR values should be used.

         PCRE_INFO_MATCH_EMPTY

       Return  1  if  the  pattern can match an empty string, otherwise 0. The
       fourth argument should point to an int variable.

         PCRE_INFO_MATCHLIMIT

       If the pattern set a match limit by  including  an  item  of  the  form
       (*LIMIT_MATCH=nnnn)  at  the  start,  the value is returned. The fourth
       argument should point to an unsigned 32-bit integer. If no  such  value
       has   been   set,   the  call  to  pcre_fullinfo()  returns  the  error
       PCRE_ERROR_UNSET.

         PCRE_INFO_MAXLOOKBEHIND

       Return the number of characters (NB not  data  units)  in  the  longest
       lookbehind  assertion  in  the pattern. This information is useful when
       doing multi-segment matching using  the  partial  matching  facilities.
       Note that the simple assertions \b and \B require a one-character look-
       behind. \A also registers a one-character lookbehind,  though  it  does
       not  actually inspect the previous character. This is to ensure that at
       least one character from the old segment is retained when a new segment
       is processed. Otherwise, if there are no lookbehinds in the pattern, \A
       might match incorrectly at the start of a new segment.

         PCRE_INFO_MINLENGTH

       If the pattern was studied and a minimum length  for  matching  subject
       strings  was  computed,  its  value is returned. Otherwise the returned
       value is -1. The value is a number of characters, which in UTF mode may
       be  different from the number of data units. The fourth argument should
       point to an int variable. A non-negative value is a lower bound to  the
       length  of  any  matching  string. There may not be any strings of that
       length that do actually match, but every string that does match  is  at
       least that long.

         PCRE_INFO_NAMECOUNT
         PCRE_INFO_NAMEENTRYSIZE
         PCRE_INFO_NAMETABLE

       PCRE  supports the use of named as well as numbered capturing parenthe-
       ses. The names are just an additional way of identifying the  parenthe-
       ses, which still acquire numbers. Several convenience functions such as
       pcre_get_named_substring() are provided for  extracting  captured  sub-
       strings  by  name. It is also possible to extract the data directly, by
       first converting the name to a number in order to  access  the  correct
       pointers in the output vector (described with pcre_exec() below). To do
       the conversion, you need  to  use  the  name-to-number  map,  which  is
       described by these three values.

       The map consists of a number of fixed-size entries. PCRE_INFO_NAMECOUNT
       gives the number of entries, and PCRE_INFO_NAMEENTRYSIZE gives the size
       of  each  entry;  both  of  these  return  an int value. The entry size
       depends on the length of the longest name. PCRE_INFO_NAMETABLE  returns
       a pointer to the first entry of the table. This is a pointer to char in
       the 8-bit library, where the first two bytes of each entry are the num-
       ber  of  the capturing parenthesis, most significant byte first. In the
       16-bit library, the pointer points to 16-bit data units, the  first  of
       which  contains  the  parenthesis  number.  In  the 32-bit library, the
       pointer points to 32-bit data units, the first of  which  contains  the
       parenthesis  number.  The  rest of the entry is the corresponding name,
       zero terminated.

       The names are in alphabetical order. If (?| is used to create  multiple
       groups  with  the same number, as described in the section on duplicate
       subpattern numbers in the pcrepattern page, the groups may be given the
       same  name,  but  there is only one entry in the table. Different names
       for groups of the same number are not permitted.  Duplicate  names  for
       subpatterns with different numbers are permitted, but only if PCRE_DUP-
       NAMES is set. They appear in the table in the order in which they  were
       found  in  the  pattern.  In  the  absence  of (?| this is the order of
       increasing number; when (?| is used this is not  necessarily  the  case
       because later subpatterns may have lower numbers.

       As  a  simple  example of the name/number table, consider the following
       pattern after compilation by the 8-bit library (assume PCRE_EXTENDED is
       set, so white space - including newlines - is ignored):

         (?<date> (?<year>(\d\d)?\d\d) -
         (?<month>\d\d) - (?<day>\d\d) )

       There  are  four  named subpatterns, so the table has four entries, and
       each entry in the table is eight bytes long. The table is  as  follows,
       with non-printing bytes shows in hexadecimal, and undefined bytes shown
       as ??:

         00 01 d  a  t  e  00 ??
         00 05 d  a  y  00 ?? ??
         00 04 m  o  n  t  h  00
         00 02 y  e  a  r  00 ??

       When writing code to extract data  from  named  subpatterns  using  the
       name-to-number  map,  remember that the length of the entries is likely
       to be different for each compiled pattern.

         PCRE_INFO_OKPARTIAL

       Return 1  if  the  pattern  can  be  used  for  partial  matching  with
       pcre_exec(),  otherwise  0.  The fourth argument should point to an int
       variable. From  release  8.00,  this  always  returns  1,  because  the
       restrictions  that  previously  applied  to  partial matching have been
       lifted. The pcrepartial documentation gives details of  partial  match-
       ing.

         PCRE_INFO_OPTIONS

       Return  a  copy of the options with which the pattern was compiled. The
       fourth argument should point to an unsigned long  int  variable.  These
       option bits are those specified in the call to pcre_compile(), modified
       by any top-level option settings at the start of the pattern itself. In
       other  words,  they are the options that will be in force when matching
       starts. For example, if the pattern /(?im)abc(?-i)d/ is  compiled  with
       the  PCRE_EXTENDED option, the result is PCRE_CASELESS, PCRE_MULTILINE,
       and PCRE_EXTENDED.

       A pattern is automatically anchored by PCRE if  all  of  its  top-level
       alternatives begin with one of the following:

         ^     unless PCRE_MULTILINE is set
         \A    always
         \G    always
         .*    if PCRE_DOTALL is set and there are no back
                 references to the subpattern in which .* appears

       For such patterns, the PCRE_ANCHORED bit is set in the options returned
       by pcre_fullinfo().

         PCRE_INFO_RECURSIONLIMIT

       If the pattern set a recursion limit by including an item of  the  form
       (*LIMIT_RECURSION=nnnn) at the start, the value is returned. The fourth
       argument should point to an unsigned 32-bit integer. If no  such  value
       has   been   set,   the  call  to  pcre_fullinfo()  returns  the  error
       PCRE_ERROR_UNSET.

         PCRE_INFO_SIZE

       Return the size of  the  compiled  pattern  in  bytes  (for  all  three
       libraries). The fourth argument should point to a size_t variable. This
       value does not include the size of the pcre structure that is  returned
       by  pcre_compile().  The  value  that  is  passed  as  the  argument to
       pcre_malloc() when pcre_compile() is getting memory in which  to  place
       the compiled data is the value returned by this option plus the size of
       the pcre structure. Studying a compiled pattern, with or  without  JIT,
       does not alter the value returned by this option.

         PCRE_INFO_STUDYSIZE

       Return  the  size  in bytes (for all three libraries) of the data block
       pointed to by the study_data field in a pcre_extra block. If pcre_extra
       is  NULL, or there is no study data, zero is returned. The fourth argu-
       ment should point to a size_t variable. The study_data field is set  by
       pcre_study() to record information that will speed up matching (see the
       section entitled  "Studying  a  pattern"  above).  The  format  of  the
       study_data  block is private, but its length is made available via this
       option so that it can be saved and  restored  (see  the  pcreprecompile
       documentation for details).

         PCRE_INFO_REQUIREDCHARFLAGS

       Returns  1 if there is a rightmost literal data unit that must exist in
       any matched string, other than at its start. The fourth argument should
       point  to an int variable. If there is no such value, 0 is returned. If
       returning  1,  the  character  value  itself  can  be  retrieved  using
       PCRE_INFO_REQUIREDCHAR.

       For anchored patterns, a last literal value is recorded only if it fol-
       lows something  of  variable  length.  For  example,  for  the  pattern
       /^a\d+z\d+/   the   returned   value   1   (with   "z"   returned  from
       PCRE_INFO_REQUIREDCHAR), but for /^a\dz\d/ the returned value is 0.

         PCRE_INFO_REQUIREDCHAR

       Return the value of the rightmost literal data unit that must exist  in
       any  matched  string, other than at its start, if such a value has been
       recorded. The fourth argument should point to an uint32_t variable.  If
       there is no such value, 0 is returned.


REFERENCE COUNTS

       int pcre_refcount(pcre *code, int adjust);

       The  pcre_refcount()  function is used to maintain a reference count in
       the data block that contains a compiled pattern. It is provided for the
       benefit  of  applications  that  operate  in an object-oriented manner,
       where different parts of the application may be using the same compiled
       pattern, but you want to free the block when they are all done.

       When a pattern is compiled, the reference count field is initialized to
       zero.  It is changed only by calling this function, whose action is  to
       add  the  adjust  value  (which may be positive or negative) to it. The
       yield of the function is the new value. However, the value of the count
       is  constrained to lie between 0 and 65535, inclusive. If the new value
       is outside these limits, it is forced to the appropriate limit value.

       Except when it is zero, the reference count is not correctly  preserved
       if  a  pattern  is  compiled on one host and then transferred to a host
       whose byte-order is different. (This seems a highly unlikely scenario.)


MATCHING A PATTERN: THE TRADITIONAL FUNCTION

       int pcre_exec(const pcre *code, const pcre_extra *extra,
            const char *subject, int length, int startoffset,
            int options, int *ovector, int ovecsize);

       The function pcre_exec() is called to match a subject string against  a
       compiled  pattern, which is passed in the code argument. If the pattern
       was studied, the result of the study should  be  passed  in  the  extra
       argument.  You  can call pcre_exec() with the same code and extra argu-
       ments as many times as you like, in order to  match  different  subject
       strings with the same pattern.

       This  function  is  the  main  matching facility of the library, and it
       operates in a Perl-like manner. For specialist use  there  is  also  an
       alternative  matching function, which is described below in the section
       about the pcre_dfa_exec() function.

       In most applications, the pattern will have been compiled (and  option-
       ally  studied)  in the same process that calls pcre_exec(). However, it
       is possible to save compiled patterns and study data, and then use them
       later  in  different processes, possibly even on different hosts. For a
       discussion about this, see the pcreprecompile documentation.

       Here is an example of a simple call to pcre_exec():

         int rc;
         int ovector[30];
         rc = pcre_exec(
           re,             /* result of pcre_compile() */
           NULL,           /* we didn't study the pattern */
           "some string",  /* the subject string */
           11,             /* the length of the subject string */
           0,              /* start at offset 0 in the subject */
           0,              /* default options */
           ovector,        /* vector of integers for substring information */
           30);            /* number of elements (NOT size in bytes) */

   Extra data for pcre_exec()

       If the extra argument is not NULL, it must point to a  pcre_extra  data
       block.  The pcre_study() function returns such a block (when it doesn't
       return NULL), but you can also create one for yourself, and pass  addi-
       tional  information  in it. The pcre_extra block contains the following
       fields (not necessarily in this order):

         unsigned long int flags;
         void *study_data;
         void *executable_jit;
         unsigned long int match_limit;
         unsigned long int match_limit_recursion;
         void *callout_data;
         const unsigned char *tables;
         unsigned char **mark;

       In the 16-bit version of  this  structure,  the  mark  field  has  type
       "PCRE_UCHAR16 **".

       In  the  32-bit  version  of  this  structure,  the mark field has type
       "PCRE_UCHAR32 **".

       The flags field is used to specify which of the other fields  are  set.
       The flag bits are:

         PCRE_EXTRA_CALLOUT_DATA
         PCRE_EXTRA_EXECUTABLE_JIT
         PCRE_EXTRA_MARK
         PCRE_EXTRA_MATCH_LIMIT
         PCRE_EXTRA_MATCH_LIMIT_RECURSION
         PCRE_EXTRA_STUDY_DATA
         PCRE_EXTRA_TABLES

       Other  flag  bits should be set to zero. The study_data field and some-
       times the executable_jit field are set in the pcre_extra block that  is
       returned  by pcre_study(), together with the appropriate flag bits. You
       should not set these yourself, but you may add to the block by  setting
       other fields and their corresponding flag bits.

       The match_limit field provides a means of preventing PCRE from using up
       a vast amount of resources when running patterns that are not going  to
       match,  but  which  have  a very large number of possibilities in their
       search trees. The classic example is a pattern that uses nested  unlim-
       ited repeats.

       Internally,  pcre_exec() uses a function called match(), which it calls
       repeatedly (sometimes recursively). The limit  set  by  match_limit  is
       imposed  on the number of times this function is called during a match,
       which has the effect of limiting the amount of  backtracking  that  can
       take place. For patterns that are not anchored, the count restarts from
       zero for each position in the subject string.

       When pcre_exec() is called with a pattern that was successfully studied
       with  a  JIT  option, the way that the matching is executed is entirely
       different.  However, there is still the possibility of runaway matching
       that goes on for a very long time, and so the match_limit value is also
       used in this case (but in a different way) to limit how long the match-
       ing can continue.

       The  default  value  for  the  limit can be set when PCRE is built; the
       default default is 10 million, which handles all but the  most  extreme
       cases.  You  can  override  the  default by suppling pcre_exec() with a
       pcre_extra    block    in    which    match_limit    is    set,     and
       PCRE_EXTRA_MATCH_LIMIT  is  set  in  the  flags  field. If the limit is
       exceeded, pcre_exec() returns PCRE_ERROR_MATCHLIMIT.

       A value for the match limit may also be supplied  by  an  item  at  the
       start of a pattern of the form

         (*LIMIT_MATCH=d)

       where  d is a decimal number. However, such a setting is ignored unless
       d is less than the limit set by the caller of  pcre_exec()  or,  if  no
       such limit is set, less than the default.

       The  match_limit_recursion field is similar to match_limit, but instead
       of limiting the total number of times that match() is called, it limits
       the  depth  of  recursion. The recursion depth is a smaller number than
       the total number of calls, because not all calls to match() are  recur-
       sive.  This limit is of use only if it is set smaller than match_limit.

       Limiting  the  recursion  depth limits the amount of machine stack that
       can be used, or, when PCRE has been compiled to use memory on the  heap
       instead  of the stack, the amount of heap memory that can be used. This
       limit is not relevant, and is ignored, when matching is done using  JIT
       compiled code.

       The  default  value  for  match_limit_recursion can be set when PCRE is
       built; the default default  is  the  same  value  as  the  default  for
       match_limit.  You can override the default by suppling pcre_exec() with
       a  pcre_extra  block  in  which  match_limit_recursion  is   set,   and
       PCRE_EXTRA_MATCH_LIMIT_RECURSION  is  set  in  the  flags field. If the
       limit is exceeded, pcre_exec() returns PCRE_ERROR_RECURSIONLIMIT.

       A value for the recursion limit may also be supplied by an item at  the
       start of a pattern of the form

         (*LIMIT_RECURSION=d)

       where  d is a decimal number. However, such a setting is ignored unless
       d is less than the limit set by the caller of  pcre_exec()  or,  if  no
       such limit is set, less than the default.

       The  callout_data  field is used in conjunction with the "callout" fea-
       ture, and is described in the pcrecallout documentation.

       The tables field is provided for use with patterns that have been  pre-
       compiled using custom character tables, saved to disc or elsewhere, and
       then reloaded, because the tables that were used to compile  a  pattern
       are  not saved with it. See the pcreprecompile documentation for a dis-
       cussion of saving compiled patterns for later use. If  NULL  is  passed
       using this mechanism, it forces PCRE's internal tables to be used.

       Warning:  The  tables  that  pcre_exec() uses must be the same as those
       that were used when the pattern was compiled. If this is not the  case,
       the behaviour of pcre_exec() is undefined. Therefore, when a pattern is
       compiled and matched in the same process, this field  should  never  be
       set. In this (the most common) case, the correct table pointer is auto-
       matically passed with  the  compiled  pattern  from  pcre_compile()  to
       pcre_exec().

       If  PCRE_EXTRA_MARK  is  set in the flags field, the mark field must be
       set to point to a suitable variable. If the pattern contains any  back-
       tracking  control verbs such as (*MARK:NAME), and the execution ends up
       with a name to pass back, a pointer to the  name  string  (zero  termi-
       nated)  is  placed  in  the  variable pointed to by the mark field. The
       names are within the compiled pattern; if you wish  to  retain  such  a
       name  you must copy it before freeing the memory of a compiled pattern.
       If there is no name to pass back, the variable pointed to by  the  mark
       field  is  set  to NULL. For details of the backtracking control verbs,
       see the section entitled "Backtracking control" in the pcrepattern doc-
       umentation.

   Option bits for pcre_exec()

       The  unused  bits of the options argument for pcre_exec() must be zero.
       The only bits that may  be  set  are  PCRE_ANCHORED,  PCRE_NEWLINE_xxx,
       PCRE_NOTBOL,    PCRE_NOTEOL,    PCRE_NOTEMPTY,   PCRE_NOTEMPTY_ATSTART,
       PCRE_NO_START_OPTIMIZE,  PCRE_NO_UTF8_CHECK,   PCRE_PARTIAL_HARD,   and
       PCRE_PARTIAL_SOFT.

       If  the  pattern  was successfully studied with one of the just-in-time
       (JIT) compile options, the only supported options for JIT execution are
       PCRE_NO_UTF8_CHECK,     PCRE_NOTBOL,     PCRE_NOTEOL,    PCRE_NOTEMPTY,
       PCRE_NOTEMPTY_ATSTART, PCRE_PARTIAL_HARD, and PCRE_PARTIAL_SOFT. If  an
       unsupported  option  is  used, JIT execution is disabled and the normal
       interpretive code in pcre_exec() is run.

         PCRE_ANCHORED

       The PCRE_ANCHORED option limits pcre_exec() to matching  at  the  first
       matching  position.  If  a  pattern was compiled with PCRE_ANCHORED, or
       turned out to be anchored by virtue of its contents, it cannot be  made
       unachored at matching time.

         PCRE_BSR_ANYCRLF
         PCRE_BSR_UNICODE

       These options (which are mutually exclusive) control what the \R escape
       sequence matches. The choice is either to match only CR, LF,  or  CRLF,
       or  to  match  any Unicode newline sequence. These options override the
       choice that was made or defaulted when the pattern was compiled.

         PCRE_NEWLINE_CR
         PCRE_NEWLINE_LF
         PCRE_NEWLINE_CRLF
         PCRE_NEWLINE_ANYCRLF
         PCRE_NEWLINE_ANY

       These options override  the  newline  definition  that  was  chosen  or
       defaulted  when the pattern was compiled. For details, see the descrip-
       tion of pcre_compile()  above.  During  matching,  the  newline  choice
       affects  the  behaviour  of the dot, circumflex, and dollar metacharac-
       ters. It may also alter the way the match position is advanced after  a
       match failure for an unanchored pattern.

       When  PCRE_NEWLINE_CRLF,  PCRE_NEWLINE_ANYCRLF,  or PCRE_NEWLINE_ANY is
       set, and a match attempt for an unanchored pattern fails when the  cur-
       rent  position  is  at  a  CRLF  sequence,  and the pattern contains no
       explicit matches for  CR  or  LF  characters,  the  match  position  is
       advanced by two characters instead of one, in other words, to after the
       CRLF.

       The above rule is a compromise that makes the most common cases work as
       expected.  For  example,  if  the  pattern  is .+A (and the PCRE_DOTALL
       option is not set), it does not match the string "\r\nA" because, after
       failing  at the start, it skips both the CR and the LF before retrying.
       However, the pattern [\r\n]A does match that string,  because  it  con-
       tains an explicit CR or LF reference, and so advances only by one char-
       acter after the first failure.

       An explicit match for CR of LF is either a literal appearance of one of
       those  characters,  or  one  of the \r or \n escape sequences. Implicit
       matches such as [^X] do not count, nor does \s (which includes  CR  and
       LF in the characters that it matches).

       Notwithstanding  the above, anomalous effects may still occur when CRLF
       is a valid newline sequence and explicit \r or \n escapes appear in the
       pattern.

         PCRE_NOTBOL

       This option specifies that first character of the subject string is not
       the beginning of a line, so the  circumflex  metacharacter  should  not
       match  before it. Setting this without PCRE_MULTILINE (at compile time)
       causes circumflex never to match. This option affects only  the  behav-
       iour of the circumflex metacharacter. It does not affect \A.

         PCRE_NOTEOL

       This option specifies that the end of the subject string is not the end
       of a line, so the dollar metacharacter should not match it nor  (except
       in  multiline mode) a newline immediately before it. Setting this with-
       out PCRE_MULTILINE (at compile time) causes dollar never to match. This
       option  affects only the behaviour of the dollar metacharacter. It does
       not affect \Z or \z.

         PCRE_NOTEMPTY

       An empty string is not considered to be a valid match if this option is
       set.  If  there are alternatives in the pattern, they are tried. If all
       the alternatives match the empty string, the entire  match  fails.  For
       example, if the pattern

         a?b?

       is  applied  to  a  string not beginning with "a" or "b", it matches an
       empty string at the start of the subject. With PCRE_NOTEMPTY set,  this
       match is not valid, so PCRE searches further into the string for occur-
       rences of "a" or "b".

         PCRE_NOTEMPTY_ATSTART

       This is like PCRE_NOTEMPTY, except that an empty string match  that  is
       not  at  the  start  of  the  subject  is  permitted. If the pattern is
       anchored, such a match can occur only if the pattern contains \K.

       Perl    has    no    direct    equivalent    of    PCRE_NOTEMPTY     or
       PCRE_NOTEMPTY_ATSTART,  but  it  does  make a special case of a pattern
       match of the empty string within its split() function, and  when  using
       the  /g  modifier.  It  is  possible  to emulate Perl's behaviour after
       matching a null string by first trying the match again at the same off-
       set  with  PCRE_NOTEMPTY_ATSTART  and  PCRE_ANCHORED,  and then if that
       fails, by advancing the starting offset (see below) and trying an ordi-
       nary  match  again. There is some code that demonstrates how to do this
       in the pcredemo sample program. In the most general case, you  have  to
       check  to  see  if the newline convention recognizes CRLF as a newline,
       and if so, and the current character is CR followed by LF, advance  the
       starting offset by two characters instead of one.

         PCRE_NO_START_OPTIMIZE

       There  are a number of optimizations that pcre_exec() uses at the start
       of a match, in order to speed up the process. For  example,  if  it  is
       known that an unanchored match must start with a specific character, it
       searches the subject for that character, and fails  immediately  if  it
       cannot  find  it,  without actually running the main matching function.
       This means that a special item such as (*COMMIT) at the start of a pat-
       tern  is  not  considered until after a suitable starting point for the
       match has been found. Also, when callouts or (*MARK) items are in  use,
       these "start-up" optimizations can cause them to be skipped if the pat-
       tern is never actually used. The start-up optimizations are in effect a
       pre-scan of the subject that takes place before the pattern is run.

       The  PCRE_NO_START_OPTIMIZE option disables the start-up optimizations,
       possibly causing performance to suffer,  but  ensuring  that  in  cases
       where  the  result is "no match", the callouts do occur, and that items
       such as (*COMMIT) and (*MARK) are considered at every possible starting
       position  in  the  subject  string. If PCRE_NO_START_OPTIMIZE is set at
       compile time,  it  cannot  be  unset  at  matching  time.  The  use  of
       PCRE_NO_START_OPTIMIZE  at  matching  time  (that  is,  passing  it  to
       pcre_exec()) disables JIT execution; in  this  situation,  matching  is
       always done using interpretively.

       Setting  PCRE_NO_START_OPTIMIZE  can  change  the outcome of a matching
       operation.  Consider the pattern

         (*COMMIT)ABC

       When this is compiled, PCRE records the fact that a  match  must  start
       with  the  character  "A".  Suppose the subject string is "DEFABC". The
       start-up optimization scans along the subject, finds "A" and  runs  the
       first  match attempt from there. The (*COMMIT) item means that the pat-
       tern must match the current starting position, which in this  case,  it
       does.  However,  if  the  same match is run with PCRE_NO_START_OPTIMIZE
       set, the initial scan along the subject string  does  not  happen.  The
       first  match  attempt  is  run  starting  from "D" and when this fails,
       (*COMMIT) prevents any further matches  being  tried,  so  the  overall
       result  is  "no  match". If the pattern is studied, more start-up opti-
       mizations may be used. For example, a minimum length  for  the  subject
       may be recorded. Consider the pattern

         (*MARK:A)(X|Y)

       The  minimum  length  for  a  match is one character. If the subject is
       "ABC", there will be attempts to  match  "ABC",  "BC",  "C",  and  then
       finally  an empty string.  If the pattern is studied, the final attempt
       does not take place, because PCRE knows that the subject is too  short,
       and  so  the  (*MARK) is never encountered.  In this case, studying the
       pattern does not affect the overall match result, which  is  still  "no
       match", but it does affect the auxiliary information that is returned.

         PCRE_NO_UTF8_CHECK

       When PCRE_UTF8 is set at compile time, the validity of the subject as a
       UTF-8 string is automatically checked when pcre_exec() is  subsequently
       called.  The entire string is checked before any other processing takes
       place. The value of startoffset is  also  checked  to  ensure  that  it
       points  to  the start of a UTF-8 character. There is a discussion about
       the validity of UTF-8 strings in the pcreunicode page.  If  an  invalid
       sequence   of   bytes   is   found,   pcre_exec()   returns  the  error
       PCRE_ERROR_BADUTF8 or, if PCRE_PARTIAL_HARD is set and the problem is a
       truncated character at the end of the subject, PCRE_ERROR_SHORTUTF8. In
       both cases, information about the precise nature of the error may  also
       be  returned (see the descriptions of these errors in the section enti-
       tled Error return values from pcre_exec() below).  If startoffset  con-
       tains a value that does not point to the start of a UTF-8 character (or
       to the end of the subject), PCRE_ERROR_BADUTF8_OFFSET is returned.

       If you already know that your subject is valid, and you  want  to  skip
       these    checks    for   performance   reasons,   you   can   set   the
       PCRE_NO_UTF8_CHECK option when calling pcre_exec(). You might  want  to
       do  this  for the second and subsequent calls to pcre_exec() if you are
       making repeated calls to find all  the  matches  in  a  single  subject
       string.  However,  you  should  be  sure  that the value of startoffset
       points to the start of a character (or the end of  the  subject).  When
       PCRE_NO_UTF8_CHECK is set, the effect of passing an invalid string as a
       subject or an invalid value of startoffset is undefined.  Your  program
       may crash or loop.

         PCRE_PARTIAL_HARD
         PCRE_PARTIAL_SOFT

       These  options turn on the partial matching feature. For backwards com-
       patibility, PCRE_PARTIAL is a synonym for PCRE_PARTIAL_SOFT. A  partial
       match  occurs if the end of the subject string is reached successfully,
       but there are not enough subject characters to complete the  match.  If
       this happens when PCRE_PARTIAL_SOFT (but not PCRE_PARTIAL_HARD) is set,
       matching continues by testing any remaining alternatives.  Only  if  no
       complete  match  can be found is PCRE_ERROR_PARTIAL returned instead of
       PCRE_ERROR_NOMATCH. In other words,  PCRE_PARTIAL_SOFT  says  that  the
       caller  is  prepared to handle a partial match, but only if no complete
       match can be found.

       If PCRE_PARTIAL_HARD is set, it overrides  PCRE_PARTIAL_SOFT.  In  this
       case,  if  a  partial  match  is found, pcre_exec() immediately returns
       PCRE_ERROR_PARTIAL, without  considering  any  other  alternatives.  In
       other  words, when PCRE_PARTIAL_HARD is set, a partial match is consid-
       ered to be more important that an alternative complete match.

       In both cases, the portion of the string that was  inspected  when  the
       partial match was found is set as the first matching string. There is a
       more detailed discussion of partial and  multi-segment  matching,  with
       examples, in the pcrepartial documentation.

   The string to be matched by pcre_exec()

       The  subject string is passed to pcre_exec() as a pointer in subject, a
       length in length, and a starting offset in startoffset. The  units  for
       length  and  startoffset  are  bytes for the 8-bit library, 16-bit data
       items for the 16-bit library, and 32-bit  data  items  for  the  32-bit
       library.

       If  startoffset  is negative or greater than the length of the subject,
       pcre_exec() returns PCRE_ERROR_BADOFFSET. When the starting  offset  is
       zero,  the  search  for a match starts at the beginning of the subject,
       and this is by far the most common case. In UTF-8 or UTF-16  mode,  the
       offset  must  point to the start of a character, or the end of the sub-
       ject (in UTF-32 mode, one data unit equals one character, so  all  off-
       sets  are  valid).  Unlike  the pattern string, the subject may contain
       binary zeroes.

       A non-zero starting offset is useful when searching for  another  match
       in  the same subject by calling pcre_exec() again after a previous suc-
       cess.  Setting startoffset differs from just passing over  a  shortened
       string  and  setting  PCRE_NOTBOL  in the case of a pattern that begins
       with any kind of lookbehind. For example, consider the pattern

         \Biss\B

       which finds occurrences of "iss" in the middle of  words.  (\B  matches
       only  if  the  current position in the subject is not a word boundary.)
       When applied to the string "Mississipi" the first call  to  pcre_exec()
       finds  the  first  occurrence. If pcre_exec() is called again with just
       the remainder of the subject,  namely  "issipi",  it  does  not  match,
       because \B is always false at the start of the subject, which is deemed
       to be a word boundary. However, if pcre_exec()  is  passed  the  entire
       string again, but with startoffset set to 4, it finds the second occur-
       rence of "iss" because it is able to look behind the starting point  to
       discover that it is preceded by a letter.

       Finding  all  the  matches  in a subject is tricky when the pattern can
       match an empty string. It is possible to emulate Perl's /g behaviour by
       first   trying   the   match   again  at  the  same  offset,  with  the
       PCRE_NOTEMPTY_ATSTART and  PCRE_ANCHORED  options,  and  then  if  that
       fails,  advancing  the  starting  offset  and  trying an ordinary match
       again. There is some code that demonstrates how to do this in the pcre-
       demo sample program. In the most general case, you have to check to see
       if the newline convention recognizes CRLF as a newline, and if so,  and
       the current character is CR followed by LF, advance the starting offset
       by two characters instead of one.

       If a non-zero starting offset is passed when the pattern  is  anchored,
       one attempt to match at the given offset is made. This can only succeed
       if the pattern does not require the match to be at  the  start  of  the
       subject.

   How pcre_exec() returns captured substrings

       In  general, a pattern matches a certain portion of the subject, and in
       addition, further substrings from the subject  may  be  picked  out  by
       parts  of  the  pattern.  Following the usage in Jeffrey Friedl's book,
       this is called "capturing" in what follows, and the  phrase  "capturing
       subpattern"  is  used for a fragment of a pattern that picks out a sub-
       string. PCRE supports several other kinds of  parenthesized  subpattern
       that do not cause substrings to be captured.

       Captured substrings are returned to the caller via a vector of integers
       whose address is passed in ovector. The number of elements in the  vec-
       tor  is  passed in ovecsize, which must be a non-negative number. Note:
       this argument is NOT the size of ovector in bytes.

       The first two-thirds of the vector is used to pass back  captured  sub-
       strings,  each  substring using a pair of integers. The remaining third
       of the vector is used as workspace by pcre_exec() while  matching  cap-
       turing  subpatterns, and is not available for passing back information.
       The number passed in ovecsize should always be a multiple of three.  If
       it is not, it is rounded down.

       When  a  match  is successful, information about captured substrings is
       returned in pairs of integers, starting at the  beginning  of  ovector,
       and  continuing  up  to two-thirds of its length at the most. The first
       element of each pair is set to the offset of the first character  in  a
       substring,  and  the second is set to the offset of the first character
       after the end of a substring. These values are always  data  unit  off-
       sets,  even  in  UTF  mode. They are byte offsets in the 8-bit library,
       16-bit data item offsets in the 16-bit library, and  32-bit  data  item
       offsets in the 32-bit library. Note: they are not character counts.

       The  first  pair  of  integers, ovector[0] and ovector[1], identify the
       portion of the subject string matched by the entire pattern.  The  next
       pair  is  used for the first capturing subpattern, and so on. The value
       returned by pcre_exec() is one more than the highest numbered pair that
       has  been  set.  For example, if two substrings have been captured, the
       returned value is 3. If there are no capturing subpatterns, the  return
       value from a successful match is 1, indicating that just the first pair
       of offsets has been set.

       If a capturing subpattern is matched repeatedly, it is the last portion
       of the string that it matched that is returned.

       If  the vector is too small to hold all the captured substring offsets,
       it is used as far as possible (up to two-thirds of its length), and the
       function  returns a value of zero. If neither the actual string matched
       nor any captured substrings are of interest, pcre_exec() may be  called
       with  ovector passed as NULL and ovecsize as zero. However, if the pat-
       tern contains back references and the ovector  is  not  big  enough  to
       remember  the related substrings, PCRE has to get additional memory for
       use during matching. Thus it is usually advisable to supply an  ovector
       of reasonable size.

       There  are  some  cases where zero is returned (indicating vector over-
       flow) when in fact the vector is exactly the right size for  the  final
       match. For example, consider the pattern

         (a)(?:(b)c|bd)

       If  a  vector of 6 elements (allowing for only 1 captured substring) is
       given with subject string "abd", pcre_exec() will try to set the second
       captured string, thereby recording a vector overflow, before failing to
       match "c" and backing up  to  try  the  second  alternative.  The  zero
       return,  however,  does  correctly  indicate that the maximum number of
       slots (namely 2) have been filled. In similar cases where there is tem-
       porary  overflow,  but  the final number of used slots is actually less
       than the maximum, a non-zero value is returned.

       The pcre_fullinfo() function can be used to find out how many capturing
       subpatterns  there  are  in  a  compiled pattern. The smallest size for
       ovector that will allow for n captured substrings, in addition  to  the
       offsets of the substring matched by the whole pattern, is (n+1)*3.

       It  is  possible for capturing subpattern number n+1 to match some part
       of the subject when subpattern n has not been used at all. For example,
       if  the  string  "abc"  is  matched against the pattern (a|(z))(bc) the
       return from the function is 4, and subpatterns 1 and 3 are matched, but
       2  is  not.  When  this happens, both values in the offset pairs corre-
       sponding to unused subpatterns are set to -1.

       Offset values that correspond to unused subpatterns at the end  of  the
       expression  are  also  set  to  -1. For example, if the string "abc" is
       matched against the pattern (abc)(x(yz)?)? subpatterns 2 and 3 are  not
       matched.  The  return  from the function is 2, because the highest used
       capturing subpattern number is 1, and the offsets for  for  the  second
       and  third  capturing subpatterns (assuming the vector is large enough,
       of course) are set to -1.

       Note: Elements in the first two-thirds of ovector that  do  not  corre-
       spond  to  capturing parentheses in the pattern are never changed. That
       is, if a pattern contains n capturing parentheses, no more  than  ovec-
       tor[0]  to ovector[2n+1] are set by pcre_exec(). The other elements (in
       the first two-thirds) retain whatever values they previously had.

       Some convenience functions are provided  for  extracting  the  captured
       substrings as separate strings. These are described below.

   Error return values from pcre_exec()

       If  pcre_exec()  fails, it returns a negative number. The following are
       defined in the header file:

         PCRE_ERROR_NOMATCH        (-1)

       The subject string did not match the pattern.

         PCRE_ERROR_NULL           (-2)

       Either code or subject was passed as NULL,  or  ovector  was  NULL  and
       ovecsize was not zero.

         PCRE_ERROR_BADOPTION      (-3)

       An unrecognized bit was set in the options argument.

         PCRE_ERROR_BADMAGIC       (-4)

       PCRE  stores a 4-byte "magic number" at the start of the compiled code,
       to catch the case when it is passed a junk pointer and to detect when a
       pattern that was compiled in an environment of one endianness is run in
       an environment with the other endianness. This is the error  that  PCRE
       gives when the magic number is not present.

         PCRE_ERROR_UNKNOWN_OPCODE (-5)

       While running the pattern match, an unknown item was encountered in the
       compiled pattern. This error could be caused by a bug  in  PCRE  or  by
       overwriting of the compiled pattern.

         PCRE_ERROR_NOMEMORY       (-6)

       If  a  pattern contains back references, but the ovector that is passed
       to pcre_exec() is not big enough to remember the referenced substrings,
       PCRE  gets  a  block of memory at the start of matching to use for this
       purpose. If the call via pcre_malloc() fails, this error is given.  The
       memory is automatically freed at the end of matching.

       This  error  is also given if pcre_stack_malloc() fails in pcre_exec().
       This can happen only when PCRE has been compiled with  --disable-stack-
       for-recursion.

         PCRE_ERROR_NOSUBSTRING    (-7)

       This  error is used by the pcre_copy_substring(), pcre_get_substring(),
       and  pcre_get_substring_list()  functions  (see  below).  It  is  never
       returned by pcre_exec().

         PCRE_ERROR_MATCHLIMIT     (-8)

       The  backtracking  limit,  as  specified  by the match_limit field in a
       pcre_extra structure (or defaulted) was reached.  See  the  description
       above.

         PCRE_ERROR_CALLOUT        (-9)

       This error is never generated by pcre_exec() itself. It is provided for
       use by callout functions that want to yield a distinctive  error  code.
       See the pcrecallout documentation for details.

         PCRE_ERROR_BADUTF8        (-10)

       A  string  that contains an invalid UTF-8 byte sequence was passed as a
       subject, and the PCRE_NO_UTF8_CHECK option was not set. If the size  of
       the  output  vector  (ovecsize)  is  at least 2, the byte offset to the
       start of the the invalid UTF-8 character is placed in  the  first  ele-
       ment,  and  a  reason  code is placed in the second element. The reason
       codes are listed in the following section.  For backward compatibility,
       if  PCRE_PARTIAL_HARD is set and the problem is a truncated UTF-8 char-
       acter  at  the  end  of  the   subject   (reason   codes   1   to   5),
       PCRE_ERROR_SHORTUTF8 is returned instead of PCRE_ERROR_BADUTF8.

         PCRE_ERROR_BADUTF8_OFFSET (-11)

       The  UTF-8  byte  sequence that was passed as a subject was checked and
       found to be valid (the PCRE_NO_UTF8_CHECK option was not set), but  the
       value  of startoffset did not point to the beginning of a UTF-8 charac-
       ter or the end of the subject.

         PCRE_ERROR_PARTIAL        (-12)

       The subject string did not match, but it did match partially.  See  the
       pcrepartial documentation for details of partial matching.

         PCRE_ERROR_BADPARTIAL     (-13)

       This  code  is  no  longer  in  use.  It was formerly returned when the
       PCRE_PARTIAL option was used with a compiled pattern  containing  items
       that  were  not  supported  for  partial  matching.  From  release 8.00
       onwards, there are no restrictions on partial matching.

         PCRE_ERROR_INTERNAL       (-14)

       An unexpected internal error has occurred. This error could  be  caused
       by a bug in PCRE or by overwriting of the compiled pattern.

         PCRE_ERROR_BADCOUNT       (-15)

       This error is given if the value of the ovecsize argument is negative.

         PCRE_ERROR_RECURSIONLIMIT (-21)

       The internal recursion limit, as specified by the match_limit_recursion
       field in a pcre_extra structure (or defaulted)  was  reached.  See  the
       description above.

         PCRE_ERROR_BADNEWLINE     (-23)

       An invalid combination of PCRE_NEWLINE_xxx options was given.

         PCRE_ERROR_BADOFFSET      (-24)

       The value of startoffset was negative or greater than the length of the
       subject, that is, the value in length.

         PCRE_ERROR_SHORTUTF8      (-25)

       This error is returned instead of PCRE_ERROR_BADUTF8 when  the  subject
       string  ends with a truncated UTF-8 character and the PCRE_PARTIAL_HARD
       option is set.  Information  about  the  failure  is  returned  as  for
       PCRE_ERROR_BADUTF8.  It  is in fact sufficient to detect this case, but
       this special error code for PCRE_PARTIAL_HARD precedes the  implementa-
       tion  of returned information; it is retained for backwards compatibil-
       ity.

         PCRE_ERROR_RECURSELOOP    (-26)

       This error is returned when pcre_exec() detects a recursion loop within
       the  pattern. Specifically, it means that either the whole pattern or a
       subpattern has been called recursively for the second time at the  same
       position in the subject string. Some simple patterns that might do this
       are detected and faulted at compile time, but more  complicated  cases,
       in particular mutual recursions between two different subpatterns, can-
       not be detected until run time.

         PCRE_ERROR_JIT_STACKLIMIT (-27)

       This error is returned when a pattern  that  was  successfully  studied
       using  a  JIT compile option is being matched, but the memory available
       for the just-in-time processing stack is  not  large  enough.  See  the
       pcrejit documentation for more details.

         PCRE_ERROR_BADMODE        (-28)

       This error is given if a pattern that was compiled by the 8-bit library
       is passed to a 16-bit or 32-bit library function, or vice versa.

         PCRE_ERROR_BADENDIANNESS  (-29)

       This error is given if  a  pattern  that  was  compiled  and  saved  is
       reloaded  on  a  host  with  different endianness. The utility function
       pcre_pattern_to_host_byte_order() can be used to convert such a pattern
       so that it runs on the new host.

         PCRE_ERROR_JIT_BADOPTION

       This  error  is  returned  when a pattern that was successfully studied
       using a JIT compile option is being  matched,  but  the  matching  mode
       (partial  or complete match) does not correspond to any JIT compilation
       mode. When the JIT fast path function is used, this error may  be  also
       given  for  invalid  options.  See  the  pcrejit documentation for more
       details.

         PCRE_ERROR_BADLENGTH      (-32)

       This error is given if pcre_exec() is called with a negative value  for
       the length argument.

       Error numbers -16 to -20, -22, and 30 are not used by pcre_exec().

   Reason codes for invalid UTF-8 strings

       This  section  applies  only  to  the  8-bit library. The corresponding
       information for the 16-bit and 32-bit libraries is given in the  pcre16
       and pcre32 pages.

       When pcre_exec() returns either PCRE_ERROR_BADUTF8 or PCRE_ERROR_SHORT-
       UTF8, and the size of the output vector (ovecsize) is at least  2,  the
       offset  of  the  start  of the invalid UTF-8 character is placed in the
       first output vector element (ovector[0]) and a reason code is placed in
       the  second  element  (ovector[1]). The reason codes are given names in
       the pcre.h header file:

         PCRE_UTF8_ERR1
         PCRE_UTF8_ERR2
         PCRE_UTF8_ERR3
         PCRE_UTF8_ERR4
         PCRE_UTF8_ERR5

       The string ends with a truncated UTF-8 character;  the  code  specifies
       how  many bytes are missing (1 to 5). Although RFC 3629 restricts UTF-8
       characters to be no longer than 4 bytes, the  encoding  scheme  (origi-
       nally  defined  by  RFC  2279)  allows  for  up to 6 bytes, and this is
       checked first; hence the possibility of 4 or 5 missing bytes.

         PCRE_UTF8_ERR6
         PCRE_UTF8_ERR7
         PCRE_UTF8_ERR8
         PCRE_UTF8_ERR9
         PCRE_UTF8_ERR10

       The two most significant bits of the 2nd, 3rd, 4th, 5th, or 6th byte of
       the  character  do  not have the binary value 0b10 (that is, either the
       most significant bit is 0, or the next bit is 1).

         PCRE_UTF8_ERR11
         PCRE_UTF8_ERR12

       A character that is valid by the RFC 2279 rules is either 5 or 6  bytes
       long; these code points are excluded by RFC 3629.

         PCRE_UTF8_ERR13

       A  4-byte character has a value greater than 0x10fff; these code points
       are excluded by RFC 3629.

         PCRE_UTF8_ERR14

       A 3-byte character has a value in the  range  0xd800  to  0xdfff;  this
       range  of code points are reserved by RFC 3629 for use with UTF-16, and
       so are excluded from UTF-8.

         PCRE_UTF8_ERR15
         PCRE_UTF8_ERR16
         PCRE_UTF8_ERR17
         PCRE_UTF8_ERR18
         PCRE_UTF8_ERR19

       A 2-, 3-, 4-, 5-, or 6-byte character is "overlong", that is, it  codes
       for  a  value that can be represented by fewer bytes, which is invalid.
       For example, the two bytes 0xc0, 0xae give the value 0x2e,  whose  cor-
       rect coding uses just one byte.

         PCRE_UTF8_ERR20

       The two most significant bits of the first byte of a character have the
       binary value 0b10 (that is, the most significant bit is 1 and the  sec-
       ond  is  0). Such a byte can only validly occur as the second or subse-
       quent byte of a multi-byte character.

         PCRE_UTF8_ERR21

       The first byte of a character has the value 0xfe or 0xff. These  values
       can never occur in a valid UTF-8 string.

         PCRE_UTF8_ERR22

       This  error  code  was  formerly  used when the presence of a so-called
       "non-character" caused an error. Unicode corrigendum #9 makes it  clear
       that  such  characters should not cause a string to be rejected, and so
       this code is no longer in use and is never returned.


EXTRACTING CAPTURED SUBSTRINGS BY NUMBER

       int pcre_copy_substring(const char *subject, int *ovector,
            int stringcount, int stringnumber, char *buffer,
            int buffersize);

       int pcre_get_substring(const char *subject, int *ovector,
            int stringcount, int stringnumber,
            const char **stringptr);

       int pcre_get_substring_list(const char *subject,
            int *ovector, int stringcount, const char ***listptr);

       Captured substrings can be  accessed  directly  by  using  the  offsets
       returned  by  pcre_exec()  in  ovector.  For convenience, the functions
       pcre_copy_substring(),    pcre_get_substring(),    and    pcre_get_sub-
       string_list()  are  provided for extracting captured substrings as new,
       separate, zero-terminated strings. These functions identify  substrings
       by  number.  The  next section describes functions for extracting named
       substrings.

       A substring that contains a binary zero is correctly extracted and  has
       a  further zero added on the end, but the result is not, of course, a C
       string.  However, you can process such a string  by  referring  to  the
       length  that  is  returned  by  pcre_copy_substring() and pcre_get_sub-
       string().  Unfortunately, the interface to pcre_get_substring_list() is
       not  adequate for handling strings containing binary zeros, because the
       end of the final string is not independently indicated.

       The first three arguments are the same for all  three  of  these  func-
       tions:  subject  is  the subject string that has just been successfully
       matched, ovector is a pointer to the vector of integer offsets that was
       passed to pcre_exec(), and stringcount is the number of substrings that
       were captured by the match, including the substring  that  matched  the
       entire regular expression. This is the value returned by pcre_exec() if
       it is greater than zero. If pcre_exec() returned zero, indicating  that
       it  ran out of space in ovector, the value passed as stringcount should
       be the number of elements in the vector divided by three.

       The functions pcre_copy_substring() and pcre_get_substring() extract  a
       single  substring,  whose  number  is given as stringnumber. A value of
       zero extracts the substring that matched the  entire  pattern,  whereas
       higher  values  extract  the  captured  substrings.  For pcre_copy_sub-
       string(), the string is placed in buffer,  whose  length  is  given  by
       buffersize,  while  for  pcre_get_substring()  a new block of memory is
       obtained via pcre_malloc, and its address is  returned  via  stringptr.
       The  yield  of  the function is the length of the string, not including
       the terminating zero, or one of these error codes:

         PCRE_ERROR_NOMEMORY       (-6)

       The buffer was too small for pcre_copy_substring(), or the  attempt  to
       get memory failed for pcre_get_substring().

         PCRE_ERROR_NOSUBSTRING    (-7)

       There is no substring whose number is stringnumber.

       The  pcre_get_substring_list()  function  extracts  all  available sub-
       strings and builds a list of pointers to them. All this is  done  in  a
       single block of memory that is obtained via pcre_malloc. The address of
       the memory block is returned via listptr, which is also  the  start  of
       the  list  of  string pointers. The end of the list is marked by a NULL
       pointer. The yield of the function is zero if all  went  well,  or  the
       error code

         PCRE_ERROR_NOMEMORY       (-6)

       if the attempt to get the memory block failed.

       When  any of these functions encounter a substring that is unset, which
       can happen when capturing subpattern number n+1 matches  some  part  of
       the  subject, but subpattern n has not been used at all, they return an
       empty string. This can be distinguished from a genuine zero-length sub-
       string  by inspecting the appropriate offset in ovector, which is nega-
       tive for unset substrings.

       The two convenience functions pcre_free_substring() and  pcre_free_sub-
       string_list()  can  be  used  to free the memory returned by a previous
       call  of  pcre_get_substring()  or  pcre_get_substring_list(),  respec-
       tively.  They  do  nothing  more  than  call the function pointed to by
       pcre_free, which of course could be called directly from a  C  program.
       However,  PCRE is used in some situations where it is linked via a spe-
       cial  interface  to  another  programming  language  that  cannot   use
       pcre_free  directly;  it is for these cases that the functions are pro-
       vided.


EXTRACTING CAPTURED SUBSTRINGS BY NAME

       int pcre_get_stringnumber(const pcre *code,
            const char *name);

       int pcre_copy_named_substring(const pcre *code,
            const char *subject, int *ovector,
            int stringcount, const char *stringname,
            char *buffer, int buffersize);

       int pcre_get_named_substring(const pcre *code,
            const char *subject, int *ovector,
            int stringcount, const char *stringname,
            const char **stringptr);

       To extract a substring by name, you first have to find associated  num-
       ber.  For example, for this pattern

         (a+)b(?<xxx>\d+)...

       the number of the subpattern called "xxx" is 2. If the name is known to
       be unique (PCRE_DUPNAMES was not set), you can find the number from the
       name by calling pcre_get_stringnumber(). The first argument is the com-
       piled pattern, and the second is the name. The yield of the function is
       the  subpattern  number,  or PCRE_ERROR_NOSUBSTRING (-7) if there is no
       subpattern of that name.

       Given the number, you can extract the substring directly, or use one of
       the functions described in the previous section. For convenience, there
       are also two functions that do the whole job.

       Most   of   the   arguments    of    pcre_copy_named_substring()    and
       pcre_get_named_substring()  are  the  same  as  those for the similarly
       named functions that extract by number. As these are described  in  the
       previous  section,  they  are not re-described here. There are just two
       differences:

       First, instead of a substring number, a substring name is  given.  Sec-
       ond, there is an extra argument, given at the start, which is a pointer
       to the compiled pattern. This is needed in order to gain access to  the
       name-to-number translation table.

       These  functions call pcre_get_stringnumber(), and if it succeeds, they
       then call pcre_copy_substring() or pcre_get_substring(),  as  appropri-
       ate.  NOTE:  If PCRE_DUPNAMES is set and there are duplicate names, the
       behaviour may not be what you want (see the next section).

       Warning: If the pattern uses the (?| feature to set up multiple subpat-
       terns  with  the  same number, as described in the section on duplicate
       subpattern numbers in the pcrepattern page, you  cannot  use  names  to
       distinguish  the  different subpatterns, because names are not included
       in the compiled code. The matching process uses only numbers. For  this
       reason,  the  use of different names for subpatterns of the same number
       causes an error at compile time.


DUPLICATE SUBPATTERN NAMES

       int pcre_get_stringtable_entries(const pcre *code,
            const char *name, char **first, char **last);

       When a pattern is compiled with the  PCRE_DUPNAMES  option,  names  for
       subpatterns  are not required to be unique. (Duplicate names are always
       allowed for subpatterns with the same number, created by using the  (?|
       feature.  Indeed,  if  such subpatterns are named, they are required to
       use the same names.)

       Normally, patterns with duplicate names are such that in any one match,
       only  one of the named subpatterns participates. An example is shown in
       the pcrepattern documentation.

       When   duplicates   are   present,   pcre_copy_named_substring()    and
       pcre_get_named_substring()  return the first substring corresponding to
       the given name that is set. If  none  are  set,  PCRE_ERROR_NOSUBSTRING
       (-7)  is  returned;  no  data  is returned. The pcre_get_stringnumber()
       function returns one of the numbers that are associated with the  name,
       but it is not defined which it is.

       If  you want to get full details of all captured substrings for a given
       name, you must use  the  pcre_get_stringtable_entries()  function.  The
       first argument is the compiled pattern, and the second is the name. The
       third and fourth are pointers to variables which  are  updated  by  the
       function. After it has run, they point to the first and last entries in
       the name-to-number table  for  the  given  name.  The  function  itself
       returns  the  length  of  each entry, or PCRE_ERROR_NOSUBSTRING (-7) if
       there are none. The format of the table is described above in the  sec-
       tion  entitled  Information about a pattern above.  Given all the rele-
       vant entries for the name, you can extract each of their  numbers,  and
       hence the captured data, if any.


FINDING ALL POSSIBLE MATCHES

       The  traditional  matching  function  uses a similar algorithm to Perl,
       which stops when it finds the first match, starting at a given point in
       the  subject.  If you want to find all possible matches, or the longest
       possible match, consider using the alternative matching  function  (see
       below)  instead.  If you cannot use the alternative function, but still
       need to find all possible matches, you can kludge it up by  making  use
       of the callout facility, which is described in the pcrecallout documen-
       tation.

       What you have to do is to insert a callout right at the end of the pat-
       tern.   When your callout function is called, extract and save the cur-
       rent matched substring. Then return  1,  which  forces  pcre_exec()  to
       backtrack  and  try other alternatives. Ultimately, when it runs out of
       matches, pcre_exec() will yield PCRE_ERROR_NOMATCH.


OBTAINING AN ESTIMATE OF STACK USAGE

       Matching certain patterns using pcre_exec() can use a  lot  of  process
       stack,  which  in  certain  environments can be rather limited in size.
       Some users find it helpful to have an estimate of the amount  of  stack
       that  is  used  by  pcre_exec(),  to help them set recursion limits, as
       described in the pcrestack documentation. The estimate that  is  output
       by pcretest when called with the -m and -C options is obtained by call-
       ing pcre_exec with the values NULL, NULL, NULL, -999, and -999 for  its
       first five arguments.

       Normally,  if  its  first  argument  is  NULL,  pcre_exec() immediately
       returns the negative error code PCRE_ERROR_NULL, but with this  special
       combination  of  arguments,  it returns instead a negative number whose
       absolute value is the approximate stack frame size in bytes.  (A  nega-
       tive  number  is  used so that it is clear that no match has happened.)
       The value is approximate because in  some  cases,  recursive  calls  to
       pcre_exec() occur when there are one or two additional variables on the
       stack.

       If PCRE has been compiled to use the heap  instead  of  the  stack  for
       recursion,  the  value  returned  is  the  size  of  each block that is
       obtained from the heap.


MATCHING A PATTERN: THE ALTERNATIVE FUNCTION

       int pcre_dfa_exec(const pcre *code, const pcre_extra *extra,
            const char *subject, int length, int startoffset,
            int options, int *ovector, int ovecsize,
            int *workspace, int wscount);

       The function pcre_dfa_exec()  is  called  to  match  a  subject  string
       against  a  compiled pattern, using a matching algorithm that scans the
       subject string just once, and does not backtrack.  This  has  different
       characteristics  to  the  normal  algorithm, and is not compatible with
       Perl. Some of the features of PCRE patterns are not  supported.  Never-
       theless,  there are times when this kind of matching can be useful. For
       a discussion of the two matching algorithms, and  a  list  of  features
       that  pcre_dfa_exec() does not support, see the pcrematching documenta-
       tion.

       The arguments for the pcre_dfa_exec() function  are  the  same  as  for
       pcre_exec(), plus two extras. The ovector argument is used in a differ-
       ent way, and this is described below. The other  common  arguments  are
       used  in  the  same way as for pcre_exec(), so their description is not
       repeated here.

       The two additional arguments provide workspace for  the  function.  The
       workspace  vector  should  contain at least 20 elements. It is used for
       keeping  track  of  multiple  paths  through  the  pattern  tree.  More
       workspace  will  be  needed for patterns and subjects where there are a
       lot of potential matches.

       Here is an example of a simple call to pcre_dfa_exec():

         int rc;
         int ovector[10];
         int wspace[20];
         rc = pcre_dfa_exec(
           re,             /* result of pcre_compile() */
           NULL,           /* we didn't study the pattern */
           "some string",  /* the subject string */
           11,             /* the length of the subject string */
           0,              /* start at offset 0 in the subject */
           0,              /* default options */
           ovector,        /* vector of integers for substring information */
           10,             /* number of elements (NOT size in bytes) */
           wspace,         /* working space vector */
           20);            /* number of elements (NOT size in bytes) */

   Option bits for pcre_dfa_exec()

       The unused bits of the options argument  for  pcre_dfa_exec()  must  be
       zero.  The  only  bits  that  may  be  set are PCRE_ANCHORED, PCRE_NEW-
       LINE_xxx,        PCRE_NOTBOL,        PCRE_NOTEOL,        PCRE_NOTEMPTY,
       PCRE_NOTEMPTY_ATSTART,       PCRE_NO_UTF8_CHECK,      PCRE_BSR_ANYCRLF,
       PCRE_BSR_UNICODE, PCRE_NO_START_OPTIMIZE, PCRE_PARTIAL_HARD,  PCRE_PAR-
       TIAL_SOFT,  PCRE_DFA_SHORTEST,  and PCRE_DFA_RESTART.  All but the last
       four of these are  exactly  the  same  as  for  pcre_exec(),  so  their
       description is not repeated here.

         PCRE_PARTIAL_HARD
         PCRE_PARTIAL_SOFT

       These  have the same general effect as they do for pcre_exec(), but the
       details are slightly  different.  When  PCRE_PARTIAL_HARD  is  set  for
       pcre_dfa_exec(),  it  returns PCRE_ERROR_PARTIAL if the end of the sub-
       ject is reached and there is still at least  one  matching  possibility
       that requires additional characters. This happens even if some complete
       matches have also been found. When PCRE_PARTIAL_SOFT is set, the return
       code PCRE_ERROR_NOMATCH is converted into PCRE_ERROR_PARTIAL if the end
       of the subject is reached, there have been  no  complete  matches,  but
       there  is  still  at least one matching possibility. The portion of the
       string that was inspected when the longest partial match was  found  is
       set  as  the  first  matching  string  in  both cases.  There is a more
       detailed discussion of partial and multi-segment matching,  with  exam-
       ples, in the pcrepartial documentation.

         PCRE_DFA_SHORTEST

       Setting  the  PCRE_DFA_SHORTEST option causes the matching algorithm to
       stop as soon as it has found one match. Because of the way the alterna-
       tive  algorithm  works, this is necessarily the shortest possible match
       at the first possible matching point in the subject string.

         PCRE_DFA_RESTART

       When pcre_dfa_exec() returns a partial match, it is possible to call it
       again,  with  additional  subject characters, and have it continue with
       the same match. The PCRE_DFA_RESTART option requests this action;  when
       it  is  set,  the workspace and wscount options must reference the same
       vector as before because data about the match so far is  left  in  them
       after a partial match. There is more discussion of this facility in the
       pcrepartial documentation.

   Successful returns from pcre_dfa_exec()

       When pcre_dfa_exec() succeeds, it may have matched more than  one  sub-
       string in the subject. Note, however, that all the matches from one run
       of the function start at the same point in  the  subject.  The  shorter
       matches  are all initial substrings of the longer matches. For example,
       if the pattern

         <.*>

       is matched against the string

         This is <something> <something else> <something further> no more

       the three matched strings are

         <something>
         <something> <something else>
         <something> <something else> <something further>

       On success, the yield of the function is a number  greater  than  zero,
       which  is  the  number of matched substrings. The substrings themselves
       are returned in ovector. Each string uses two elements;  the  first  is
       the  offset  to  the start, and the second is the offset to the end. In
       fact, all the strings have the same start  offset.  (Space  could  have
       been  saved by giving this only once, but it was decided to retain some
       compatibility with the way pcre_exec() returns data,  even  though  the
       meaning of the strings is different.)

       The strings are returned in reverse order of length; that is, the long-
       est matching string is given first. If there were too many  matches  to
       fit  into ovector, the yield of the function is zero, and the vector is
       filled with the longest matches.  Unlike  pcre_exec(),  pcre_dfa_exec()
       can use the entire ovector for returning matched strings.

       NOTE:  PCRE's  "auto-possessification"  optimization usually applies to
       character repeats at the end of a pattern (as well as internally).  For
       example,  the  pattern "a\d+" is compiled as if it were "a\d++" because
       there is no point even considering the possibility of backtracking into
       the  repeated digits. For DFA matching, this means that only one possi-
       ble match is found. If you really do  want  multiple  matches  in  such
       cases,   either   use   an   ungreedy   repeat  ("a\d+?")  or  set  the
       PCRE_NO_AUTO_POSSESS option when compiling.

   Error returns from pcre_dfa_exec()

       The pcre_dfa_exec() function returns a negative number when  it  fails.
       Many  of  the  errors  are  the  same as for pcre_exec(), and these are
       described above.  There are in addition the following errors  that  are
       specific to pcre_dfa_exec():

         PCRE_ERROR_DFA_UITEM      (-16)

       This  return is given if pcre_dfa_exec() encounters an item in the pat-
       tern that it does not support, for instance, the use of \C  or  a  back
       reference.

         PCRE_ERROR_DFA_UCOND      (-17)

       This  return  is  given  if pcre_dfa_exec() encounters a condition item
       that uses a back reference for the condition, or a test  for  recursion
       in a specific group. These are not supported.

         PCRE_ERROR_DFA_UMLIMIT    (-18)

       This  return  is given if pcre_dfa_exec() is called with an extra block
       that contains a setting of  the  match_limit  or  match_limit_recursion
       fields.  This  is  not  supported (these fields are meaningless for DFA
       matching).

         PCRE_ERROR_DFA_WSSIZE     (-19)

       This return is given if  pcre_dfa_exec()  runs  out  of  space  in  the
       workspace vector.

         PCRE_ERROR_DFA_RECURSE    (-20)

       When  a  recursive subpattern is processed, the matching function calls
       itself recursively, using private vectors for  ovector  and  workspace.
       This  error  is  given  if  the output vector is not large enough. This
       should be extremely rare, as a vector of size 1000 is used.

         PCRE_ERROR_DFA_BADRESTART (-30)

       When pcre_dfa_exec() is called with the PCRE_DFA_RESTART  option,  some
       plausibility  checks  are  made on the contents of the workspace, which
       should contain data about the previous partial match. If any  of  these
       checks fail, this error is given.


SEE ALSO

       pcre16(3),   pcre32(3),  pcrebuild(3),  pcrecallout(3),  pcrecpp(3)(3),
       pcrematching(3), pcrepartial(3), pcreposix(3), pcreprecompile(3), pcre-
       sample(3), pcrestack(3).


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 09 February 2014
       Copyright (c) 1997-2014 University of Cambridge.
------------------------------------------------------------------------------


PCRECALLOUT(3)             Library Functions Manual             PCRECALLOUT(3)



NAME
       PCRE - Perl-compatible regular expressions

SYNOPSIS

       #include <pcre.h>

       int (*pcre_callout)(pcre_callout_block *);

       int (*pcre16_callout)(pcre16_callout_block *);

       int (*pcre32_callout)(pcre32_callout_block *);


DESCRIPTION

       PCRE provides a feature called "callout", which is a means of temporar-
       ily passing control to the caller of PCRE  in  the  middle  of  pattern
       matching.  The  caller of PCRE provides an external function by putting
       its entry point in the global variable pcre_callout (pcre16_callout for
       the 16-bit library, pcre32_callout for the 32-bit library). By default,
       this variable contains NULL, which disables all calling out.

       Within a regular expression, (?C) indicates the  points  at  which  the
       external  function  is  to  be  called. Different callout points can be
       identified by putting a number less than 256 after the  letter  C.  The
       default  value  is  zero.   For  example,  this pattern has two callout
       points:

         (?C1)abc(?C2)def

       If the PCRE_AUTO_CALLOUT option bit is set when a pattern is  compiled,
       PCRE  automatically  inserts callouts, all with number 255, before each
       item in the pattern. For example, if PCRE_AUTO_CALLOUT is used with the
       pattern

         A(\d{2}|--)

       it is processed as if it were

       (?C255)A(?C255)((?C255)\d{2}(?C255)|(?C255)-(?C255)-(?C255))(?C255)

       Notice  that  there  is a callout before and after each parenthesis and
       alternation bar. If the pattern contains a conditional group whose con-
       dition  is  an  assertion, an automatic callout is inserted immediately
       before the condition. Such a callout may also be  inserted  explicitly,
       for example:

         (?(?C9)(?=a)ab|de)

       This  applies only to assertion conditions (because they are themselves
       independent groups).

       Automatic callouts can be used for tracking  the  progress  of  pattern
       matching.   The pcretest program has a pattern qualifier (/C) that sets
       automatic callouts; when it is used, the output indicates how the  pat-
       tern  is  being matched. This is useful information when you are trying
       to optimize the performance of a particular pattern.


MISSING CALLOUTS

       You should be aware that, because of optimizations in the way PCRE com-
       piles and matches patterns, callouts sometimes do not happen exactly as
       you might expect.

       At compile time, PCRE "auto-possessifies" repeated items when it  knows
       that  what follows cannot be part of the repeat. For example, a+[bc] is
       compiled as if it were a++[bc]. The pcretest output when  this  pattern
       is  anchored  and  then  applied  with automatic callouts to the string
       "aaaa" is:

         --->aaaa
          +0 ^        ^
          +1 ^        a+
          +3 ^   ^    [bc]
         No match

       This indicates that when matching [bc] fails, there is no  backtracking
       into  a+  and  therefore the callouts that would be taken for the back-
       tracks do not occur.  You can disable the  auto-possessify  feature  by
       passing PCRE_NO_AUTO_POSSESS to pcre_compile(), or starting the pattern
       with (*NO_AUTO_POSSESS). If this is done  in  pcretest  (using  the  /O
       qualifier), the output changes to this:

         --->aaaa
          +0 ^        ^
          +1 ^        a+
          +3 ^   ^    [bc]
          +3 ^  ^     [bc]
          +3 ^ ^      [bc]
          +3 ^^       [bc]
         No match

       This time, when matching [bc] fails, the matcher backtracks into a+ and
       tries again, repeatedly, until a+ itself fails.

       Other optimizations that provide fast "no match"  results  also  affect
       callouts.  For example, if the pattern is

         ab(?C4)cd

       PCRE knows that any matching string must contain the letter "d". If the
       subject string is "abyz", the lack of "d" means that  matching  doesn't
       ever  start,  and  the  callout is never reached. However, with "abyd",
       though the result is still no match, the callout is obeyed.

       If the pattern is studied, PCRE knows the minimum length of a  matching
       string,  and will immediately give a "no match" return without actually
       running a match if the subject is not long enough, or,  for  unanchored
       patterns, if it has been scanned far enough.

       You  can disable these optimizations by passing the PCRE_NO_START_OPTI-
       MIZE option to the matching function, or by starting the  pattern  with
       (*NO_START_OPT).  This slows down the matching process, but does ensure
       that callouts such as the example above are obeyed.


THE CALLOUT INTERFACE

       During matching, when PCRE reaches a callout point, the external  func-
       tion defined by pcre_callout or pcre[16|32]_callout is called (if it is
       set). This applies to both normal and DFA matching. The  only  argument
       to   the   callout   function   is  a  pointer  to  a  pcre_callout  or
       pcre[16|32]_callout block.  These  structures  contains  the  following
       fields:

         int           version;
         int           callout_number;
         int          *offset_vector;
         const char   *subject;           (8-bit version)
         PCRE_SPTR16   subject;           (16-bit version)
         PCRE_SPTR32   subject;           (32-bit version)
         int           subject_length;
         int           start_match;
         int           current_position;
         int           capture_top;
         int           capture_last;
         void         *callout_data;
         int           pattern_position;
         int           next_item_length;
         const unsigned char *mark;       (8-bit version)
         const PCRE_UCHAR16  *mark;       (16-bit version)
         const PCRE_UCHAR32  *mark;       (32-bit version)

       The  version  field  is an integer containing the version number of the
       block format. The initial version was 0; the current version is 2.  The
       version  number  will  change  again in future if additional fields are
       added, but the intention is never to remove any of the existing fields.

       The callout_number field contains the number of the  callout,  as  com-
       piled  into  the pattern (that is, the number after ?C for manual call-
       outs, and 255 for automatically generated callouts).

       The offset_vector field is a pointer to the vector of offsets that  was
       passed  by  the  caller  to  the matching function. When pcre_exec() or
       pcre[16|32]_exec() is used, the contents can be inspected, in order  to
       extract  substrings  that  have been matched so far, in the same way as
       for extracting substrings after a match  has  completed.  For  the  DFA
       matching functions, this field is not useful.

       The subject and subject_length fields contain copies of the values that
       were passed to the matching function.

       The start_match field normally contains the offset within  the  subject
       at  which  the  current  match  attempt started. However, if the escape
       sequence \K has been encountered, this value is changed to reflect  the
       modified  starting  point.  If the pattern is not anchored, the callout
       function may be called several times from the same point in the pattern
       for different starting points in the subject.

       The  current_position  field  contains the offset within the subject of
       the current match pointer.

       When the pcre_exec() or pcre[16|32]_exec()  is  used,  the  capture_top
       field  contains  one  more than the number of the highest numbered cap-
       tured substring so far. If no substrings have been captured, the  value
       of  capture_top  is one. This is always the case when the DFA functions
       are used, because they do not support captured substrings.

       The capture_last field contains the number of the  most  recently  cap-
       tured  substring. However, when a recursion exits, the value reverts to
       what it was outside the recursion, as do the  values  of  all  captured
       substrings.  If  no  substrings  have  been captured, the value of cap-
       ture_last is -1. This is always the case for  the  DFA  matching  func-
       tions.

       The  callout_data  field  contains a value that is passed to a matching
       function specifically so that it can be passed back in callouts. It  is
       passed  in  the callout_data field of a pcre_extra or pcre[16|32]_extra
       data structure. If no such data was passed, the value  of  callout_data
       in  a  callout  block is NULL. There is a description of the pcre_extra
       structure in the pcreapi documentation.

       The pattern_position field is present from version  1  of  the  callout
       structure. It contains the offset to the next item to be matched in the
       pattern string.

       The next_item_length field is present from version  1  of  the  callout
       structure. It contains the length of the next item to be matched in the
       pattern string. When the callout immediately  precedes  an  alternation
       bar,  a  closing  parenthesis, or the end of the pattern, the length is
       zero. When the callout precedes an opening parenthesis, the  length  is
       that of the entire subpattern.

       The  pattern_position  and next_item_length fields are intended to help
       in distinguishing between different automatic callouts, which all  have
       the same callout number. However, they are set for all callouts.

       The  mark  field is present from version 2 of the callout structure. In
       callouts from pcre_exec() or pcre[16|32]_exec() it contains  a  pointer
       to  the  zero-terminated  name  of  the  most  recently passed (*MARK),
       (*PRUNE), or (*THEN) item in the match, or NULL if no such  items  have
       been  passed.  Instances  of  (*PRUNE) or (*THEN) without a name do not
       obliterate a previous (*MARK). In callouts from the DFA matching  func-
       tions this field always contains NULL.


RETURN VALUES

       The  external callout function returns an integer to PCRE. If the value
       is zero, matching proceeds as normal. If  the  value  is  greater  than
       zero,  matching  fails  at  the current point, but the testing of other
       matching possibilities goes ahead, just as if a lookahead assertion had
       failed.  If  the  value  is less than zero, the match is abandoned, the
       matching function returns the negative value.

       Negative  values  should  normally  be   chosen   from   the   set   of
       PCRE_ERROR_xxx values. In particular, PCRE_ERROR_NOMATCH forces a stan-
       dard "no  match"  failure.   The  error  number  PCRE_ERROR_CALLOUT  is
       reserved  for  use  by callout functions; it will never be used by PCRE
       itself.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 12 November 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCRECOMPAT(3)              Library Functions Manual              PCRECOMPAT(3)



NAME
       PCRE - Perl-compatible regular expressions

DIFFERENCES BETWEEN PCRE AND PERL

       This  document describes the differences in the ways that PCRE and Perl
       handle regular expressions. The differences  described  here  are  with
       respect to Perl versions 5.10 and above.

       1. PCRE has only a subset of Perl's Unicode support. Details of what it
       does have are given in the pcreunicode page.

       2. PCRE allows repeat quantifiers only on parenthesized assertions, but
       they  do  not mean what you might think. For example, (?!a){3} does not
       assert that the next three characters are not "a". It just asserts that
       the next character is not "a" three times (in principle: PCRE optimizes
       this to run the assertion just once). Perl allows repeat quantifiers on
       other assertions such as \b, but these do not seem to have any use.

       3.  Capturing  subpatterns  that occur inside negative lookahead asser-
       tions are counted, but their entries in the offsets  vector  are  never
       set.  Perl sometimes (but not always) sets its numerical variables from
       inside negative assertions.

       4. Though binary zero characters are supported in the  subject  string,
       they are not allowed in a pattern string because it is passed as a nor-
       mal C string, terminated by zero. The escape sequence \0 can be used in
       the pattern to represent a binary zero.

       5.  The  following Perl escape sequences are not supported: \l, \u, \L,
       \U, and \N when followed by a character name or Unicode value.  (\N  on
       its own, matching a non-newline character, is supported.) In fact these
       are implemented by Perl's general string-handling and are not  part  of
       its  pattern  matching engine. If any of these are encountered by PCRE,
       an error is generated by default. However, if the  PCRE_JAVASCRIPT_COM-
       PAT  option  is set, \U and \u are interpreted as JavaScript interprets
       them.

       6. The Perl escape sequences \p, \P, and \X are supported only if  PCRE
       is  built  with Unicode character property support. The properties that
       can be tested with \p and \P are limited to the general category  prop-
       erties  such  as  Lu and Nd, script names such as Greek or Han, and the
       derived properties Any and L&. PCRE does  support  the  Cs  (surrogate)
       property,  which  Perl  does  not; the Perl documentation says "Because
       Perl hides the need for the user to understand the internal representa-
       tion  of Unicode characters, there is no need to implement the somewhat
       messy concept of surrogates."

       7. PCRE does support the \Q...\E escape for quoting substrings. Charac-
       ters  in  between  are  treated as literals. This is slightly different
       from Perl in that $ and @ are  also  handled  as  literals  inside  the
       quotes.  In Perl, they cause variable interpolation (but of course PCRE
       does not have variables). Note the following examples:

           Pattern            PCRE matches      Perl matches

           \Qabc$xyz\E        abc$xyz           abc followed by the
                                                  contents of $xyz
           \Qabc\$xyz\E       abc\$xyz          abc\$xyz
           \Qabc\E\$\Qxyz\E   abc$xyz           abc$xyz

       The \Q...\E sequence is recognized both inside  and  outside  character
       classes.

       8. Fairly obviously, PCRE does not support the (?{code}) and (??{code})
       constructions. However, there is support for recursive  patterns.  This
       is  not  available  in Perl 5.8, but it is in Perl 5.10. Also, the PCRE
       "callout" feature allows an external function to be called during  pat-
       tern matching. See the pcrecallout documentation for details.

       9.  Subpatterns  that  are called as subroutines (whether or not recur-
       sively) are always treated as atomic  groups  in  PCRE.  This  is  like
       Python,  but  unlike Perl.  Captured values that are set outside a sub-
       routine call can be reference from inside in PCRE,  but  not  in  Perl.
       There is a discussion that explains these differences in more detail in
       the section on recursion differences from Perl in the pcrepattern page.

       10. If any of the backtracking control verbs are used in  a  subpattern
       that  is  called  as  a  subroutine (whether or not recursively), their
       effect is confined to that subpattern; it does not extend to  the  sur-
       rounding  pattern.  This is not always the case in Perl. In particular,
       if (*THEN) is present in a group that is called as  a  subroutine,  its
       action is limited to that group, even if the group does not contain any
       | characters. Note that such subpatterns are processed as  anchored  at
       the point where they are tested.

       11.  If a pattern contains more than one backtracking control verb, the
       first one that is backtracked onto acts. For example,  in  the  pattern
       A(*COMMIT)B(*PRUNE)C  a  failure in B triggers (*COMMIT), but a failure
       in C triggers (*PRUNE). Perl's behaviour is more complex; in many cases
       it is the same as PCRE, but there are examples where it differs.

       12.  Most  backtracking  verbs in assertions have their normal actions.
       They are not confined to the assertion.

       13. There are some differences that are concerned with the settings  of
       captured  strings  when  part  of  a  pattern is repeated. For example,
       matching "aba" against the  pattern  /^(a(b)?)+$/  in  Perl  leaves  $2
       unset, but in PCRE it is set to "b".

       14.  PCRE's handling of duplicate subpattern numbers and duplicate sub-
       pattern names is not as general as Perl's. This is a consequence of the
       fact the PCRE works internally just with numbers, using an external ta-
       ble to translate between numbers and names. In  particular,  a  pattern
       such  as  (?|(?<a>A)|(?<b)B),  where the two capturing parentheses have
       the same number but different names, is not supported,  and  causes  an
       error  at compile time. If it were allowed, it would not be possible to
       distinguish which parentheses matched, because both names map  to  cap-
       turing subpattern number 1. To avoid this confusing situation, an error
       is given at compile time.

       15. Perl recognizes comments in some places that  PCRE  does  not,  for
       example,  between  the  ( and ? at the start of a subpattern. If the /x
       modifier is set, Perl allows white space between ( and ?  (though  cur-
       rent  Perls  warn that this is deprecated) but PCRE never does, even if
       the PCRE_EXTENDED option is set.

       16. Perl, when in warning mode, gives warnings  for  character  classes
       such  as  [A-\d] or [a-[:digit:]]. It then treats the hyphens as liter-
       als. PCRE has no warning features, so it gives an error in these  cases
       because they are almost certainly user mistakes.

       17.  In  PCRE,  the upper/lower case character properties Lu and Ll are
       not affected when case-independent matching is specified. For  example,
       \p{Lu} always matches an upper case letter. I think Perl has changed in
       this respect; in the release at the time of writing (5.16), \p{Lu}  and
       \p{Ll} match all letters, regardless of case, when case independence is
       specified.

       18. PCRE provides some extensions to the Perl regular expression facil-
       ities.   Perl  5.10  includes new features that are not in earlier ver-
       sions of Perl, some of which (such as named parentheses) have  been  in
       PCRE for some time. This list is with respect to Perl 5.10:

       (a)  Although  lookbehind  assertions  in  PCRE must match fixed length
       strings, each alternative branch of a lookbehind assertion can match  a
       different  length  of  string.  Perl requires them all to have the same
       length.

       (b) If PCRE_DOLLAR_ENDONLY is set and PCRE_MULTILINE is not set, the  $
       meta-character matches only at the very end of the string.

       (c) If PCRE_EXTRA is set, a backslash followed by a letter with no spe-
       cial meaning is faulted. Otherwise, like Perl, the backslash is quietly
       ignored.  (Perl can be made to issue a warning.)

       (d)  If  PCRE_UNGREEDY is set, the greediness of the repetition quanti-
       fiers is inverted, that is, by default they are not greedy, but if fol-
       lowed by a question mark they are.

       (e) PCRE_ANCHORED can be used at matching time to force a pattern to be
       tried only at the first matching position in the subject string.

       (f) The PCRE_NOTBOL, PCRE_NOTEOL, PCRE_NOTEMPTY, PCRE_NOTEMPTY_ATSTART,
       and  PCRE_NO_AUTO_CAPTURE  options for pcre_exec() have no Perl equiva-
       lents.

       (g) The \R escape sequence can be restricted to match only CR,  LF,  or
       CRLF by the PCRE_BSR_ANYCRLF option.

       (h) The callout facility is PCRE-specific.

       (i) The partial matching facility is PCRE-specific.

       (j) Patterns compiled by PCRE can be saved and re-used at a later time,
       even on different hosts that have the other endianness.  However,  this
       does not apply to optimized data created by the just-in-time compiler.

       (k)    The    alternative    matching    functions    (pcre_dfa_exec(),
       pcre16_dfa_exec() and pcre32_dfa_exec(),) match in a different way  and
       are not Perl-compatible.

       (l)  PCRE  recognizes some special sequences such as (*CR) at the start
       of a pattern that set overall options that cannot be changed within the
       pattern.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 10 November 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREPATTERN(3)             Library Functions Manual             PCREPATTERN(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE REGULAR EXPRESSION DETAILS

       The  syntax and semantics of the regular expressions that are supported
       by PCRE are described in detail below. There is a quick-reference  syn-
       tax summary in the pcresyntax page. PCRE tries to match Perl syntax and
       semantics as closely as it can. PCRE  also  supports  some  alternative
       regular  expression  syntax (which does not conflict with the Perl syn-
       tax) in order to provide some compatibility with regular expressions in
       Python, .NET, and Oniguruma.

       Perl's  regular expressions are described in its own documentation, and
       regular expressions in general are covered in a number of  books,  some
       of  which  have  copious  examples. Jeffrey Friedl's "Mastering Regular
       Expressions", published by  O'Reilly,  covers  regular  expressions  in
       great  detail.  This  description  of  PCRE's  regular  expressions  is
       intended as reference material.

       This document discusses the patterns that are supported  by  PCRE  when
       one    its    main   matching   functions,   pcre_exec()   (8-bit)   or
       pcre[16|32]_exec() (16- or 32-bit), is used. PCRE also has  alternative
       matching  functions,  pcre_dfa_exec()  and pcre[16|32_dfa_exec(), which
       match using a different algorithm that is not Perl-compatible. Some  of
       the  features  discussed  below  are not available when DFA matching is
       used. The advantages and disadvantages of  the  alternative  functions,
       and  how  they  differ  from the normal functions, are discussed in the
       pcrematching page.


SPECIAL START-OF-PATTERN ITEMS

       A number of options that can be passed to pcre_compile()  can  also  be
       set by special items at the start of a pattern. These are not Perl-com-
       patible, but are provided to make these options accessible  to  pattern
       writers  who are not able to change the program that processes the pat-
       tern. Any number of these items  may  appear,  but  they  must  all  be
       together right at the start of the pattern string, and the letters must
       be in upper case.

   UTF support

       The original operation of PCRE was on strings of  one-byte  characters.
       However,  there  is  now also support for UTF-8 strings in the original
       library, an extra library that supports  16-bit  and  UTF-16  character
       strings,  and a third library that supports 32-bit and UTF-32 character
       strings. To use these features, PCRE must be built to include appropri-
       ate  support. When using UTF strings you must either call the compiling
       function with the PCRE_UTF8, PCRE_UTF16, or PCRE_UTF32 option,  or  the
       pattern must start with one of these special sequences:

         (*UTF8)
         (*UTF16)
         (*UTF32)
         (*UTF)

       (*UTF)  is  a  generic  sequence  that  can  be  used  with  any of the
       libraries.  Starting a pattern with such a sequence  is  equivalent  to
       setting  the  relevant  option.  How setting a UTF mode affects pattern
       matching is mentioned in several places below. There is also a  summary
       of features in the pcreunicode page.

       Some applications that allow their users to supply patterns may wish to
       restrict  them  to  non-UTF  data  for   security   reasons.   If   the
       PCRE_NEVER_UTF  option  is  set  at  compile  time, (*UTF) etc. are not
       allowed, and their appearance causes an error.

   Unicode property support

       Another special sequence that may appear at the start of a  pattern  is
       (*UCP).   This  has  the same effect as setting the PCRE_UCP option: it
       causes sequences such as \d and \w to use Unicode properties to  deter-
       mine character types, instead of recognizing only characters with codes
       less than 128 via a lookup table.

   Disabling auto-possessification

       If a pattern starts with (*NO_AUTO_POSSESS), it has the same effect  as
       setting  the  PCRE_NO_AUTO_POSSESS  option  at compile time. This stops
       PCRE from making quantifiers possessive when what follows cannot  match
       the  repeated item. For example, by default a+b is treated as a++b. For
       more details, see the pcreapi documentation.

   Disabling start-up optimizations

       If a pattern starts with (*NO_START_OPT), it has  the  same  effect  as
       setting the PCRE_NO_START_OPTIMIZE option either at compile or matching
       time. This disables several  optimizations  for  quickly  reaching  "no
       match" results. For more details, see the pcreapi documentation.

   Newline conventions

       PCRE  supports five different conventions for indicating line breaks in
       strings: a single CR (carriage return) character, a  single  LF  (line-
       feed) character, the two-character sequence CRLF, any of the three pre-
       ceding, or any Unicode newline sequence. The pcreapi page  has  further
       discussion  about newlines, and shows how to set the newline convention
       in the options arguments for the compiling and matching functions.

       It is also possible to specify a newline convention by starting a  pat-
       tern string with one of the following five sequences:

         (*CR)        carriage return
         (*LF)        linefeed
         (*CRLF)      carriage return, followed by linefeed
         (*ANYCRLF)   any of the three above
         (*ANY)       all Unicode newline sequences

       These override the default and the options given to the compiling func-
       tion. For example, on a Unix system where LF  is  the  default  newline
       sequence, the pattern

         (*CR)a.b

       changes the convention to CR. That pattern matches "a\nb" because LF is
       no longer a newline. If more than one of these settings is present, the
       last one is used.

       The  newline  convention affects where the circumflex and dollar asser-
       tions are true. It also affects the interpretation of the dot metachar-
       acter when PCRE_DOTALL is not set, and the behaviour of \N. However, it
       does not affect what the \R escape sequence matches. By  default,  this
       is  any Unicode newline sequence, for Perl compatibility. However, this
       can be changed; see the description of \R in the section entitled "New-
       line  sequences"  below.  A change of \R setting can be combined with a
       change of newline convention.

   Setting match and recursion limits

       The caller of pcre_exec() can set a limit on the number  of  times  the
       internal  match() function is called and on the maximum depth of recur-
       sive calls. These facilities are provided to catch runaway matches that
       are provoked by patterns with huge matching trees (a typical example is
       a pattern with nested unlimited repeats) and to avoid  running  out  of
       system  stack  by  too  much  recursion.  When  one  of these limits is
       reached, pcre_exec() gives an error return. The limits can also be  set
       by items at the start of the pattern of the form

         (*LIMIT_MATCH=d)
         (*LIMIT_RECURSION=d)

       where d is any number of decimal digits. However, the value of the set-
       ting must be less than the value set (or defaulted) by  the  caller  of
       pcre_exec()  for  it  to  have  any effect. In other words, the pattern
       writer can lower the limits set by the programmer, but not raise  them.
       If  there  is  more  than one setting of one of these limits, the lower
       value is used.


EBCDIC CHARACTER CODES

       PCRE can be compiled to run in an environment that uses EBCDIC  as  its
       character code rather than ASCII or Unicode (typically a mainframe sys-
       tem). In the sections below, character code values are  ASCII  or  Uni-
       code; in an EBCDIC environment these characters may have different code
       values, and there are no code points greater than 255.


CHARACTERS AND METACHARACTERS

       A regular expression is a pattern that is  matched  against  a  subject
       string  from  left  to right. Most characters stand for themselves in a
       pattern, and match the corresponding characters in the  subject.  As  a
       trivial example, the pattern

         The quick brown fox

       matches a portion of a subject string that is identical to itself. When
       caseless matching is specified (the PCRE_CASELESS option), letters  are
       matched  independently  of case. In a UTF mode, PCRE always understands
       the concept of case for characters whose values are less than  128,  so
       caseless  matching  is always possible. For characters with higher val-
       ues, the concept of case is supported if PCRE is compiled with  Unicode
       property  support,  but  not  otherwise.   If  you want to use caseless
       matching for characters 128 and above, you must  ensure  that  PCRE  is
       compiled with Unicode property support as well as with UTF support.

       The  power  of  regular  expressions  comes from the ability to include
       alternatives and repetitions in the pattern. These are encoded  in  the
       pattern by the use of metacharacters, which do not stand for themselves
       but instead are interpreted in some special way.

       There are two different sets of metacharacters: those that  are  recog-
       nized  anywhere in the pattern except within square brackets, and those
       that are recognized within square brackets.  Outside  square  brackets,
       the metacharacters are as follows:

         \      general escape character with several uses
         ^      assert start of string (or line, in multiline mode)
         $      assert end of string (or line, in multiline mode)
         .      match any character except newline (by default)
         [      start character class definition
         |      start of alternative branch
         (      start subpattern
         )      end subpattern
         ?      extends the meaning of (
                also 0 or 1 quantifier
                also quantifier minimizer
         *      0 or more quantifier
         +      1 or more quantifier
                also "possessive quantifier"
         {      start min/max quantifier

       Part  of  a  pattern  that is in square brackets is called a "character
       class". In a character class the only metacharacters are:

         \      general escape character
         ^      negate the class, but only if the first character
         -      indicates character range
         [      POSIX character class (only if followed by POSIX
                  syntax)
         ]      terminates the character class

       The following sections describe the use of each of the metacharacters.


BACKSLASH

       The backslash character has several uses. Firstly, if it is followed by
       a character that is not a number or a letter, it takes away any special
       meaning that character may have. This use of  backslash  as  an  escape
       character applies both inside and outside character classes.

       For  example,  if  you want to match a * character, you write \* in the
       pattern.  This escaping action applies whether  or  not  the  following
       character  would  otherwise be interpreted as a metacharacter, so it is
       always safe to precede a non-alphanumeric  with  backslash  to  specify
       that  it stands for itself. In particular, if you want to match a back-
       slash, you write \\.

       In a UTF mode, only ASCII numbers and letters have any special  meaning
       after  a  backslash.  All  other characters (in particular, those whose
       codepoints are greater than 127) are treated as literals.

       If a pattern is compiled with  the  PCRE_EXTENDED  option,  most  white
       space  in the pattern (other than in a character class), and characters
       between a # outside a character class and the next newline,  inclusive,
       are ignored. An escaping backslash can be used to include a white space
       or # character as part of the pattern.

       If you want to remove the special meaning from a  sequence  of  charac-
       ters,  you can do so by putting them between \Q and \E. This is differ-
       ent from Perl in that $ and  @  are  handled  as  literals  in  \Q...\E
       sequences  in  PCRE, whereas in Perl, $ and @ cause variable interpola-
       tion. Note the following examples:

         Pattern            PCRE matches   Perl matches

         \Qabc$xyz\E        abc$xyz        abc followed by the
                                             contents of $xyz
         \Qabc\$xyz\E       abc\$xyz       abc\$xyz
         \Qabc\E\$\Qxyz\E   abc$xyz        abc$xyz

       The \Q...\E sequence is recognized both inside  and  outside  character
       classes.   An  isolated \E that is not preceded by \Q is ignored. If \Q
       is not followed by \E later in the pattern, the literal  interpretation
       continues  to  the  end  of  the pattern (that is, \E is assumed at the
       end). If the isolated \Q is inside a character class,  this  causes  an
       error, because the character class is not terminated.

   Non-printing characters

       A second use of backslash provides a way of encoding non-printing char-
       acters in patterns in a visible manner. There is no restriction on  the
       appearance  of non-printing characters, apart from the binary zero that
       terminates a pattern, but when a pattern  is  being  prepared  by  text
       editing,  it  is  often  easier  to  use  one  of  the following escape
       sequences than the binary character it represents:

         \a        alarm, that is, the BEL character (hex 07)
         \cx       "control-x", where x is any ASCII character
         \e        escape (hex 1B)
         \f        form feed (hex 0C)
         \n        linefeed (hex 0A)
         \r        carriage return (hex 0D)
         \t        tab (hex 09)
         \0dd      character with octal code 0dd
         \ddd      character with octal code ddd, or back reference
         \o{ddd..} character with octal code ddd..
         \xhh      character with hex code hh
         \x{hhh..} character with hex code hhh.. (non-JavaScript mode)
         \uhhhh    character with hex code hhhh (JavaScript mode only)

       The precise effect of \cx on ASCII characters is as follows: if x is  a
       lower  case  letter,  it  is converted to upper case. Then bit 6 of the
       character (hex 40) is inverted. Thus \cA to \cZ become hex 01 to hex 1A
       (A  is  41, Z is 5A), but \c{ becomes hex 3B ({ is 7B), and \c; becomes
       hex 7B (; is 3B). If the data item (byte or 16-bit value) following  \c
       has  a  value greater than 127, a compile-time error occurs. This locks
       out non-ASCII characters in all modes.

       The \c facility was designed for use with ASCII  characters,  but  with
       the  extension  to  Unicode it is even less useful than it once was. It
       is, however, recognized when PCRE is compiled  in  EBCDIC  mode,  where
       data  items  are always bytes. In this mode, all values are valid after
       \c. If the next character is a lower case letter, it  is  converted  to
       upper  case.  Then  the  0xc0  bits  of the byte are inverted. Thus \cA
       becomes hex 01, as in ASCII (A is C1), but because the  EBCDIC  letters
       are  disjoint,  \cZ becomes hex 29 (Z is E9), and other characters also
       generate different values.

       After \0 up to two further octal digits are read. If  there  are  fewer
       than  two  digits,  just  those  that  are  present  are used. Thus the
       sequence \0\x\07 specifies two binary zeros followed by a BEL character
       (code  value 7). Make sure you supply two digits after the initial zero
       if the pattern character that follows is itself an octal digit.

       The escape \o must be followed by a sequence of octal digits,  enclosed
       in  braces.  An  error occurs if this is not the case. This escape is a
       recent addition to Perl; it provides way of specifying  character  code
       points  as  octal  numbers  greater than 0777, and it also allows octal
       numbers and back references to be unambiguously specified.

       For greater clarity and unambiguity, it is best to avoid following \ by
       a digit greater than zero. Instead, use \o{} or \x{} to specify charac-
       ter numbers, and \g{} to specify back references. The  following  para-
       graphs describe the old, ambiguous syntax.

       The handling of a backslash followed by a digit other than 0 is compli-
       cated, and Perl has changed in recent releases, causing  PCRE  also  to
       change. Outside a character class, PCRE reads the digit and any follow-
       ing digits as a decimal number. If the number is less  than  8,  or  if
       there  have been at least that many previous capturing left parentheses
       in the expression, the entire sequence is taken as a back reference.  A
       description  of how this works is given later, following the discussion
       of parenthesized subpatterns.

       Inside a character class, or if  the  decimal  number  following  \  is
       greater than 7 and there have not been that many capturing subpatterns,
       PCRE handles \8 and \9 as the literal characters "8" and "9", and  oth-
       erwise re-reads up to three octal digits following the backslash, using
       them to generate a data character.  Any  subsequent  digits  stand  for
       themselves. For example:

         \040   is another way of writing an ASCII space
         \40    is the same, provided there are fewer than 40
                   previous capturing subpatterns
         \7     is always a back reference
         \11    might be a back reference, or another way of
                   writing a tab
         \011   is always a tab
         \0113  is a tab followed by the character "3"
         \113   might be a back reference, otherwise the
                   character with octal code 113
         \377   might be a back reference, otherwise
                   the value 255 (decimal)
         \81    is either a back reference, or the two
                   characters "8" and "1"

       Note  that octal values of 100 or greater that are specified using this
       syntax must not be introduced by a leading zero, because no  more  than
       three octal digits are ever read.

       By  default, after \x that is not followed by {, from zero to two hexa-
       decimal digits are read (letters can be in upper or  lower  case).  Any
       number of hexadecimal digits may appear between \x{ and }. If a charac-
       ter other than a hexadecimal digit appears between \x{  and  },  or  if
       there is no terminating }, an error occurs.

       If  the  PCRE_JAVASCRIPT_COMPAT option is set, the interpretation of \x
       is as just described only when it is followed by two  hexadecimal  dig-
       its.   Otherwise,  it  matches  a  literal "x" character. In JavaScript
       mode, support for code points greater than 256 is provided by \u, which
       must  be  followed  by  four hexadecimal digits; otherwise it matches a
       literal "u" character.

       Characters whose value is less than 256 can be defined by either of the
       two  syntaxes for \x (or by \u in JavaScript mode). There is no differ-
       ence in the way they are handled. For example, \xdc is exactly the same
       as \x{dc} (or \u00dc in JavaScript mode).

   Constraints on character values

       Characters  that  are  specified using octal or hexadecimal numbers are
       limited to certain values, as follows:

         8-bit non-UTF mode    less than 0x100
         8-bit UTF-8 mode      less than 0x10ffff and a valid codepoint
         16-bit non-UTF mode   less than 0x10000
         16-bit UTF-16 mode    less than 0x10ffff and a valid codepoint
         32-bit non-UTF mode   less than 0x100000000
         32-bit UTF-32 mode    less than 0x10ffff and a valid codepoint

       Invalid Unicode codepoints are the range  0xd800  to  0xdfff  (the  so-
       called "surrogate" codepoints), and 0xffef.

   Escape sequences in character classes

       All the sequences that define a single character value can be used both
       inside and outside character classes. In addition, inside  a  character
       class, \b is interpreted as the backspace character (hex 08).

       \N  is not allowed in a character class. \B, \R, and \X are not special
       inside a character class. Like  other  unrecognized  escape  sequences,
       they  are  treated  as  the  literal  characters  "B",  "R", and "X" by
       default, but cause an error if the PCRE_EXTRA option is set. Outside  a
       character class, these sequences have different meanings.

   Unsupported escape sequences

       In  Perl, the sequences \l, \L, \u, and \U are recognized by its string
       handler and used  to  modify  the  case  of  following  characters.  By
       default,  PCRE does not support these escape sequences. However, if the
       PCRE_JAVASCRIPT_COMPAT option is set, \U matches a "U"  character,  and
       \u can be used to define a character by code point, as described in the
       previous section.

   Absolute and relative back references

       The sequence \g followed by an unsigned or a negative  number,  option-
       ally  enclosed  in braces, is an absolute or relative back reference. A
       named back reference can be coded as \g{name}. Back references are dis-
       cussed later, following the discussion of parenthesized subpatterns.

   Absolute and relative subroutine calls

       For  compatibility with Oniguruma, the non-Perl syntax \g followed by a
       name or a number enclosed either in angle brackets or single quotes, is
       an  alternative  syntax for referencing a subpattern as a "subroutine".
       Details are discussed later.   Note  that  \g{...}  (Perl  syntax)  and
       \g<...>  (Oniguruma  syntax)  are  not synonymous. The former is a back
       reference; the latter is a subroutine call.

   Generic character types

       Another use of backslash is for specifying generic character types:

         \d     any decimal digit
         \D     any character that is not a decimal digit
         \h     any horizontal white space character
         \H     any character that is not a horizontal white space character
         \s     any white space character
         \S     any character that is not a white space character
         \v     any vertical white space character
         \V     any character that is not a vertical white space character
         \w     any "word" character
         \W     any "non-word" character

       There is also the single sequence \N, which matches a non-newline char-
       acter.   This  is the same as the "." metacharacter when PCRE_DOTALL is
       not set. Perl also uses \N to match characters by name; PCRE  does  not
       support this.

       Each  pair of lower and upper case escape sequences partitions the com-
       plete set of characters into two disjoint  sets.  Any  given  character
       matches  one, and only one, of each pair. The sequences can appear both
       inside and outside character classes. They each match one character  of
       the  appropriate  type.  If the current matching point is at the end of
       the subject string, all of them fail, because there is no character  to
       match.

       For  compatibility with Perl, \s did not used to match the VT character
       (code 11), which made it different from the the  POSIX  "space"  class.
       However,  Perl  added  VT  at  release  5.18, and PCRE followed suit at
       release 8.34. The default \s characters are now HT  (9),  LF  (10),  VT
       (11),  FF  (12),  CR  (13),  and space (32), which are defined as white
       space in the "C" locale. This list may vary if locale-specific matching
       is  taking place. For example, in some locales the "non-breaking space"
       character (\xA0) is recognized as white space, and  in  others  the  VT
       character is not.

       A  "word"  character is an underscore or any character that is a letter
       or digit.  By default, the definition of letters  and  digits  is  con-
       trolled  by PCRE's low-valued character tables, and may vary if locale-
       specific matching is taking place (see "Locale support" in the  pcreapi
       page).  For  example,  in  a French locale such as "fr_FR" in Unix-like
       systems, or "french" in Windows, some character codes greater than  127
       are  used  for  accented letters, and these are then matched by \w. The
       use of locales with Unicode is discouraged.

       By default, characters whose code points are  greater  than  127  never
       match \d, \s, or \w, and always match \D, \S, and \W, although this may
       vary for characters in the range 128-255 when locale-specific  matching
       is  happening.   These  escape sequences retain their original meanings
       from before Unicode support was available, mainly for  efficiency  rea-
       sons.  If  PCRE  is  compiled  with  Unicode  property support, and the
       PCRE_UCP option is set, the behaviour is changed so that Unicode  prop-
       erties are used to determine character types, as follows:

         \d  any character that matches \p{Nd} (decimal digit)
         \s  any character that matches \p{Z} or \h or \v
         \w  any character that matches \p{L} or \p{N}, plus underscore

       The  upper case escapes match the inverse sets of characters. Note that
       \d matches only decimal digits, whereas \w matches any  Unicode  digit,
       as  well as any Unicode letter, and underscore. Note also that PCRE_UCP
       affects \b, and \B because they are defined in  terms  of  \w  and  \W.
       Matching these sequences is noticeably slower when PCRE_UCP is set.

       The  sequences  \h, \H, \v, and \V are features that were added to Perl
       at release 5.10. In contrast to the other sequences, which  match  only
       ASCII  characters  by  default,  these always match certain high-valued
       code points, whether or not PCRE_UCP is set. The horizontal space char-
       acters are:

         U+0009     Horizontal tab (HT)
         U+0020     Space
         U+00A0     Non-break space
         U+1680     Ogham space mark
         U+180E     Mongolian vowel separator
         U+2000     En quad
         U+2001     Em quad
         U+2002     En space
         U+2003     Em space
         U+2004     Three-per-em space
         U+2005     Four-per-em space
         U+2006     Six-per-em space
         U+2007     Figure space
         U+2008     Punctuation space
         U+2009     Thin space
         U+200A     Hair space
         U+202F     Narrow no-break space
         U+205F     Medium mathematical space
         U+3000     Ideographic space

       The vertical space characters are:

         U+000A     Linefeed (LF)
         U+000B     Vertical tab (VT)
         U+000C     Form feed (FF)
         U+000D     Carriage return (CR)
         U+0085     Next line (NEL)
         U+2028     Line separator
         U+2029     Paragraph separator

       In 8-bit, non-UTF-8 mode, only the characters with codepoints less than
       256 are relevant.

   Newline sequences

       Outside a character class, by default, the escape sequence  \R  matches
       any  Unicode newline sequence. In 8-bit non-UTF-8 mode \R is equivalent
       to the following:

         (?>\r\n|\n|\x0b|\f|\r|\x85)

       This is an example of an "atomic group", details  of  which  are  given
       below.  This particular group matches either the two-character sequence
       CR followed by LF, or  one  of  the  single  characters  LF  (linefeed,
       U+000A),  VT  (vertical  tab, U+000B), FF (form feed, U+000C), CR (car-
       riage return, U+000D), or NEL (next line,  U+0085).  The  two-character
       sequence is treated as a single unit that cannot be split.

       In  other modes, two additional characters whose codepoints are greater
       than 255 are added: LS (line separator, U+2028) and PS (paragraph sepa-
       rator,  U+2029).   Unicode character property support is not needed for
       these characters to be recognized.

       It is possible to restrict \R to match only CR, LF, or CRLF (instead of
       the  complete  set  of  Unicode  line  endings)  by  setting the option
       PCRE_BSR_ANYCRLF either at compile time or when the pattern is matched.
       (BSR is an abbrevation for "backslash R".) This can be made the default
       when PCRE is built; if this is the case, the  other  behaviour  can  be
       requested  via  the  PCRE_BSR_UNICODE  option.   It is also possible to
       specify these settings by starting a pattern string  with  one  of  the
       following sequences:

         (*BSR_ANYCRLF)   CR, LF, or CRLF only
         (*BSR_UNICODE)   any Unicode newline sequence

       These override the default and the options given to the compiling func-
       tion, but they can themselves be  overridden  by  options  given  to  a
       matching  function.  Note  that  these  special settings, which are not
       Perl-compatible, are recognized only at the very start  of  a  pattern,
       and  that  they  must  be  in  upper  case. If more than one of them is
       present, the last one is used. They can be combined with  a  change  of
       newline convention; for example, a pattern can start with:

         (*ANY)(*BSR_ANYCRLF)

       They  can also be combined with the (*UTF8), (*UTF16), (*UTF32), (*UTF)
       or (*UCP) special sequences. Inside a character class, \R is treated as
       an  unrecognized  escape  sequence,  and  so  matches the letter "R" by
       default, but causes an error if PCRE_EXTRA is set.

   Unicode character properties

       When PCRE is built with Unicode character property support, three addi-
       tional  escape sequences that match characters with specific properties
       are available.  When in 8-bit non-UTF-8 mode, these  sequences  are  of
       course  limited  to  testing  characters whose codepoints are less than
       256, but they do work in this mode.  The extra escape sequences are:

         \p{xx}   a character with the xx property
         \P{xx}   a character without the xx property
         \X       a Unicode extended grapheme cluster

       The property names represented by xx above are limited to  the  Unicode
       script names, the general category properties, "Any", which matches any
       character  (including  newline),  and  some  special  PCRE   properties
       (described  in the next section).  Other Perl properties such as "InMu-
       sicalSymbols" are not currently supported by PCRE.  Note  that  \P{Any}
       does not match any characters, so always causes a match failure.

       Sets of Unicode characters are defined as belonging to certain scripts.
       A character from one of these sets can be matched using a script  name.
       For example:

         \p{Greek}
         \P{Han}

       Those  that are not part of an identified script are lumped together as
       "Common". The current list of scripts is:

       Arabic, Armenian, Avestan, Balinese, Bamum, Bassa_Vah, Batak,  Bengali,
       Bopomofo,  Brahmi,  Braille, Buginese, Buhid, Canadian_Aboriginal, Car-
       ian, Caucasian_Albanian, Chakma, Cham, Cherokee, Common, Coptic, Cunei-
       form, Cypriot, Cyrillic, Deseret, Devanagari, Duployan, Egyptian_Hiero-
       glyphs,  Elbasan,  Ethiopic,  Georgian,  Glagolitic,  Gothic,  Grantha,
       Greek,  Gujarati,  Gurmukhi,  Han,  Hangul,  Hanunoo, Hebrew, Hiragana,
       Imperial_Aramaic,    Inherited,     Inscriptional_Pahlavi,     Inscrip-
       tional_Parthian,   Javanese,   Kaithi,   Kannada,  Katakana,  Kayah_Li,
       Kharoshthi, Khmer, Khojki, Khudawadi, Lao, Latin, Lepcha,  Limbu,  Lin-
       ear_A,  Linear_B,  Lisu,  Lycian, Lydian, Mahajani, Malayalam, Mandaic,
       Manichaean,     Meetei_Mayek,     Mende_Kikakui,      Meroitic_Cursive,
       Meroitic_Hieroglyphs,  Miao,  Modi, Mongolian, Mro, Myanmar, Nabataean,
       New_Tai_Lue,  Nko,  Ogham,  Ol_Chiki,  Old_Italic,   Old_North_Arabian,
       Old_Permic, Old_Persian, Old_South_Arabian, Old_Turkic, Oriya, Osmanya,
       Pahawh_Hmong,    Palmyrene,    Pau_Cin_Hau,    Phags_Pa,    Phoenician,
       Psalter_Pahlavi,  Rejang,  Runic,  Samaritan, Saurashtra, Sharada, Sha-
       vian, Siddham, Sinhala, Sora_Sompeng, Sundanese, Syloti_Nagri,  Syriac,
       Tagalog,  Tagbanwa,  Tai_Le,  Tai_Tham, Tai_Viet, Takri, Tamil, Telugu,
       Thaana, Thai, Tibetan, Tifinagh, Tirhuta, Ugaritic,  Vai,  Warang_Citi,
       Yi.

       Each character has exactly one Unicode general category property, spec-
       ified by a two-letter abbreviation. For compatibility with Perl,  nega-
       tion  can  be  specified  by including a circumflex between the opening
       brace and the property name.  For  example,  \p{^Lu}  is  the  same  as
       \P{Lu}.

       If only one letter is specified with \p or \P, it includes all the gen-
       eral category properties that start with that letter. In this case,  in
       the  absence of negation, the curly brackets in the escape sequence are
       optional; these two examples have the same effect:

         \p{L}
         \pL

       The following general category property codes are supported:

         C     Other
         Cc    Control
         Cf    Format
         Cn    Unassigned
         Co    Private use
         Cs    Surrogate

         L     Letter
         Ll    Lower case letter
         Lm    Modifier letter
         Lo    Other letter
         Lt    Title case letter
         Lu    Upper case letter

         M     Mark
         Mc    Spacing mark
         Me    Enclosing mark
         Mn    Non-spacing mark

         N     Number
         Nd    Decimal number
         Nl    Letter number
         No    Other number

         P     Punctuation
         Pc    Connector punctuation
         Pd    Dash punctuation
         Pe    Close punctuation
         Pf    Final punctuation
         Pi    Initial punctuation
         Po    Other punctuation
         Ps    Open punctuation

         S     Symbol
         Sc    Currency symbol
         Sk    Modifier symbol
         Sm    Mathematical symbol
         So    Other symbol

         Z     Separator
         Zl    Line separator
         Zp    Paragraph separator
         Zs    Space separator

       The special property L& is also supported: it matches a character  that
       has  the  Lu,  Ll, or Lt property, in other words, a letter that is not
       classified as a modifier or "other".

       The Cs (Surrogate) property applies only to  characters  in  the  range
       U+D800  to U+DFFF. Such characters are not valid in Unicode strings and
       so cannot be tested by PCRE, unless  UTF  validity  checking  has  been
       turned    off    (see    the    discussion    of    PCRE_NO_UTF8_CHECK,
       PCRE_NO_UTF16_CHECK and PCRE_NO_UTF32_CHECK in the pcreapi page).  Perl
       does not support the Cs property.

       The  long  synonyms  for  property  names  that  Perl supports (such as
       \p{Letter}) are not supported by PCRE, nor is it  permitted  to  prefix
       any of these properties with "Is".

       No character that is in the Unicode table has the Cn (unassigned) prop-
       erty.  Instead, this property is assumed for any code point that is not
       in the Unicode table.

       Specifying  caseless  matching  does not affect these escape sequences.
       For example, \p{Lu} always matches only upper  case  letters.  This  is
       different from the behaviour of current versions of Perl.

       Matching  characters  by Unicode property is not fast, because PCRE has
       to do a multistage table lookup in order to find  a  character's  prop-
       erty. That is why the traditional escape sequences such as \d and \w do
       not use Unicode properties in PCRE by default, though you can make them
       do  so  by  setting the PCRE_UCP option or by starting the pattern with
       (*UCP).

   Extended grapheme clusters

       The \X escape matches any number of Unicode  characters  that  form  an
       "extended grapheme cluster", and treats the sequence as an atomic group
       (see below).  Up to and including release 8.31, PCRE  matched  an  ear-
       lier, simpler definition that was equivalent to

         (?>\PM\pM*)

       That  is,  it matched a character without the "mark" property, followed
       by zero or more characters with the "mark"  property.  Characters  with
       the  "mark"  property are typically non-spacing accents that affect the
       preceding character.

       This simple definition was extended in Unicode to include more  compli-
       cated  kinds of composite character by giving each character a grapheme
       breaking property, and creating rules  that  use  these  properties  to
       define  the  boundaries  of  extended grapheme clusters. In releases of
       PCRE later than 8.31, \X matches one of these clusters.

       \X always matches at least one character. Then it  decides  whether  to
       add additional characters according to the following rules for ending a
       cluster:

       1. End at the end of the subject string.

       2. Do not end between CR and LF; otherwise end after any control  char-
       acter.

       3.  Do  not  break  Hangul (a Korean script) syllable sequences. Hangul
       characters are of five types: L, V, T, LV, and LVT. An L character  may
       be  followed by an L, V, LV, or LVT character; an LV or V character may
       be followed by a V or T character; an LVT or T character may be follwed
       only by a T character.

       4.  Do not end before extending characters or spacing marks. Characters
       with the "mark" property always have  the  "extend"  grapheme  breaking
       property.

       5. Do not end after prepend characters.

       6. Otherwise, end the cluster.

   PCRE's additional properties

       As  well  as the standard Unicode properties described above, PCRE sup-
       ports four more that make it possible  to  convert  traditional  escape
       sequences  such as \w and \s to use Unicode properties. PCRE uses these
       non-standard, non-Perl properties internally when PCRE_UCP is set. How-
       ever, they may also be used explicitly. These properties are:

         Xan   Any alphanumeric character
         Xps   Any POSIX space character
         Xsp   Any Perl space character
         Xwd   Any Perl "word" character

       Xan  matches  characters that have either the L (letter) or the N (num-
       ber) property. Xps matches the characters tab, linefeed, vertical  tab,
       form  feed,  or carriage return, and any other character that has the Z
       (separator) property.  Xsp is the same as Xps; it used to exclude  ver-
       tical  tab,  for Perl compatibility, but Perl changed, and so PCRE fol-
       lowed at release 8.34. Xwd matches the same  characters  as  Xan,  plus
       underscore.

       There  is another non-standard property, Xuc, which matches any charac-
       ter that can be represented by a Universal Character Name  in  C++  and
       other  programming  languages.  These are the characters $, @, ` (grave
       accent), and all characters with Unicode code points  greater  than  or
       equal  to U+00A0, except for the surrogates U+D800 to U+DFFF. Note that
       most base (ASCII) characters are excluded. (Universal  Character  Names
       are  of  the  form \uHHHH or \UHHHHHHHH where H is a hexadecimal digit.
       Note that the Xuc property does not match these sequences but the char-
       acters that they represent.)

   Resetting the match start

       The  escape sequence \K causes any previously matched characters not to
       be included in the final matched sequence. For example, the pattern:

         foo\Kbar

       matches "foobar", but reports that it has matched "bar".  This  feature
       is  similar  to  a lookbehind assertion (described below).  However, in
       this case, the part of the subject before the real match does not  have
       to  be of fixed length, as lookbehind assertions do. The use of \K does
       not interfere with the setting of captured  substrings.   For  example,
       when the pattern

         (foo)\Kbar

       matches "foobar", the first substring is still set to "foo".

       Perl  documents  that  the  use  of  \K  within assertions is "not well
       defined". In PCRE, \K is acted upon  when  it  occurs  inside  positive
       assertions,  but  is  ignored  in negative assertions. Note that when a
       pattern such as (?=ab\K) matches, the reported start of the  match  can
       be greater than the end of the match.

   Simple assertions

       The  final use of backslash is for certain simple assertions. An asser-
       tion specifies a condition that has to be met at a particular point  in
       a  match, without consuming any characters from the subject string. The
       use of subpatterns for more complicated assertions is described  below.
       The backslashed assertions are:

         \b     matches at a word boundary
         \B     matches when not at a word boundary
         \A     matches at the start of the subject
         \Z     matches at the end of the subject
                 also matches before a newline at the end of the subject
         \z     matches only at the end of the subject
         \G     matches at the first matching position in the subject

       Inside  a  character  class, \b has a different meaning; it matches the
       backspace character. If any other of  these  assertions  appears  in  a
       character  class, by default it matches the corresponding literal char-
       acter  (for  example,  \B  matches  the  letter  B).  However,  if  the
       PCRE_EXTRA  option is set, an "invalid escape sequence" error is gener-
       ated instead.

       A word boundary is a position in the subject string where  the  current
       character  and  the previous character do not both match \w or \W (i.e.
       one matches \w and the other matches \W), or the start or  end  of  the
       string  if  the  first or last character matches \w, respectively. In a
       UTF mode, the meanings of \w and \W  can  be  changed  by  setting  the
       PCRE_UCP  option. When this is done, it also affects \b and \B. Neither
       PCRE nor Perl has a separate "start of word" or "end of  word"  metase-
       quence.  However,  whatever follows \b normally determines which it is.
       For example, the fragment \ba matches "a" at the start of a word.

       The \A, \Z, and \z assertions differ from  the  traditional  circumflex
       and dollar (described in the next section) in that they only ever match
       at the very start and end of the subject string, whatever  options  are
       set.  Thus,  they are independent of multiline mode. These three asser-
       tions are not affected by the PCRE_NOTBOL or PCRE_NOTEOL options, which
       affect  only the behaviour of the circumflex and dollar metacharacters.
       However, if the startoffset argument of pcre_exec() is non-zero,  indi-
       cating that matching is to start at a point other than the beginning of
       the subject, \A can never match. The difference between \Z  and  \z  is
       that \Z matches before a newline at the end of the string as well as at
       the very end, whereas \z matches only at the end.

       The \G assertion is true only when the current matching position is  at
       the  start point of the match, as specified by the startoffset argument
       of pcre_exec(). It differs from \A when the  value  of  startoffset  is
       non-zero.  By calling pcre_exec() multiple times with appropriate argu-
       ments, you can mimic Perl's /g option, and it is in this kind of imple-
       mentation where \G can be useful.

       Note,  however,  that  PCRE's interpretation of \G, as the start of the
       current match, is subtly different from Perl's, which defines it as the
       end  of  the  previous  match. In Perl, these can be different when the
       previously matched string was empty. Because PCRE does just  one  match
       at a time, it cannot reproduce this behaviour.

       If  all  the alternatives of a pattern begin with \G, the expression is
       anchored to the starting match position, and the "anchored" flag is set
       in the compiled regular expression.


CIRCUMFLEX AND DOLLAR

       The  circumflex  and  dollar  metacharacters are zero-width assertions.
       That is, they test for a particular condition being true  without  con-
       suming any characters from the subject string.

       Outside a character class, in the default matching mode, the circumflex
       character is an assertion that is true only  if  the  current  matching
       point  is  at the start of the subject string. If the startoffset argu-
       ment of pcre_exec() is non-zero, circumflex  can  never  match  if  the
       PCRE_MULTILINE  option  is  unset. Inside a character class, circumflex
       has an entirely different meaning (see below).

       Circumflex need not be the first character of the pattern if  a  number
       of  alternatives are involved, but it should be the first thing in each
       alternative in which it appears if the pattern is ever  to  match  that
       branch.  If all possible alternatives start with a circumflex, that is,
       if the pattern is constrained to match only at the start  of  the  sub-
       ject,  it  is  said  to be an "anchored" pattern. (There are also other
       constructs that can cause a pattern to be anchored.)

       The dollar character is an assertion that is true only if  the  current
       matching  point  is  at  the  end of the subject string, or immediately
       before a newline at the end of the string (by default). Note,  however,
       that  it  does  not  actually match the newline. Dollar need not be the
       last character of the pattern if a number of alternatives are involved,
       but  it should be the last item in any branch in which it appears. Dol-
       lar has no special meaning in a character class.

       The meaning of dollar can be changed so that it  matches  only  at  the
       very  end  of  the string, by setting the PCRE_DOLLAR_ENDONLY option at
       compile time. This does not affect the \Z assertion.

       The meanings of the circumflex and dollar characters are changed if the
       PCRE_MULTILINE  option  is  set.  When  this  is the case, a circumflex
       matches immediately after internal newlines as well as at the start  of
       the  subject  string.  It  does not match after a newline that ends the
       string. A dollar matches before any newlines in the string, as well  as
       at  the very end, when PCRE_MULTILINE is set. When newline is specified
       as the two-character sequence CRLF, isolated CR and  LF  characters  do
       not indicate newlines.

       For  example, the pattern /^abc$/ matches the subject string "def\nabc"
       (where \n represents a newline) in multiline mode, but  not  otherwise.
       Consequently,  patterns  that  are anchored in single line mode because
       all branches start with ^ are not anchored in  multiline  mode,  and  a
       match  for  circumflex  is  possible  when  the startoffset argument of
       pcre_exec() is non-zero. The PCRE_DOLLAR_ENDONLY option is  ignored  if
       PCRE_MULTILINE is set.

       Note  that  the sequences \A, \Z, and \z can be used to match the start
       and end of the subject in both modes, and if all branches of a  pattern
       start  with  \A it is always anchored, whether or not PCRE_MULTILINE is
       set.


FULL STOP (PERIOD, DOT) AND \N

       Outside a character class, a dot in the pattern matches any one charac-
       ter  in  the subject string except (by default) a character that signi-
       fies the end of a line.

       When a line ending is defined as a single character, dot never  matches
       that  character; when the two-character sequence CRLF is used, dot does
       not match CR if it is immediately followed  by  LF,  but  otherwise  it
       matches  all characters (including isolated CRs and LFs). When any Uni-
       code line endings are being recognized, dot does not match CR or LF  or
       any of the other line ending characters.

       The  behaviour  of  dot  with regard to newlines can be changed. If the
       PCRE_DOTALL option is set, a dot matches  any  one  character,  without
       exception. If the two-character sequence CRLF is present in the subject
       string, it takes two dots to match it.

       The handling of dot is entirely independent of the handling of  circum-
       flex  and  dollar,  the  only relationship being that they both involve
       newlines. Dot has no special meaning in a character class.

       The escape sequence \N behaves like  a  dot,  except  that  it  is  not
       affected  by  the  PCRE_DOTALL  option.  In other words, it matches any
       character except one that signifies the end of a line. Perl  also  uses
       \N to match characters by name; PCRE does not support this.


MATCHING A SINGLE DATA UNIT

       Outside  a character class, the escape sequence \C matches any one data
       unit, whether or not a UTF mode is set. In the 8-bit library, one  data
       unit  is  one  byte;  in the 16-bit library it is a 16-bit unit; in the
       32-bit library it is a 32-bit unit. Unlike a  dot,  \C  always  matches
       line-ending  characters.  The  feature  is provided in Perl in order to
       match individual bytes in UTF-8 mode, but it is unclear how it can use-
       fully  be  used.  Because  \C breaks up characters into individual data
       units, matching one unit with \C in a UTF mode means that the  rest  of
       the string may start with a malformed UTF character. This has undefined
       results, because PCRE assumes that it is dealing with valid UTF strings
       (and  by  default  it checks this at the start of processing unless the
       PCRE_NO_UTF8_CHECK, PCRE_NO_UTF16_CHECK or  PCRE_NO_UTF32_CHECK  option
       is used).

       PCRE  does  not  allow \C to appear in lookbehind assertions (described
       below) in a UTF mode, because this would make it impossible  to  calcu-
       late the length of the lookbehind.

       In general, the \C escape sequence is best avoided. However, one way of
       using it that avoids the problem of malformed UTF characters is to  use
       a  lookahead to check the length of the next character, as in this pat-
       tern, which could be used with a UTF-8 string (ignore white  space  and
       line breaks):

         (?| (?=[\x00-\x7f])(\C) |
             (?=[\x80-\x{7ff}])(\C)(\C) |
             (?=[\x{800}-\x{ffff}])(\C)(\C)(\C) |
             (?=[\x{10000}-\x{1fffff}])(\C)(\C)(\C)(\C))

       A  group  that starts with (?| resets the capturing parentheses numbers
       in each alternative (see "Duplicate  Subpattern  Numbers"  below).  The
       assertions  at  the start of each branch check the next UTF-8 character
       for values whose encoding uses 1, 2, 3, or 4 bytes,  respectively.  The
       character's  individual bytes are then captured by the appropriate num-
       ber of groups.


SQUARE BRACKETS AND CHARACTER CLASSES

       An opening square bracket introduces a character class, terminated by a
       closing square bracket. A closing square bracket on its own is not spe-
       cial by default.  However, if the PCRE_JAVASCRIPT_COMPAT option is set,
       a lone closing square bracket causes a compile-time error. If a closing
       square bracket is required as a member of the class, it should  be  the
       first  data  character  in  the  class (after an initial circumflex, if
       present) or escaped with a backslash.

       A character class matches a single character in the subject. In  a  UTF
       mode,  the  character  may  be  more than one data unit long. A matched
       character must be in the set of characters defined by the class, unless
       the  first  character in the class definition is a circumflex, in which
       case the subject character must not be in the set defined by the class.
       If  a  circumflex is actually required as a member of the class, ensure
       it is not the first character, or escape it with a backslash.

       For example, the character class [aeiou] matches any lower case  vowel,
       while  [^aeiou]  matches  any character that is not a lower case vowel.
       Note that a circumflex is just a convenient notation for specifying the
       characters  that  are in the class by enumerating those that are not. A
       class that starts with a circumflex is not an assertion; it still  con-
       sumes  a  character  from the subject string, and therefore it fails if
       the current pointer is at the end of the string.

       In UTF-8 (UTF-16, UTF-32) mode, characters with values greater than 255
       (0xffff)  can be included in a class as a literal string of data units,
       or by using the \x{ escaping mechanism.

       When caseless matching is set, any letters in a  class  represent  both
       their  upper  case  and lower case versions, so for example, a caseless
       [aeiou] matches "A" as well as "a", and a caseless  [^aeiou]  does  not
       match  "A", whereas a caseful version would. In a UTF mode, PCRE always
       understands the concept of case for characters whose  values  are  less
       than  128, so caseless matching is always possible. For characters with
       higher values, the concept of case is supported  if  PCRE  is  compiled
       with  Unicode  property support, but not otherwise.  If you want to use
       caseless matching in a UTF mode for characters 128 and above, you  must
       ensure  that  PCRE is compiled with Unicode property support as well as
       with UTF support.

       Characters that might indicate line breaks are  never  treated  in  any
       special  way  when  matching  character  classes,  whatever line-ending
       sequence is in  use,  and  whatever  setting  of  the  PCRE_DOTALL  and
       PCRE_MULTILINE options is used. A class such as [^a] always matches one
       of these characters.

       The minus (hyphen) character can be used to specify a range of  charac-
       ters  in  a  character  class.  For  example,  [d-m] matches any letter
       between d and m, inclusive. If a  minus  character  is  required  in  a
       class,  it  must  be  escaped  with a backslash or appear in a position
       where it cannot be interpreted as indicating a range, typically as  the
       first or last character in the class, or immediately after a range. For
       example, [b-d-z] matches letters in the range b to d, a hyphen  charac-
       ter, or z.

       It is not possible to have the literal character "]" as the end charac-
       ter of a range. A pattern such as [W-]46] is interpreted as a class  of
       two  characters ("W" and "-") followed by a literal string "46]", so it
       would match "W46]" or "-46]". However, if the "]"  is  escaped  with  a
       backslash  it is interpreted as the end of range, so [W-\]46] is inter-
       preted as a class containing a range followed by two other  characters.
       The  octal or hexadecimal representation of "]" can also be used to end
       a range.

       An error is generated if a POSIX character  class  (see  below)  or  an
       escape  sequence other than one that defines a single character appears
       at a point where a range ending character  is  expected.  For  example,
       [z-\xff] is valid, but [A-\d] and [A-[:digit:]] are not.

       Ranges  operate in the collating sequence of character values. They can
       also  be  used  for  characters  specified  numerically,  for   example
       [\000-\037].  Ranges  can include any characters that are valid for the
       current mode.

       If a range that includes letters is used when caseless matching is set,
       it matches the letters in either case. For example, [W-c] is equivalent
       to [][\\^_`wxyzabc], matched caselessly, and  in  a  non-UTF  mode,  if
       character  tables  for  a French locale are in use, [\xc8-\xcb] matches
       accented E characters in both cases. In UTF modes,  PCRE  supports  the
       concept  of  case for characters with values greater than 128 only when
       it is compiled with Unicode property support.

       The character escape sequences \d, \D, \h, \H, \p, \P, \s, \S, \v,  \V,
       \w, and \W may appear in a character class, and add the characters that
       they match to the class. For example, [\dABCDEF] matches any  hexadeci-
       mal  digit.  In  UTF modes, the PCRE_UCP option affects the meanings of
       \d, \s, \w and their upper case partners, just as  it  does  when  they
       appear  outside a character class, as described in the section entitled
       "Generic character types" above. The escape sequence \b has a different
       meaning  inside  a character class; it matches the backspace character.
       The sequences \B, \N, \R, and \X are not  special  inside  a  character
       class.  Like  any other unrecognized escape sequences, they are treated
       as the literal characters "B", "N", "R", and "X" by default, but  cause
       an error if the PCRE_EXTRA option is set.

       A  circumflex  can  conveniently  be used with the upper case character
       types to specify a more restricted set of characters than the  matching
       lower  case  type.  For example, the class [^\W_] matches any letter or
       digit, but not underscore, whereas [\w] includes underscore. A positive
       character class should be read as "something OR something OR ..." and a
       negative class as "NOT something AND NOT something AND NOT ...".

       The only metacharacters that are recognized in  character  classes  are
       backslash,  hyphen  (only  where  it can be interpreted as specifying a
       range), circumflex (only at the start), opening  square  bracket  (only
       when  it can be interpreted as introducing a POSIX class name, or for a
       special compatibility feature - see the next  two  sections),  and  the
       terminating  closing  square  bracket.  However,  escaping  other  non-
       alphanumeric characters does no harm.


POSIX CHARACTER CLASSES

       Perl supports the POSIX notation for character classes. This uses names
       enclosed  by  [: and :] within the enclosing square brackets. PCRE also
       supports this notation. For example,

         [01[:alpha:]%]

       matches "0", "1", any alphabetic character, or "%". The supported class
       names are:

         alnum    letters and digits
         alpha    letters
         ascii    character codes 0 - 127
         blank    space or tab only
         cntrl    control characters
         digit    decimal digits (same as \d)
         graph    printing characters, excluding space
         lower    lower case letters
         print    printing characters, including space
         punct    printing characters, excluding letters and digits and space
         space    white space (the same as \s from PCRE 8.34)
         upper    upper case letters
         word     "word" characters (same as \w)
         xdigit   hexadecimal digits

       The  default  "space" characters are HT (9), LF (10), VT (11), FF (12),
       CR (13), and space (32). If locale-specific matching is  taking  place,
       the  list  of  space characters may be different; there may be fewer or
       more of them. "Space" used to be different to \s, which did not include
       VT, for Perl compatibility.  However, Perl changed at release 5.18, and
       PCRE followed at release 8.34.  "Space" and \s now match the  same  set
       of characters.

       The  name  "word"  is  a Perl extension, and "blank" is a GNU extension
       from Perl 5.8. Another Perl extension is negation, which  is  indicated
       by a ^ character after the colon. For example,

         [12[:^digit:]]

       matches  "1", "2", or any non-digit. PCRE (and Perl) also recognize the
       POSIX syntax [.ch.] and [=ch=] where "ch" is a "collating element", but
       these are not supported, and an error is given if they are encountered.

       By default, characters with values greater than 128 do not match any of
       the POSIX character classes. However, if the PCRE_UCP option is  passed
       to  pcre_compile(),  some  of  the  classes are changed so that Unicode
       character properties are used. This is achieved  by  replacing  certain
       POSIX classes by other sequences, as follows:

         [:alnum:]  becomes  \p{Xan}
         [:alpha:]  becomes  \p{L}
         [:blank:]  becomes  \h
         [:digit:]  becomes  \p{Nd}
         [:lower:]  becomes  \p{Ll}
         [:space:]  becomes  \p{Xps}
         [:upper:]  becomes  \p{Lu}
         [:word:]   becomes  \p{Xwd}

       Negated  versions, such as [:^alpha:] use \P instead of \p. Three other
       POSIX classes are handled specially in UCP mode:

       [:graph:] This matches characters that have glyphs that mark  the  page
                 when printed. In Unicode property terms, it matches all char-
                 acters with the L, M, N, P, S, or Cf properties, except for:

                   U+061C           Arabic Letter Mark
                   U+180E           Mongolian Vowel Separator
                   U+2066 - U+2069  Various "isolate"s


       [:print:] This matches the same  characters  as  [:graph:]  plus  space
                 characters  that  are  not controls, that is, characters with
                 the Zs property.

       [:punct:] This matches all characters that have the Unicode P (punctua-
                 tion)  property,  plus those characters whose code points are
                 less than 128 that have the S (Symbol) property.

       The other POSIX classes are unchanged, and match only  characters  with
       code points less than 128.


COMPATIBILITY FEATURE FOR WORD BOUNDARIES

       In  the POSIX.2 compliant library that was included in 4.4BSD Unix, the
       ugly syntax [[:<:]] and [[:>:]] is used for matching  "start  of  word"
       and "end of word". PCRE treats these items as follows:

         [[:<:]]  is converted to  \b(?=\w)
         [[:>:]]  is converted to  \b(?<=\w)

       Only these exact character sequences are recognized. A sequence such as
       [a[:<:]b] provokes error for an unrecognized  POSIX  class  name.  This
       support  is not compatible with Perl. It is provided to help migrations
       from other environments, and is best not used in any new patterns. Note
       that  \b matches at the start and the end of a word (see "Simple asser-
       tions" above), and in a Perl-style pattern the preceding  or  following
       character  normally  shows  which  is  wanted, without the need for the
       assertions that are used above in order to give exactly the  POSIX  be-
       haviour.


VERTICAL BAR

       Vertical  bar characters are used to separate alternative patterns. For
       example, the pattern

         gilbert|sullivan

       matches either "gilbert" or "sullivan". Any number of alternatives  may
       appear,  and  an  empty  alternative  is  permitted (matching the empty
       string). The matching process tries each alternative in turn, from left
       to  right, and the first one that succeeds is used. If the alternatives
       are within a subpattern (defined below), "succeeds" means matching  the
       rest of the main pattern as well as the alternative in the subpattern.


INTERNAL OPTION SETTING

       The  settings  of  the  PCRE_CASELESS, PCRE_MULTILINE, PCRE_DOTALL, and
       PCRE_EXTENDED options (which are Perl-compatible) can be  changed  from
       within  the  pattern  by  a  sequence  of  Perl option letters enclosed
       between "(?" and ")".  The option letters are

         i  for PCRE_CASELESS
         m  for PCRE_MULTILINE
         s  for PCRE_DOTALL
         x  for PCRE_EXTENDED

       For example, (?im) sets caseless, multiline matching. It is also possi-
       ble to unset these options by preceding the letter with a hyphen, and a
       combined setting and unsetting such as (?im-sx), which sets  PCRE_CASE-
       LESS  and PCRE_MULTILINE while unsetting PCRE_DOTALL and PCRE_EXTENDED,
       is also permitted. If a  letter  appears  both  before  and  after  the
       hyphen, the option is unset.

       The  PCRE-specific options PCRE_DUPNAMES, PCRE_UNGREEDY, and PCRE_EXTRA
       can be changed in the same way as the Perl-compatible options by  using
       the characters J, U and X respectively.

       When  one  of  these  option  changes occurs at top level (that is, not
       inside subpattern parentheses), the change applies to the remainder  of
       the pattern that follows. If the change is placed right at the start of
       a pattern, PCRE extracts it into the global options (and it will there-
       fore show up in data extracted by the pcre_fullinfo() function).

       An  option  change  within a subpattern (see below for a description of
       subpatterns) affects only that part of the subpattern that follows  it,
       so

         (a(?i)b)c

       matches abc and aBc and no other strings (assuming PCRE_CASELESS is not
       used).  By this means, options can be made to have  different  settings
       in  different parts of the pattern. Any changes made in one alternative
       do carry on into subsequent branches within the  same  subpattern.  For
       example,

         (a(?i)b|c)

       matches  "ab",  "aB",  "c",  and "C", even though when matching "C" the
       first branch is abandoned before the option setting.  This  is  because
       the  effects  of option settings happen at compile time. There would be
       some very weird behaviour otherwise.

       Note: There are other PCRE-specific options that  can  be  set  by  the
       application  when  the  compiling  or matching functions are called. In
       some cases the pattern can contain special leading  sequences  such  as
       (*CRLF)  to  override  what  the  application  has set or what has been
       defaulted.  Details  are  given  in  the  section   entitled   "Newline
       sequences"  above.  There  are also the (*UTF8), (*UTF16),(*UTF32), and
       (*UCP) leading sequences that can be used to set UTF and Unicode  prop-
       erty  modes;  they are equivalent to setting the PCRE_UTF8, PCRE_UTF16,
       PCRE_UTF32 and the PCRE_UCP options, respectively. The (*UTF)  sequence
       is  a  generic version that can be used with any of the libraries. How-
       ever, the application can set the PCRE_NEVER_UTF  option,  which  locks
       out the use of the (*UTF) sequences.


SUBPATTERNS

       Subpatterns are delimited by parentheses (round brackets), which can be
       nested.  Turning part of a pattern into a subpattern does two things:

       1. It localizes a set of alternatives. For example, the pattern

         cat(aract|erpillar|)

       matches "cataract", "caterpillar", or "cat". Without  the  parentheses,
       it would match "cataract", "erpillar" or an empty string.

       2.  It  sets  up  the  subpattern as a capturing subpattern. This means
       that, when the whole pattern  matches,  that  portion  of  the  subject
       string that matched the subpattern is passed back to the caller via the
       ovector argument of the matching function. (This applies  only  to  the
       traditional  matching functions; the DFA matching functions do not sup-
       port capturing.)

       Opening parentheses are counted from left to right (starting from 1) to
       obtain  numbers  for  the  capturing  subpatterns.  For example, if the
       string "the red king" is matched against the pattern

         the ((red|white) (king|queen))

       the captured substrings are "red king", "red", and "king", and are num-
       bered 1, 2, and 3, respectively.

       The  fact  that  plain  parentheses  fulfil two functions is not always
       helpful.  There are often times when a grouping subpattern is  required
       without  a capturing requirement. If an opening parenthesis is followed
       by a question mark and a colon, the subpattern does not do any  captur-
       ing,  and  is  not  counted when computing the number of any subsequent
       capturing subpatterns. For example, if the string "the white queen"  is
       matched against the pattern

         the ((?:red|white) (king|queen))

       the captured substrings are "white queen" and "queen", and are numbered
       1 and 2. The maximum number of capturing subpatterns is 65535.

       As a convenient shorthand, if any option settings are required  at  the
       start  of  a  non-capturing  subpattern,  the option letters may appear
       between the "?" and the ":". Thus the two patterns

         (?i:saturday|sunday)
         (?:(?i)saturday|sunday)

       match exactly the same set of strings. Because alternative branches are
       tried  from  left  to right, and options are not reset until the end of
       the subpattern is reached, an option setting in one branch does  affect
       subsequent  branches,  so  the above patterns match "SUNDAY" as well as
       "Saturday".


DUPLICATE SUBPATTERN NUMBERS

       Perl 5.10 introduced a feature whereby each alternative in a subpattern
       uses  the same numbers for its capturing parentheses. Such a subpattern
       starts with (?| and is itself a non-capturing subpattern. For  example,
       consider this pattern:

         (?|(Sat)ur|(Sun))day

       Because  the two alternatives are inside a (?| group, both sets of cap-
       turing parentheses are numbered one. Thus, when  the  pattern  matches,
       you  can  look  at captured substring number one, whichever alternative
       matched. This construct is useful when you want to  capture  part,  but
       not all, of one of a number of alternatives. Inside a (?| group, paren-
       theses are numbered as usual, but the number is reset at the  start  of
       each  branch.  The numbers of any capturing parentheses that follow the
       subpattern start after the highest number used in any branch. The  fol-
       lowing example is taken from the Perl documentation. The numbers under-
       neath show in which buffer the captured content will be stored.

         # before  ---------------branch-reset----------- after
         / ( a )  (?| x ( y ) z | (p (q) r) | (t) u (v) ) ( z ) /x
         # 1            2         2  3        2     3     4

       A back reference to a numbered subpattern uses the  most  recent  value
       that  is  set  for that number by any subpattern. The following pattern
       matches "abcabc" or "defdef":

         /(?|(abc)|(def))\1/

       In contrast, a subroutine call to a numbered subpattern  always  refers
       to  the  first  one in the pattern with the given number. The following
       pattern matches "abcabc" or "defabc":

         /(?|(abc)|(def))(?1)/

       If a condition test for a subpattern's having matched refers to a  non-
       unique  number, the test is true if any of the subpatterns of that num-
       ber have matched.

       An alternative approach to using this "branch reset" feature is to  use
       duplicate named subpatterns, as described in the next section.


NAMED SUBPATTERNS

       Identifying  capturing  parentheses  by number is simple, but it can be
       very hard to keep track of the numbers in complicated  regular  expres-
       sions.  Furthermore,  if  an  expression  is  modified, the numbers may
       change. To help with this difficulty, PCRE supports the naming of  sub-
       patterns. This feature was not added to Perl until release 5.10. Python
       had the feature earlier, and PCRE introduced it at release  4.0,  using
       the  Python syntax. PCRE now supports both the Perl and the Python syn-
       tax. Perl allows identically numbered  subpatterns  to  have  different
       names, but PCRE does not.

       In  PCRE,  a subpattern can be named in one of three ways: (?<name>...)
       or (?'name'...) as in Perl, or (?P<name>...) as in  Python.  References
       to  capturing parentheses from other parts of the pattern, such as back
       references, recursion, and conditions, can be made by name as  well  as
       by number.

       Names  consist of up to 32 alphanumeric characters and underscores, but
       must start with a non-digit.  Named  capturing  parentheses  are  still
       allocated  numbers  as  well as names, exactly as if the names were not
       present. The PCRE API provides function calls for extracting the  name-
       to-number  translation  table  from a compiled pattern. There is also a
       convenience function for extracting a captured substring by name.

       By default, a name must be unique within a pattern, but it is  possible
       to relax this constraint by setting the PCRE_DUPNAMES option at compile
       time. (Duplicate names are also always permitted for  subpatterns  with
       the  same  number, set up as described in the previous section.) Dupli-
       cate names can be useful for patterns where only one  instance  of  the
       named  parentheses  can  match. Suppose you want to match the name of a
       weekday, either as a 3-letter abbreviation or as the full name, and  in
       both cases you want to extract the abbreviation. This pattern (ignoring
       the line breaks) does the job:

         (?<DN>Mon|Fri|Sun)(?:day)?|
         (?<DN>Tue)(?:sday)?|
         (?<DN>Wed)(?:nesday)?|
         (?<DN>Thu)(?:rsday)?|
         (?<DN>Sat)(?:urday)?

       There are five capturing substrings, but only one is ever set  after  a
       match.  (An alternative way of solving this problem is to use a "branch
       reset" subpattern, as described in the previous section.)

       The convenience function for extracting the data by  name  returns  the
       substring  for  the first (and in this example, the only) subpattern of
       that name that matched. This saves searching  to  find  which  numbered
       subpattern it was.

       If  you  make  a  back  reference to a non-unique named subpattern from
       elsewhere in the pattern, the subpatterns to which the name refers  are
       checked  in  the order in which they appear in the overall pattern. The
       first one that is set is used for the reference. For example, this pat-
       tern matches both "foofoo" and "barbar" but not "foobar" or "barfoo":

         (?:(?<n>foo)|(?<n>bar))\k<n>


       If you make a subroutine call to a non-unique named subpattern, the one
       that corresponds to the first occurrence of the name is  used.  In  the
       absence of duplicate numbers (see the previous section) this is the one
       with the lowest number.

       If you use a named reference in a condition test (see the section about
       conditions below), either to check whether a subpattern has matched, or
       to check for recursion, all subpatterns with the same name are  tested.
       If  the condition is true for any one of them, the overall condition is
       true. This is the same behaviour as  testing  by  number.  For  further
       details  of  the  interfaces  for  handling  named subpatterns, see the
       pcreapi documentation.

       Warning: You cannot use different names to distinguish between two sub-
       patterns  with  the same number because PCRE uses only the numbers when
       matching. For this reason, an error is given at compile time if differ-
       ent  names  are given to subpatterns with the same number. However, you
       can always give the same name to subpatterns with the same number, even
       when PCRE_DUPNAMES is not set.


REPETITION

       Repetition  is  specified  by  quantifiers, which can follow any of the
       following items:

         a literal data character
         the dot metacharacter
         the \C escape sequence
         the \X escape sequence
         the \R escape sequence
         an escape such as \d or \pL that matches a single character
         a character class
         a back reference (see next section)
         a parenthesized subpattern (including assertions)
         a subroutine call to a subpattern (recursive or otherwise)

       The general repetition quantifier specifies a minimum and maximum  num-
       ber  of  permitted matches, by giving the two numbers in curly brackets
       (braces), separated by a comma. The numbers must be  less  than  65536,
       and the first must be less than or equal to the second. For example:

         z{2,4}

       matches  "zz",  "zzz",  or  "zzzz". A closing brace on its own is not a
       special character. If the second number is omitted, but  the  comma  is
       present,  there  is  no upper limit; if the second number and the comma
       are both omitted, the quantifier specifies an exact number of  required
       matches. Thus

         [aeiou]{3,}

       matches at least 3 successive vowels, but may match many more, while

         \d{8}

       matches  exactly  8  digits. An opening curly bracket that appears in a
       position where a quantifier is not allowed, or one that does not  match
       the  syntax of a quantifier, is taken as a literal character. For exam-
       ple, {,6} is not a quantifier, but a literal string of four characters.

       In UTF modes, quantifiers apply to characters rather than to individual
       data  units. Thus, for example, \x{100}{2} matches two characters, each
       of which is represented by a two-byte sequence in a UTF-8 string. Simi-
       larly,  \X{3} matches three Unicode extended grapheme clusters, each of
       which may be several data units long (and  they  may  be  of  different
       lengths).

       The quantifier {0} is permitted, causing the expression to behave as if
       the previous item and the quantifier were not present. This may be use-
       ful  for  subpatterns that are referenced as subroutines from elsewhere
       in the pattern (but see also the section entitled "Defining subpatterns
       for  use  by  reference only" below). Items other than subpatterns that
       have a {0} quantifier are omitted from the compiled pattern.

       For convenience, the three most common quantifiers have  single-charac-
       ter abbreviations:

         *    is equivalent to {0,}
         +    is equivalent to {1,}
         ?    is equivalent to {0,1}

       It  is  possible  to construct infinite loops by following a subpattern
       that can match no characters with a quantifier that has no upper limit,
       for example:

         (a?)*

       Earlier versions of Perl and PCRE used to give an error at compile time
       for such patterns. However, because there are cases where this  can  be
       useful,  such  patterns  are now accepted, but if any repetition of the
       subpattern does in fact match no characters, the loop is forcibly  bro-
       ken.

       By  default,  the quantifiers are "greedy", that is, they match as much
       as possible (up to the maximum  number  of  permitted  times),  without
       causing  the  rest of the pattern to fail. The classic example of where
       this gives problems is in trying to match comments in C programs. These
       appear  between  /*  and  */ and within the comment, individual * and /
       characters may appear. An attempt to match C comments by  applying  the
       pattern

         /\*.*\*/

       to the string

         /* first comment */  not comment  /* second comment */

       fails,  because it matches the entire string owing to the greediness of
       the .*  item.

       However, if a quantifier is followed by a question mark, it  ceases  to
       be greedy, and instead matches the minimum number of times possible, so
       the pattern

         /\*.*?\*/

       does the right thing with the C comments. The meaning  of  the  various
       quantifiers  is  not  otherwise  changed,  just the preferred number of
       matches.  Do not confuse this use of question mark with its  use  as  a
       quantifier  in its own right. Because it has two uses, it can sometimes
       appear doubled, as in

         \d??\d

       which matches one digit by preference, but can match two if that is the
       only way the rest of the pattern matches.

       If  the PCRE_UNGREEDY option is set (an option that is not available in
       Perl), the quantifiers are not greedy by default, but  individual  ones
       can  be  made  greedy  by following them with a question mark. In other
       words, it inverts the default behaviour.

       When a parenthesized subpattern is quantified  with  a  minimum  repeat
       count  that is greater than 1 or with a limited maximum, more memory is
       required for the compiled pattern, in proportion to  the  size  of  the
       minimum or maximum.

       If a pattern starts with .* or .{0,} and the PCRE_DOTALL option (equiv-
       alent to Perl's /s) is set, thus allowing the dot  to  match  newlines,
       the  pattern  is  implicitly anchored, because whatever follows will be
       tried against every character position in the subject string, so  there
       is  no  point  in  retrying the overall match at any position after the
       first. PCRE normally treats such a pattern as though it  were  preceded
       by \A.

       In  cases  where  it  is known that the subject string contains no new-
       lines, it is worth setting PCRE_DOTALL in order to  obtain  this  opti-
       mization, or alternatively using ^ to indicate anchoring explicitly.

       However,  there  are  some cases where the optimization cannot be used.
       When .*  is inside capturing parentheses that are the subject of a back
       reference elsewhere in the pattern, a match at the start may fail where
       a later one succeeds. Consider, for example:

         (.*)abc\1

       If the subject is "xyz123abc123" the match point is the fourth  charac-
       ter. For this reason, such a pattern is not implicitly anchored.

       Another  case where implicit anchoring is not applied is when the lead-
       ing .* is inside an atomic group. Once again, a match at the start  may
       fail where a later one succeeds. Consider this pattern:

         (?>.*?a)b

       It  matches "ab" in the subject "aab". The use of the backtracking con-
       trol verbs (*PRUNE) and (*SKIP) also disable this optimization.

       When a capturing subpattern is repeated, the value captured is the sub-
       string that matched the final iteration. For example, after

         (tweedle[dume]{3}\s*)+

       has matched "tweedledum tweedledee" the value of the captured substring
       is "tweedledee". However, if there are  nested  capturing  subpatterns,
       the  corresponding captured values may have been set in previous itera-
       tions. For example, after

         /(a|(b))+/

       matches "aba" the value of the second captured substring is "b".


ATOMIC GROUPING AND POSSESSIVE QUANTIFIERS

       With both maximizing ("greedy") and minimizing ("ungreedy"  or  "lazy")
       repetition,  failure  of what follows normally causes the repeated item
       to be re-evaluated to see if a different number of repeats  allows  the
       rest  of  the pattern to match. Sometimes it is useful to prevent this,
       either to change the nature of the match, or to cause it  fail  earlier
       than  it otherwise might, when the author of the pattern knows there is
       no point in carrying on.

       Consider, for example, the pattern \d+foo when applied to  the  subject
       line

         123456bar

       After matching all 6 digits and then failing to match "foo", the normal
       action of the matcher is to try again with only 5 digits  matching  the
       \d+  item,  and  then  with  4,  and  so on, before ultimately failing.
       "Atomic grouping" (a term taken from Jeffrey  Friedl's  book)  provides
       the  means for specifying that once a subpattern has matched, it is not
       to be re-evaluated in this way.

       If we use atomic grouping for the previous example, the  matcher  gives
       up  immediately  on failing to match "foo" the first time. The notation
       is a kind of special parenthesis, starting with (?> as in this example:

         (?>\d+)foo

       This kind of parenthesis "locks up" the  part of the  pattern  it  con-
       tains  once  it  has matched, and a failure further into the pattern is
       prevented from backtracking into it. Backtracking past it  to  previous
       items, however, works as normal.

       An  alternative  description  is that a subpattern of this type matches
       the string of characters that an  identical  standalone  pattern  would
       match, if anchored at the current point in the subject string.

       Atomic grouping subpatterns are not capturing subpatterns. Simple cases
       such as the above example can be thought of as a maximizing repeat that
       must  swallow  everything  it can. So, while both \d+ and \d+? are pre-
       pared to adjust the number of digits they match in order  to  make  the
       rest of the pattern match, (?>\d+) can only match an entire sequence of
       digits.

       Atomic groups in general can of course contain arbitrarily  complicated
       subpatterns,  and  can  be  nested. However, when the subpattern for an
       atomic group is just a single repeated item, as in the example above, a
       simpler  notation,  called  a "possessive quantifier" can be used. This
       consists of an additional + character  following  a  quantifier.  Using
       this notation, the previous example can be rewritten as

         \d++foo

       Note that a possessive quantifier can be used with an entire group, for
       example:

         (abc|xyz){2,3}+

       Possessive  quantifiers  are  always  greedy;  the   setting   of   the
       PCRE_UNGREEDY option is ignored. They are a convenient notation for the
       simpler forms of atomic group. However, there is no difference  in  the
       meaning  of  a  possessive  quantifier and the equivalent atomic group,
       though there may be a performance  difference;  possessive  quantifiers
       should be slightly faster.

       The  possessive  quantifier syntax is an extension to the Perl 5.8 syn-
       tax.  Jeffrey Friedl originated the idea (and the name)  in  the  first
       edition of his book. Mike McCloskey liked it, so implemented it when he
       built Sun's Java package, and PCRE copied it from there. It  ultimately
       found its way into Perl at release 5.10.

       PCRE has an optimization that automatically "possessifies" certain sim-
       ple pattern constructs. For example, the sequence  A+B  is  treated  as
       A++B  because  there is no point in backtracking into a sequence of A's
       when B must follow.

       When a pattern contains an unlimited repeat inside  a  subpattern  that
       can  itself  be  repeated  an  unlimited number of times, the use of an
       atomic group is the only way to avoid some  failing  matches  taking  a
       very long time indeed. The pattern

         (\D+|<\d+>)*[!?]

       matches  an  unlimited number of substrings that either consist of non-
       digits, or digits enclosed in <>, followed by either ! or  ?.  When  it
       matches, it runs quickly. However, if it is applied to

         aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

       it  takes  a  long  time  before reporting failure. This is because the
       string can be divided between the internal \D+ repeat and the  external
       *  repeat  in  a  large  number of ways, and all have to be tried. (The
       example uses [!?] rather than a single character at  the  end,  because
       both  PCRE  and  Perl have an optimization that allows for fast failure
       when a single character is used. They remember the last single  charac-
       ter  that  is required for a match, and fail early if it is not present
       in the string.) If the pattern is changed so that  it  uses  an  atomic
       group, like this:

         ((?>\D+)|<\d+>)*[!?]

       sequences of non-digits cannot be broken, and failure happens quickly.


BACK REFERENCES

       Outside a character class, a backslash followed by a digit greater than
       0 (and possibly further digits) is a back reference to a capturing sub-
       pattern  earlier  (that is, to its left) in the pattern, provided there
       have been that many previous capturing left parentheses.

       However, if the decimal number following the backslash is less than 10,
       it  is  always  taken  as a back reference, and causes an error only if
       there are not that many capturing left parentheses in the  entire  pat-
       tern.  In  other words, the parentheses that are referenced need not be
       to the left of the reference for numbers less than 10. A "forward  back
       reference"  of  this  type can make sense when a repetition is involved
       and the subpattern to the right has participated in an  earlier  itera-
       tion.

       It  is  not  possible to have a numerical "forward back reference" to a
       subpattern whose number is 10 or  more  using  this  syntax  because  a
       sequence  such  as  \50 is interpreted as a character defined in octal.
       See the subsection entitled "Non-printing characters" above for further
       details  of  the  handling of digits following a backslash. There is no
       such problem when named parentheses are used. A back reference  to  any
       subpattern is possible using named parentheses (see below).

       Another  way  of  avoiding  the ambiguity inherent in the use of digits
       following a backslash is to use the \g  escape  sequence.  This  escape
       must be followed by an unsigned number or a negative number, optionally
       enclosed in braces. These examples are all identical:

         (ring), \1
         (ring), \g1
         (ring), \g{1}

       An unsigned number specifies an absolute reference without the  ambigu-
       ity that is present in the older syntax. It is also useful when literal
       digits follow the reference. A negative number is a relative reference.
       Consider this example:

         (abc(def)ghi)\g{-1}

       The sequence \g{-1} is a reference to the most recently started captur-
       ing subpattern before \g, that is, is it equivalent to \2 in this exam-
       ple.   Similarly, \g{-2} would be equivalent to \1. The use of relative
       references can be helpful in long patterns, and also in  patterns  that
       are  created  by  joining  together  fragments  that contain references
       within themselves.

       A back reference matches whatever actually matched the  capturing  sub-
       pattern  in  the  current subject string, rather than anything matching
       the subpattern itself (see "Subpatterns as subroutines" below for a way
       of doing that). So the pattern

         (sens|respons)e and \1ibility

       matches  "sense and sensibility" and "response and responsibility", but
       not "sense and responsibility". If caseful matching is in force at  the
       time  of the back reference, the case of letters is relevant. For exam-
       ple,

         ((?i)rah)\s+\1

       matches "rah rah" and "RAH RAH", but not "RAH  rah",  even  though  the
       original capturing subpattern is matched caselessly.

       There  are  several  different ways of writing back references to named
       subpatterns. The .NET syntax \k{name} and the Perl syntax  \k<name>  or
       \k'name'  are supported, as is the Python syntax (?P=name). Perl 5.10's
       unified back reference syntax, in which \g can be used for both numeric
       and  named  references,  is  also supported. We could rewrite the above
       example in any of the following ways:

         (?<p1>(?i)rah)\s+\k<p1>
         (?'p1'(?i)rah)\s+\k{p1}
         (?P<p1>(?i)rah)\s+(?P=p1)
         (?<p1>(?i)rah)\s+\g{p1}

       A subpattern that is referenced by  name  may  appear  in  the  pattern
       before or after the reference.

       There  may be more than one back reference to the same subpattern. If a
       subpattern has not actually been used in a particular match,  any  back
       references to it always fail by default. For example, the pattern

         (a|(bc))\2

       always  fails  if  it starts to match "a" rather than "bc". However, if
       the PCRE_JAVASCRIPT_COMPAT option is set at compile time, a back refer-
       ence to an unset value matches an empty string.

       Because  there may be many capturing parentheses in a pattern, all dig-
       its following a backslash are taken as part of a potential back  refer-
       ence  number.   If  the  pattern continues with a digit character, some
       delimiter must  be  used  to  terminate  the  back  reference.  If  the
       PCRE_EXTENDED  option  is  set, this can be white space. Otherwise, the
       \g{ syntax or an empty comment (see "Comments" below) can be used.

   Recursive back references

       A back reference that occurs inside the parentheses to which it  refers
       fails  when  the subpattern is first used, so, for example, (a\1) never
       matches.  However, such references can be useful inside  repeated  sub-
       patterns. For example, the pattern

         (a|b\1)+

       matches any number of "a"s and also "aba", "ababbaa" etc. At each iter-
       ation of the subpattern,  the  back  reference  matches  the  character
       string  corresponding  to  the previous iteration. In order for this to
       work, the pattern must be such that the first iteration does  not  need
       to  match the back reference. This can be done using alternation, as in
       the example above, or by a quantifier with a minimum of zero.

       Back references of this type cause the group that they reference to  be
       treated  as  an atomic group.  Once the whole group has been matched, a
       subsequent matching failure cannot cause backtracking into  the  middle
       of the group.


ASSERTIONS

       An  assertion  is  a  test on the characters following or preceding the
       current matching point that does not actually consume  any  characters.
       The  simple  assertions  coded  as  \b, \B, \A, \G, \Z, \z, ^ and $ are
       described above.

       More complicated assertions are coded as  subpatterns.  There  are  two
       kinds:  those  that  look  ahead of the current position in the subject
       string, and those that look  behind  it.  An  assertion  subpattern  is
       matched  in  the  normal way, except that it does not cause the current
       matching position to be changed.

       Assertion subpatterns are not capturing subpatterns. If such an  asser-
       tion  contains  capturing  subpatterns within it, these are counted for
       the purposes of numbering the capturing subpatterns in the  whole  pat-
       tern.  However,  substring  capturing  is carried out only for positive
       assertions. (Perl sometimes, but not always, does do capturing in nega-
       tive assertions.)

       For  compatibility  with  Perl,  assertion subpatterns may be repeated;
       though it makes no sense to assert the same thing  several  times,  the
       side  effect  of  capturing  parentheses may occasionally be useful. In
       practice, there only three cases:

       (1) If the quantifier is {0}, the  assertion  is  never  obeyed  during
       matching.   However,  it  may  contain internal capturing parenthesized
       groups that are called from elsewhere via the subroutine mechanism.

       (2) If quantifier is {0,n} where n is greater than zero, it is  treated
       as  if  it  were  {0,1}.  At run time, the rest of the pattern match is
       tried with and without the assertion, the order depending on the greed-
       iness of the quantifier.

       (3)  If  the minimum repetition is greater than zero, the quantifier is
       ignored.  The assertion is obeyed just  once  when  encountered  during
       matching.

   Lookahead assertions

       Lookahead assertions start with (?= for positive assertions and (?! for
       negative assertions. For example,

         \w+(?=;)

       matches a word followed by a semicolon, but does not include the  semi-
       colon in the match, and

         foo(?!bar)

       matches  any  occurrence  of  "foo" that is not followed by "bar". Note
       that the apparently similar pattern

         (?!foo)bar

       does not find an occurrence of "bar"  that  is  preceded  by  something
       other  than "foo"; it finds any occurrence of "bar" whatsoever, because
       the assertion (?!foo) is always true when the next three characters are
       "bar". A lookbehind assertion is needed to achieve the other effect.

       If you want to force a matching failure at some point in a pattern, the
       most convenient way to do it is  with  (?!)  because  an  empty  string
       always  matches, so an assertion that requires there not to be an empty
       string must always fail.  The backtracking control verb (*FAIL) or (*F)
       is a synonym for (?!).

   Lookbehind assertions

       Lookbehind  assertions start with (?<= for positive assertions and (?<!
       for negative assertions. For example,

         (?<!foo)bar

       does find an occurrence of "bar" that is not  preceded  by  "foo".  The
       contents  of  a  lookbehind  assertion are restricted such that all the
       strings it matches must have a fixed length. However, if there are sev-
       eral  top-level  alternatives,  they  do  not all have to have the same
       fixed length. Thus

         (?<=bullock|donkey)

       is permitted, but

         (?<!dogs?|cats?)

       causes an error at compile time. Branches that match  different  length
       strings  are permitted only at the top level of a lookbehind assertion.
       This is an extension compared with Perl, which requires all branches to
       match the same length of string. An assertion such as

         (?<=ab(c|de))

       is  not  permitted,  because  its single top-level branch can match two
       different lengths, but it is acceptable to PCRE if rewritten to use two
       top-level branches:

         (?<=abc|abde)

       In  some  cases, the escape sequence \K (see above) can be used instead
       of a lookbehind assertion to get round the fixed-length restriction.

       The implementation of lookbehind assertions is, for  each  alternative,
       to  temporarily  move the current position back by the fixed length and
       then try to match. If there are insufficient characters before the cur-
       rent position, the assertion fails.

       In  a UTF mode, PCRE does not allow the \C escape (which matches a sin-
       gle data unit even in a UTF mode) to appear in  lookbehind  assertions,
       because  it  makes it impossible to calculate the length of the lookbe-
       hind. The \X and \R escapes, which can match different numbers of  data
       units, are also not permitted.

       "Subroutine"  calls  (see below) such as (?2) or (?&X) are permitted in
       lookbehinds, as long as the subpattern matches a  fixed-length  string.
       Recursion, however, is not supported.

       Possessive  quantifiers  can  be  used  in  conjunction with lookbehind
       assertions to specify efficient matching of fixed-length strings at the
       end of subject strings. Consider a simple pattern such as

         abcd$

       when  applied  to  a  long string that does not match. Because matching
       proceeds from left to right, PCRE will look for each "a" in the subject
       and  then  see  if what follows matches the rest of the pattern. If the
       pattern is specified as

         ^.*abcd$

       the initial .* matches the entire string at first, but when this  fails
       (because there is no following "a"), it backtracks to match all but the
       last character, then all but the last two characters, and so  on.  Once
       again  the search for "a" covers the entire string, from right to left,
       so we are no better off. However, if the pattern is written as

         ^.*+(?<=abcd)

       there can be no backtracking for the .*+ item; it can  match  only  the
       entire  string.  The subsequent lookbehind assertion does a single test
       on the last four characters. If it fails, the match fails  immediately.
       For  long  strings, this approach makes a significant difference to the
       processing time.

   Using multiple assertions

       Several assertions (of any sort) may occur in succession. For example,

         (?<=\d{3})(?<!999)foo

       matches "foo" preceded by three digits that are not "999". Notice  that
       each  of  the  assertions is applied independently at the same point in
       the subject string. First there is a  check  that  the  previous  three
       characters  are  all  digits,  and  then there is a check that the same
       three characters are not "999".  This pattern does not match "foo" pre-
       ceded  by  six  characters,  the first of which are digits and the last
       three of which are not "999". For example, it  doesn't  match  "123abc-
       foo". A pattern to do that is

         (?<=\d{3}...)(?<!999)foo

       This  time  the  first assertion looks at the preceding six characters,
       checking that the first three are digits, and then the second assertion
       checks that the preceding three characters are not "999".

       Assertions can be nested in any combination. For example,

         (?<=(?<!foo)bar)baz

       matches  an occurrence of "baz" that is preceded by "bar" which in turn
       is not preceded by "foo", while

         (?<=\d{3}(?!999)...)foo

       is another pattern that matches "foo" preceded by three digits and  any
       three characters that are not "999".


CONDITIONAL SUBPATTERNS

       It  is possible to cause the matching process to obey a subpattern con-
       ditionally or to choose between two alternative subpatterns,  depending
       on  the result of an assertion, or whether a specific capturing subpat-
       tern has already been matched. The two possible  forms  of  conditional
       subpattern are:

         (?(condition)yes-pattern)
         (?(condition)yes-pattern|no-pattern)

       If  the  condition is satisfied, the yes-pattern is used; otherwise the
       no-pattern (if present) is used. If there are more  than  two  alterna-
       tives  in  the subpattern, a compile-time error occurs. Each of the two
       alternatives may itself contain nested subpatterns of any form, includ-
       ing  conditional  subpatterns;  the  restriction  to  two  alternatives
       applies only at the level of the condition. This pattern fragment is an
       example where the alternatives are complex:

         (?(1) (A|B|C) | (D | (?(2)E|F) | E) )


       There  are  four  kinds of condition: references to subpatterns, refer-
       ences to recursion, a pseudo-condition called DEFINE, and assertions.

   Checking for a used subpattern by number

       If the text between the parentheses consists of a sequence  of  digits,
       the condition is true if a capturing subpattern of that number has pre-
       viously matched. If there is more than one  capturing  subpattern  with
       the  same  number  (see  the earlier section about duplicate subpattern
       numbers), the condition is true if any of them have matched. An  alter-
       native  notation is to precede the digits with a plus or minus sign. In
       this case, the subpattern number is relative rather than absolute.  The
       most  recently opened parentheses can be referenced by (?(-1), the next
       most recent by (?(-2), and so on. Inside loops it can also  make  sense
       to refer to subsequent groups. The next parentheses to be opened can be
       referenced as (?(+1), and so on. (The value zero in any of these  forms
       is not used; it provokes a compile-time error.)

       Consider  the  following  pattern, which contains non-significant white
       space to make it more readable (assume the PCRE_EXTENDED option) and to
       divide it into three parts for ease of discussion:

         ( \( )?    [^()]+    (?(1) \) )

       The  first  part  matches  an optional opening parenthesis, and if that
       character is present, sets it as the first captured substring. The sec-
       ond  part  matches one or more characters that are not parentheses. The
       third part is a conditional subpattern that tests whether  or  not  the
       first  set  of  parentheses  matched.  If they did, that is, if subject
       started with an opening parenthesis, the condition is true, and so  the
       yes-pattern  is  executed and a closing parenthesis is required. Other-
       wise, since no-pattern is not present, the subpattern matches  nothing.
       In  other  words,  this  pattern matches a sequence of non-parentheses,
       optionally enclosed in parentheses.

       If you were embedding this pattern in a larger one,  you  could  use  a
       relative reference:

         ...other stuff... ( \( )?    [^()]+    (?(-1) \) ) ...

       This  makes  the  fragment independent of the parentheses in the larger
       pattern.

   Checking for a used subpattern by name

       Perl uses the syntax (?(<name>)...) or (?('name')...)  to  test  for  a
       used  subpattern  by  name.  For compatibility with earlier versions of
       PCRE, which had this facility before Perl, the syntax  (?(name)...)  is
       also recognized.

       Rewriting the above example to use a named subpattern gives this:

         (?<OPEN> \( )?    [^()]+    (?(<OPEN>) \) )

       If  the  name used in a condition of this kind is a duplicate, the test
       is applied to all subpatterns of the same name, and is true if any  one
       of them has matched.

   Checking for pattern recursion

       If the condition is the string (R), and there is no subpattern with the
       name R, the condition is true if a recursive call to the whole  pattern
       or any subpattern has been made. If digits or a name preceded by amper-
       sand follow the letter R, for example:

         (?(R3)...) or (?(R&name)...)

       the condition is true if the most recent recursion is into a subpattern
       whose number or name is given. This condition does not check the entire
       recursion stack. If the name used in a condition  of  this  kind  is  a
       duplicate, the test is applied to all subpatterns of the same name, and
       is true if any one of them is the most recent recursion.

       At "top level", all these recursion test  conditions  are  false.   The
       syntax for recursive patterns is described below.

   Defining subpatterns for use by reference only

       If  the  condition  is  the string (DEFINE), and there is no subpattern
       with the name DEFINE, the condition is  always  false.  In  this  case,
       there  may  be  only  one  alternative  in the subpattern. It is always
       skipped if control reaches this point  in  the  pattern;  the  idea  of
       DEFINE  is that it can be used to define subroutines that can be refer-
       enced from elsewhere. (The use of subroutines is described below.)  For
       example,  a  pattern  to match an IPv4 address such as "**************"
       could be written like this (ignore white space and line breaks):

         (?(DEFINE) (?<byte> 2[0-4]\d | 25[0-5] | 1\d\d | [1-9]?\d) )
         \b (?&byte) (\.(?&byte)){3} \b

       The first part of the pattern is a DEFINE group inside which a  another
       group  named "byte" is defined. This matches an individual component of
       an IPv4 address (a number less than 256). When  matching  takes  place,
       this  part  of  the pattern is skipped because DEFINE acts like a false
       condition. The rest of the pattern uses references to the  named  group
       to  match the four dot-separated components of an IPv4 address, insist-
       ing on a word boundary at each end.

   Assertion conditions

       If the condition is not in any of the above  formats,  it  must  be  an
       assertion.   This may be a positive or negative lookahead or lookbehind
       assertion. Consider  this  pattern,  again  containing  non-significant
       white space, and with the two alternatives on the second line:

         (?(?=[^a-z]*[a-z])
         \d{2}-[a-z]{3}-\d{2}  |  \d{2}-\d{2}-\d{2} )

       The  condition  is  a  positive  lookahead  assertion  that  matches an
       optional sequence of non-letters followed by a letter. In other  words,
       it  tests  for the presence of at least one letter in the subject. If a
       letter is found, the subject is matched against the first  alternative;
       otherwise  it  is  matched  against  the  second.  This pattern matches
       strings in one of the two forms dd-aaa-dd or dd-dd-dd,  where  aaa  are
       letters and dd are digits.


COMMENTS

       There are two ways of including comments in patterns that are processed
       by PCRE. In both cases, the start of the comment must not be in a char-
       acter class, nor in the middle of any other sequence of related charac-
       ters such as (?: or a subpattern name or number.  The  characters  that
       make up a comment play no part in the pattern matching.

       The  sequence (?# marks the start of a comment that continues up to the
       next closing parenthesis. Nested parentheses are not permitted. If  the
       PCRE_EXTENDED option is set, an unescaped # character also introduces a
       comment, which in this case continues to  immediately  after  the  next
       newline  character  or character sequence in the pattern. Which charac-
       ters are interpreted as newlines is controlled by the options passed to
       a  compiling function or by a special sequence at the start of the pat-
       tern, as described in the section entitled "Newline conventions" above.
       Note that the end of this type of comment is a literal newline sequence
       in the pattern; escape sequences that happen to represent a newline  do
       not  count.  For  example,  consider this pattern when PCRE_EXTENDED is
       set, and the default newline convention is in force:

         abc #comment \n still comment

       On encountering the # character, pcre_compile()  skips  along,  looking
       for  a newline in the pattern. The sequence \n is still literal at this
       stage, so it does not terminate the comment. Only an  actual  character
       with the code value 0x0a (the default newline) does so.


RECURSIVE PATTERNS

       Consider  the problem of matching a string in parentheses, allowing for
       unlimited nested parentheses. Without the use of  recursion,  the  best
       that  can  be  done  is  to use a pattern that matches up to some fixed
       depth of nesting. It is not possible to  handle  an  arbitrary  nesting
       depth.

       For some time, Perl has provided a facility that allows regular expres-
       sions to recurse (amongst other things). It does this by  interpolating
       Perl  code in the expression at run time, and the code can refer to the
       expression itself. A Perl pattern using code interpolation to solve the
       parentheses problem can be created like this:

         $re = qr{\( (?: (?>[^()]+) | (?p{$re}) )* \)}x;

       The (?p{...}) item interpolates Perl code at run time, and in this case
       refers recursively to the pattern in which it appears.

       Obviously, PCRE cannot support the interpolation of Perl code. Instead,
       it  supports  special  syntax  for recursion of the entire pattern, and
       also for individual subpattern recursion.  After  its  introduction  in
       PCRE  and  Python,  this  kind of recursion was subsequently introduced
       into Perl at release 5.10.

       A special item that consists of (? followed by a  number  greater  than
       zero  and  a  closing parenthesis is a recursive subroutine call of the
       subpattern of the given number, provided that  it  occurs  inside  that
       subpattern.  (If  not,  it is a non-recursive subroutine call, which is
       described in the next section.) The special item  (?R)  or  (?0)  is  a
       recursive call of the entire regular expression.

       This  PCRE  pattern  solves  the nested parentheses problem (assume the
       PCRE_EXTENDED option is set so that white space is ignored):

         \( ( [^()]++ | (?R) )* \)

       First it matches an opening parenthesis. Then it matches any number  of
       substrings  which  can  either  be  a sequence of non-parentheses, or a
       recursive match of the pattern itself (that is, a  correctly  parenthe-
       sized substring).  Finally there is a closing parenthesis. Note the use
       of a possessive quantifier to avoid backtracking into sequences of non-
       parentheses.

       If  this  were  part of a larger pattern, you would not want to recurse
       the entire pattern, so instead you could use this:

         ( \( ( [^()]++ | (?1) )* \) )

       We have put the pattern into parentheses, and caused the  recursion  to
       refer to them instead of the whole pattern.

       In  a  larger  pattern,  keeping  track  of  parenthesis numbers can be
       tricky. This is made easier by the use of relative references.  Instead
       of (?1) in the pattern above you can write (?-2) to refer to the second
       most recently opened parentheses  preceding  the  recursion.  In  other
       words,  a  negative  number counts capturing parentheses leftwards from
       the point at which it is encountered.

       It is also possible to refer to  subsequently  opened  parentheses,  by
       writing  references  such  as (?+2). However, these cannot be recursive
       because the reference is not inside the  parentheses  that  are  refer-
       enced.  They are always non-recursive subroutine calls, as described in
       the next section.

       An alternative approach is to use named parentheses instead.  The  Perl
       syntax  for  this  is (?&name); PCRE's earlier syntax (?P>name) is also
       supported. We could rewrite the above example as follows:

         (?<pn> \( ( [^()]++ | (?&pn) )* \) )

       If there is more than one subpattern with the same name,  the  earliest
       one is used.

       This  particular  example pattern that we have been looking at contains
       nested unlimited repeats, and so the use of a possessive quantifier for
       matching strings of non-parentheses is important when applying the pat-
       tern to strings that do not match. For example, when  this  pattern  is
       applied to

         (aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa()

       it  yields  "no  match" quickly. However, if a possessive quantifier is
       not used, the match runs for a very long time indeed because there  are
       so  many  different  ways the + and * repeats can carve up the subject,
       and all have to be tested before failure can be reported.

       At the end of a match, the values of capturing  parentheses  are  those
       from  the outermost level. If you want to obtain intermediate values, a
       callout function can be used (see below and the pcrecallout  documenta-
       tion). If the pattern above is matched against

         (ab(cd)ef)

       the  value  for  the  inner capturing parentheses (numbered 2) is "ef",
       which is the last value taken on at the top level. If a capturing  sub-
       pattern  is  not  matched at the top level, its final captured value is
       unset, even if it was (temporarily) set at a deeper  level  during  the
       matching process.

       If  there are more than 15 capturing parentheses in a pattern, PCRE has
       to obtain extra memory to store data during a recursion, which it  does
       by using pcre_malloc, freeing it via pcre_free afterwards. If no memory
       can be obtained, the match fails with the PCRE_ERROR_NOMEMORY error.

       Do not confuse the (?R) item with the condition (R),  which  tests  for
       recursion.   Consider  this pattern, which matches text in angle brack-
       ets, allowing for arbitrary nesting. Only digits are allowed in  nested
       brackets  (that is, when recursing), whereas any characters are permit-
       ted at the outer level.

         < (?: (?(R) \d++  | [^<>]*+) | (?R)) * >

       In this pattern, (?(R) is the start of a conditional  subpattern,  with
       two  different  alternatives for the recursive and non-recursive cases.
       The (?R) item is the actual recursive call.

   Differences in recursion processing between PCRE and Perl

       Recursion processing in PCRE differs from Perl in two  important  ways.
       In  PCRE (like Python, but unlike Perl), a recursive subpattern call is
       always treated as an atomic group. That is, once it has matched some of
       the subject string, it is never re-entered, even if it contains untried
       alternatives and there is a subsequent matching failure.  This  can  be
       illustrated  by the following pattern, which purports to match a palin-
       dromic string that contains an odd number of characters  (for  example,
       "a", "aba", "abcba", "abcdcba"):

         ^(.|(.)(?1)\2)$

       The idea is that it either matches a single character, or two identical
       characters surrounding a sub-palindrome. In Perl, this  pattern  works;
       in  PCRE  it  does  not if the pattern is longer than three characters.
       Consider the subject string "abcba":

       At the top level, the first character is matched, but as it is  not  at
       the end of the string, the first alternative fails; the second alterna-
       tive is taken and the recursion kicks in. The recursive call to subpat-
       tern  1  successfully  matches the next character ("b"). (Note that the
       beginning and end of line tests are not part of the recursion).

       Back at the top level, the next character ("c") is compared  with  what
       subpattern  2 matched, which was "a". This fails. Because the recursion
       is treated as an atomic group, there are now  no  backtracking  points,
       and  so  the  entire  match fails. (Perl is able, at this point, to re-
       enter the recursion and try the second alternative.)  However,  if  the
       pattern is written with the alternatives in the other order, things are
       different:

         ^((.)(?1)\2|.)$

       This time, the recursing alternative is tried first, and  continues  to
       recurse  until  it runs out of characters, at which point the recursion
       fails. But this time we do have  another  alternative  to  try  at  the
       higher  level.  That  is  the  big difference: in the previous case the
       remaining alternative is at a deeper recursion level, which PCRE cannot
       use.

       To  change  the pattern so that it matches all palindromic strings, not
       just those with an odd number of characters, it is tempting  to  change
       the pattern to this:

         ^((.)(?1)\2|.?)$

       Again,  this  works  in Perl, but not in PCRE, and for the same reason.
       When a deeper recursion has matched a single character,  it  cannot  be
       entered  again  in  order  to match an empty string. The solution is to
       separate the two cases, and write out the odd and even cases as  alter-
       natives at the higher level:

         ^(?:((.)(?1)\2|)|((.)(?3)\4|.))

       If  you  want  to match typical palindromic phrases, the pattern has to
       ignore all non-word characters, which can be done like this:

         ^\W*+(?:((.)\W*+(?1)\W*+\2|)|((.)\W*+(?3)\W*+\4|\W*+.\W*+))\W*+$

       If run with the PCRE_CASELESS option, this pattern matches phrases such
       as "A man, a plan, a canal: Panama!" and it works well in both PCRE and
       Perl. Note the use of the possessive quantifier *+ to avoid  backtrack-
       ing  into  sequences of non-word characters. Without this, PCRE takes a
       great deal longer (ten times or more) to  match  typical  phrases,  and
       Perl takes so long that you think it has gone into a loop.

       WARNING:  The  palindrome-matching patterns above work only if the sub-
       ject string does not start with a palindrome that is shorter  than  the
       entire  string.  For example, although "abcba" is correctly matched, if
       the subject is "ababa", PCRE finds the palindrome "aba" at  the  start,
       then  fails at top level because the end of the string does not follow.
       Once again, it cannot jump back into the recursion to try other  alter-
       natives, so the entire match fails.

       The  second  way  in which PCRE and Perl differ in their recursion pro-
       cessing is in the handling of captured values. In Perl, when a  subpat-
       tern  is  called recursively or as a subpattern (see the next section),
       it has no access to any values that were captured  outside  the  recur-
       sion,  whereas  in  PCRE  these values can be referenced. Consider this
       pattern:

         ^(.)(\1|a(?2))

       In PCRE, this pattern matches "bab". The  first  capturing  parentheses
       match  "b",  then in the second group, when the back reference \1 fails
       to match "b", the second alternative matches "a" and then recurses.  In
       the  recursion,  \1 does now match "b" and so the whole match succeeds.
       In Perl, the pattern fails to match because inside the  recursive  call
       \1 cannot access the externally set value.


SUBPATTERNS AS SUBROUTINES

       If  the  syntax for a recursive subpattern call (either by number or by
       name) is used outside the parentheses to which it refers,  it  operates
       like  a subroutine in a programming language. The called subpattern may
       be defined before or after the reference. A numbered reference  can  be
       absolute or relative, as in these examples:

         (...(absolute)...)...(?2)...
         (...(relative)...)...(?-1)...
         (...(?+1)...(relative)...

       An earlier example pointed out that the pattern

         (sens|respons)e and \1ibility

       matches  "sense and sensibility" and "response and responsibility", but
       not "sense and responsibility". If instead the pattern

         (sens|respons)e and (?1)ibility

       is used, it does match "sense and responsibility" as well as the  other
       two  strings.  Another  example  is  given  in the discussion of DEFINE
       above.

       All subroutine calls, whether recursive or not, are always  treated  as
       atomic  groups. That is, once a subroutine has matched some of the sub-
       ject string, it is never re-entered, even if it contains untried alter-
       natives  and  there  is  a  subsequent  matching failure. Any capturing
       parentheses that are set during the subroutine  call  revert  to  their
       previous values afterwards.

       Processing  options  such as case-independence are fixed when a subpat-
       tern is defined, so if it is used as a subroutine, such options  cannot
       be changed for different calls. For example, consider this pattern:

         (abc)(?i:(?-1))

       It  matches  "abcabc". It does not match "abcABC" because the change of
       processing option does not affect the called subpattern.


ONIGURUMA SUBROUTINE SYNTAX

       For compatibility with Oniguruma, the non-Perl syntax \g followed by  a
       name or a number enclosed either in angle brackets or single quotes, is
       an alternative syntax for referencing a  subpattern  as  a  subroutine,
       possibly  recursively. Here are two of the examples used above, rewrit-
       ten using this syntax:

         (?<pn> \( ( (?>[^()]+) | \g<pn> )* \) )
         (sens|respons)e and \g'1'ibility

       PCRE supports an extension to Oniguruma: if a number is preceded  by  a
       plus or a minus sign it is taken as a relative reference. For example:

         (abc)(?i:\g<-1>)

       Note  that \g{...} (Perl syntax) and \g<...> (Oniguruma syntax) are not
       synonymous. The former is a back reference; the latter is a  subroutine
       call.


CALLOUTS

       Perl has a feature whereby using the sequence (?{...}) causes arbitrary
       Perl code to be obeyed in the middle of matching a regular  expression.
       This makes it possible, amongst other things, to extract different sub-
       strings that match the same pair of parentheses when there is a repeti-
       tion.

       PCRE provides a similar feature, but of course it cannot obey arbitrary
       Perl code. The feature is called "callout". The caller of PCRE provides
       an  external function by putting its entry point in the global variable
       pcre_callout (8-bit library) or pcre[16|32]_callout (16-bit  or  32-bit
       library).   By default, this variable contains NULL, which disables all
       calling out.

       Within a regular expression, (?C) indicates the  points  at  which  the
       external  function  is  to be called. If you want to identify different
       callout points, you can put a number less than 256 after the letter  C.
       The  default  value is zero.  For example, this pattern has two callout
       points:

         (?C1)abc(?C2)def

       If the PCRE_AUTO_CALLOUT flag is passed to a compiling function,  call-
       outs  are automatically installed before each item in the pattern. They
       are all numbered 255. If there is a conditional group  in  the  pattern
       whose condition is an assertion, an additional callout is inserted just
       before the condition. An explicit callout may also be set at this posi-
       tion, as in this example:

         (?(?C9)(?=a)abc|def)

       Note that this applies only to assertion conditions, not to other types
       of condition.

       During matching, when PCRE reaches a callout point, the external  func-
       tion  is  called.  It  is  provided with the number of the callout, the
       position in the pattern, and, optionally, one item of  data  originally
       supplied  by  the caller of the matching function. The callout function
       may cause matching to proceed, to backtrack, or to fail altogether.

       By default, PCRE implements a number of optimizations at  compile  time
       and  matching  time, and one side-effect is that sometimes callouts are
       skipped. If you need all possible callouts to happen, you need  to  set
       options  that  disable  the relevant optimizations. More details, and a
       complete description of the interface  to  the  callout  function,  are
       given in the pcrecallout documentation.


BACKTRACKING CONTROL

       Perl  5.10 introduced a number of "Special Backtracking Control Verbs",
       which are still described in the Perl  documentation  as  "experimental
       and  subject to change or removal in a future version of Perl". It goes
       on to say: "Their usage in production code should  be  noted  to  avoid
       problems  during upgrades." The same remarks apply to the PCRE features
       described in this section.

       The new verbs make use of what was previously invalid syntax: an  open-
       ing parenthesis followed by an asterisk. They are generally of the form
       (*VERB) or (*VERB:NAME). Some may take either form,  possibly  behaving
       differently  depending  on  whether or not a name is present. A name is
       any sequence of characters that does not include a closing parenthesis.
       The maximum length of name is 255 in the 8-bit library and 65535 in the
       16-bit and 32-bit libraries. If the name is  empty,  that  is,  if  the
       closing  parenthesis immediately follows the colon, the effect is as if
       the colon were not there.  Any number of these verbs  may  occur  in  a
       pattern.

       Since  these  verbs  are  specifically related to backtracking, most of
       them can be used only when the pattern is to be matched  using  one  of
       the  traditional  matching  functions, because these use a backtracking
       algorithm. With the exception of (*FAIL), which behaves like a  failing
       negative  assertion,  the  backtracking control verbs cause an error if
       encountered by a DFA matching function.

       The behaviour of these verbs in repeated  groups,  assertions,  and  in
       subpatterns called as subroutines (whether or not recursively) is docu-
       mented below.

   Optimizations that affect backtracking verbs

       PCRE contains some optimizations that are used to speed up matching  by
       running some checks at the start of each match attempt. For example, it
       may know the minimum length of matching subject, or that  a  particular
       character must be present. When one of these optimizations bypasses the
       running of a match,  any  included  backtracking  verbs  will  not,  of
       course, be processed. You can suppress the start-of-match optimizations
       by setting the PCRE_NO_START_OPTIMIZE  option  when  calling  pcre_com-
       pile() or pcre_exec(), or by starting the pattern with (*NO_START_OPT).
       There is more discussion of this option in the section entitled "Option
       bits for pcre_exec()" in the pcreapi documentation.

       Experiments  with  Perl  suggest that it too has similar optimizations,
       sometimes leading to anomalous results.

   Verbs that act immediately

       The following verbs act as soon as they are encountered. They  may  not
       be followed by a name.

          (*ACCEPT)

       This  verb causes the match to end successfully, skipping the remainder
       of the pattern. However, when it is inside a subpattern that is  called
       as  a  subroutine, only that subpattern is ended successfully. Matching
       then continues at the outer level. If (*ACCEPT) in triggered in a posi-
       tive  assertion,  the  assertion succeeds; in a negative assertion, the
       assertion fails.

       If (*ACCEPT) is inside capturing parentheses, the data so far  is  cap-
       tured. For example:

         A((?:A|B(*ACCEPT)|C)D)

       This  matches  "AB", "AAD", or "ACD"; when it matches "AB", "B" is cap-
       tured by the outer parentheses.

         (*FAIL) or (*F)

       This verb causes a matching failure, forcing backtracking to occur.  It
       is  equivalent to (?!) but easier to read. The Perl documentation notes
       that it is probably useful only when combined  with  (?{})  or  (??{}).
       Those  are,  of course, Perl features that are not present in PCRE. The
       nearest equivalent is the callout feature, as for example in this  pat-
       tern:

         a+(?C)(*FAIL)

       A  match  with the string "aaaa" always fails, but the callout is taken
       before each backtrack happens (in this example, 10 times).

   Recording which path was taken

       There is one verb whose main purpose  is  to  track  how  a  match  was
       arrived  at,  though  it  also  has a secondary use in conjunction with
       advancing the match starting point (see (*SKIP) below).

         (*MARK:NAME) or (*:NAME)

       A name is always  required  with  this  verb.  There  may  be  as  many
       instances  of  (*MARK) as you like in a pattern, and their names do not
       have to be unique.

       When a match succeeds, the name of the  last-encountered  (*MARK:NAME),
       (*PRUNE:NAME),  or  (*THEN:NAME) on the matching path is passed back to
       the caller as  described  in  the  section  entitled  "Extra  data  for
       pcre_exec()"  in  the  pcreapi  documentation.  Here  is  an example of
       pcretest output, where the /K modifier requests the retrieval and  out-
       putting of (*MARK) data:

           re> /X(*MARK:A)Y|X(*MARK:B)Z/K
         data> XY
          0: XY
         MK: A
         XZ
          0: XZ
         MK: B

       The (*MARK) name is tagged with "MK:" in this output, and in this exam-
       ple it indicates which of the two alternatives matched. This is a  more
       efficient  way of obtaining this information than putting each alterna-
       tive in its own capturing parentheses.

       If a verb with a name is encountered in a positive  assertion  that  is
       true,  the  name  is recorded and passed back if it is the last-encoun-
       tered. This does not happen for negative assertions or failing positive
       assertions.

       After  a  partial match or a failed match, the last encountered name in
       the entire match process is returned. For example:

           re> /X(*MARK:A)Y|X(*MARK:B)Z/K
         data> XP
         No match, mark = B

       Note that in this unanchored example the  mark  is  retained  from  the
       match attempt that started at the letter "X" in the subject. Subsequent
       match attempts starting at "P" and then with an empty string do not get
       as far as the (*MARK) item, but nevertheless do not reset it.

       If  you  are  interested  in  (*MARK)  values after failed matches, you
       should probably set the PCRE_NO_START_OPTIMIZE option  (see  above)  to
       ensure that the match is always attempted.

   Verbs that act after backtracking

       The following verbs do nothing when they are encountered. Matching con-
       tinues with what follows, but if there is no subsequent match,  causing
       a  backtrack  to  the  verb, a failure is forced. That is, backtracking
       cannot pass to the left of the verb. However, when one of  these  verbs
       appears inside an atomic group or an assertion that is true, its effect
       is confined to that group, because once the  group  has  been  matched,
       there  is never any backtracking into it. In this situation, backtrack-
       ing can "jump back" to the left of the entire atomic  group  or  asser-
       tion.  (Remember  also,  as  stated  above, that this localization also
       applies in subroutine calls.)

       These verbs differ in exactly what kind of failure  occurs  when  back-
       tracking  reaches  them.  The behaviour described below is what happens
       when the verb is not in a subroutine or an assertion.  Subsequent  sec-
       tions cover these special cases.

         (*COMMIT)

       This  verb, which may not be followed by a name, causes the whole match
       to fail outright if there is a later matching failure that causes back-
       tracking  to  reach  it.  Even if the pattern is unanchored, no further
       attempts to find a match by advancing the starting point take place. If
       (*COMMIT)  is  the  only backtracking verb that is encountered, once it
       has been passed pcre_exec() is committed to finding a match at the cur-
       rent starting point, or not at all. For example:

         a+(*COMMIT)b

       This  matches  "xxaab" but not "aacaab". It can be thought of as a kind
       of dynamic anchor, or "I've started, so I must finish." The name of the
       most  recently passed (*MARK) in the path is passed back when (*COMMIT)
       forces a match failure.

       If there is more than one backtracking verb in a pattern,  a  different
       one  that  follows  (*COMMIT) may be triggered first, so merely passing
       (*COMMIT) during a match does not always guarantee that a match must be
       at this starting point.

       Note  that  (*COMMIT)  at  the start of a pattern is not the same as an
       anchor, unless PCRE's start-of-match optimizations are turned  off,  as
       shown in this output from pcretest:

           re> /(*COMMIT)abc/
         data> xyzabc
          0: abc
         data> xyzabc\Y
         No match

       For this pattern, PCRE knows that any match must start with "a", so the
       optimization skips along the subject to "a" before applying the pattern
       to  the first set of data. The match attempt then succeeds. In the sec-
       ond set of data, the escape sequence \Y is interpreted by the  pcretest
       program.  It  causes  the  PCRE_NO_START_OPTIMIZE option to be set when
       pcre_exec() is called.  This disables the optimization that skips along
       to the first character. The pattern is now applied starting at "x", and
       so the (*COMMIT) causes the match to  fail  without  trying  any  other
       starting points.

         (*PRUNE) or (*PRUNE:NAME)

       This  verb causes the match to fail at the current starting position in
       the subject if there is a later matching failure that causes backtrack-
       ing  to  reach it. If the pattern is unanchored, the normal "bumpalong"
       advance to the next starting character then happens.  Backtracking  can
       occur  as  usual to the left of (*PRUNE), before it is reached, or when
       matching to the right of (*PRUNE), but if there  is  no  match  to  the
       right,  backtracking cannot cross (*PRUNE). In simple cases, the use of
       (*PRUNE) is just an alternative to an atomic group or possessive  quan-
       tifier, but there are some uses of (*PRUNE) that cannot be expressed in
       any other way. In an anchored pattern (*PRUNE) has the same  effect  as
       (*COMMIT).

       The   behaviour   of   (*PRUNE:NAME)   is   the   not   the   same   as
       (*MARK:NAME)(*PRUNE).  It is like (*MARK:NAME)  in  that  the  name  is
       remembered  for  passing  back  to  the  caller.  However, (*SKIP:NAME)
       searches only for names set with (*MARK).

         (*SKIP)

       This verb, when given without a name, is like (*PRUNE), except that  if
       the  pattern  is unanchored, the "bumpalong" advance is not to the next
       character, but to the position in the subject where (*SKIP) was encoun-
       tered.  (*SKIP)  signifies that whatever text was matched leading up to
       it cannot be part of a successful match. Consider:

         a+(*SKIP)b

       If the subject is "aaaac...",  after  the  first  match  attempt  fails
       (starting  at  the  first  character in the string), the starting point
       skips on to start the next attempt at "c". Note that a possessive quan-
       tifer  does not have the same effect as this example; although it would
       suppress backtracking  during  the  first  match  attempt,  the  second
       attempt  would  start at the second character instead of skipping on to
       "c".

         (*SKIP:NAME)

       When (*SKIP) has an associated name, its behaviour is modified. When it
       is triggered, the previous path through the pattern is searched for the
       most recent (*MARK) that has the  same  name.  If  one  is  found,  the
       "bumpalong" advance is to the subject position that corresponds to that
       (*MARK) instead of to where (*SKIP) was encountered. If no (*MARK) with
       a matching name is found, the (*SKIP) is ignored.

       Note  that (*SKIP:NAME) searches only for names set by (*MARK:NAME). It
       ignores names that are set by (*PRUNE:NAME) or (*THEN:NAME).

         (*THEN) or (*THEN:NAME)

       This verb causes a skip to the next innermost  alternative  when  back-
       tracking  reaches  it.  That  is,  it  cancels any further backtracking
       within the current alternative. Its name  comes  from  the  observation
       that it can be used for a pattern-based if-then-else block:

         ( COND1 (*THEN) FOO | COND2 (*THEN) BAR | COND3 (*THEN) BAZ ) ...

       If  the COND1 pattern matches, FOO is tried (and possibly further items
       after the end of the group if FOO succeeds); on  failure,  the  matcher
       skips  to  the second alternative and tries COND2, without backtracking
       into COND1. If that succeeds and BAR fails, COND3 is tried.  If  subse-
       quently  BAZ fails, there are no more alternatives, so there is a back-
       track to whatever came before the  entire  group.  If  (*THEN)  is  not
       inside an alternation, it acts like (*PRUNE).

       The    behaviour   of   (*THEN:NAME)   is   the   not   the   same   as
       (*MARK:NAME)(*THEN).  It is like  (*MARK:NAME)  in  that  the  name  is
       remembered  for  passing  back  to  the  caller.  However, (*SKIP:NAME)
       searches only for names set with (*MARK).

       A subpattern that does not contain a | character is just a part of  the
       enclosing  alternative;  it  is  not a nested alternation with only one
       alternative. The effect of (*THEN) extends beyond such a subpattern  to
       the  enclosing alternative. Consider this pattern, where A, B, etc. are
       complex pattern fragments that do not contain any | characters at  this
       level:

         A (B(*THEN)C) | D

       If  A and B are matched, but there is a failure in C, matching does not
       backtrack into A; instead it moves to the next alternative, that is, D.
       However,  if the subpattern containing (*THEN) is given an alternative,
       it behaves differently:

         A (B(*THEN)C | (*FAIL)) | D

       The effect of (*THEN) is now confined to the inner subpattern. After  a
       failure in C, matching moves to (*FAIL), which causes the whole subpat-
       tern to fail because there are no more alternatives  to  try.  In  this
       case, matching does now backtrack into A.

       Note  that  a  conditional  subpattern  is not considered as having two
       alternatives, because only one is ever used.  In  other  words,  the  |
       character in a conditional subpattern has a different meaning. Ignoring
       white space, consider:

         ^.*? (?(?=a) a | b(*THEN)c )

       If the subject is "ba", this pattern does not  match.  Because  .*?  is
       ungreedy,  it  initially  matches  zero characters. The condition (?=a)
       then fails, the character "b" is matched,  but  "c"  is  not.  At  this
       point,  matching does not backtrack to .*? as might perhaps be expected
       from the presence of the | character.  The  conditional  subpattern  is
       part of the single alternative that comprises the whole pattern, and so
       the match fails. (If there was a backtrack into  .*?,  allowing  it  to
       match "b", the match would succeed.)

       The  verbs just described provide four different "strengths" of control
       when subsequent matching fails. (*THEN) is the weakest, carrying on the
       match  at  the next alternative. (*PRUNE) comes next, failing the match
       at the current starting position, but allowing an advance to  the  next
       character  (for an unanchored pattern). (*SKIP) is similar, except that
       the advance may be more than one character. (*COMMIT) is the strongest,
       causing the entire match to fail.

   More than one backtracking verb

       If  more  than  one  backtracking verb is present in a pattern, the one
       that is backtracked onto first acts. For example,  consider  this  pat-
       tern, where A, B, etc. are complex pattern fragments:

         (A(*COMMIT)B(*THEN)C|ABD)

       If  A matches but B fails, the backtrack to (*COMMIT) causes the entire
       match to fail. However, if A and B match, but C fails, the backtrack to
       (*THEN)  causes  the next alternative (ABD) to be tried. This behaviour
       is consistent, but is not always the same as Perl's. It means  that  if
       two  or  more backtracking verbs appear in succession, all the the last
       of them has no effect. Consider this example:

         ...(*COMMIT)(*PRUNE)...

       If there is a matching failure to the right, backtracking onto (*PRUNE)
       causes  it to be triggered, and its action is taken. There can never be
       a backtrack onto (*COMMIT).

   Backtracking verbs in repeated groups

       PCRE differs from  Perl  in  its  handling  of  backtracking  verbs  in
       repeated groups. For example, consider:

         /(a(*COMMIT)b)+ac/

       If  the  subject  is  "abac",  Perl matches, but PCRE fails because the
       (*COMMIT) in the second repeat of the group acts.

   Backtracking verbs in assertions

       (*FAIL) in an assertion has its normal effect: it forces  an  immediate
       backtrack.

       (*ACCEPT) in a positive assertion causes the assertion to succeed with-
       out any further processing. In a negative assertion,  (*ACCEPT)  causes
       the assertion to fail without any further processing.

       The  other  backtracking verbs are not treated specially if they appear
       in a positive assertion. In  particular,  (*THEN)  skips  to  the  next
       alternative  in  the  innermost  enclosing group that has alternations,
       whether or not this is within the assertion.

       Negative assertions are, however, different, in order  to  ensure  that
       changing  a  positive  assertion  into a negative assertion changes its
       result. Backtracking into (*COMMIT), (*SKIP), or (*PRUNE) causes a neg-
       ative assertion to be true, without considering any further alternative
       branches in the assertion.  Backtracking into (*THEN) causes it to skip
       to  the next enclosing alternative within the assertion (the normal be-
       haviour), but if the assertion  does  not  have  such  an  alternative,
       (*THEN) behaves like (*PRUNE).

   Backtracking verbs in subroutines

       These  behaviours  occur whether or not the subpattern is called recur-
       sively.  Perl's treatment of subroutines is different in some cases.

       (*FAIL) in a subpattern called as a subroutine has its  normal  effect:
       it forces an immediate backtrack.

       (*ACCEPT)  in a subpattern called as a subroutine causes the subroutine
       match to succeed without any further processing. Matching then  contin-
       ues after the subroutine call.

       (*COMMIT), (*SKIP), and (*PRUNE) in a subpattern called as a subroutine
       cause the subroutine match to fail.

       (*THEN) skips to the next alternative in the innermost enclosing  group
       within  the subpattern that has alternatives. If there is no such group
       within the subpattern, (*THEN) causes the subroutine match to fail.


SEE ALSO

       pcreapi(3), pcrecallout(3),  pcrematching(3),  pcresyntax(3),  pcre(3),
       pcre16(3), pcre32(3).


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 08 January 2014
       Copyright (c) 1997-2014 University of Cambridge.
------------------------------------------------------------------------------


PCRESYNTAX(3)              Library Functions Manual              PCRESYNTAX(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE REGULAR EXPRESSION SYNTAX SUMMARY

       The  full syntax and semantics of the regular expressions that are sup-
       ported by PCRE are described in  the  pcrepattern  documentation.  This
       document contains a quick-reference summary of the syntax.


QUOTING

         \x         where x is non-alphanumeric is a literal x
         \Q...\E    treat enclosed characters as literal


CHARACTERS

         \a         alarm, that is, the BEL character (hex 07)
         \cx        "control-x", where x is any ASCII character
         \e         escape (hex 1B)
         \f         form feed (hex 0C)
         \n         newline (hex 0A)
         \r         carriage return (hex 0D)
         \t         tab (hex 09)
         \0dd       character with octal code 0dd
         \ddd       character with octal code ddd, or backreference
         \o{ddd..}  character with octal code ddd..
         \xhh       character with hex code hh
         \x{hhh..}  character with hex code hhh..

       Note that \0dd is always an octal code, and that \8 and \9 are the lit-
       eral characters "8" and "9".


CHARACTER TYPES

         .          any character except newline;
                      in dotall mode, any character whatsoever
         \C         one data unit, even in UTF mode (best avoided)
         \d         a decimal digit
         \D         a character that is not a decimal digit
         \h         a horizontal white space character
         \H         a character that is not a horizontal white space character
         \N         a character that is not a newline
         \p{xx}     a character with the xx property
         \P{xx}     a character without the xx property
         \R         a newline sequence
         \s         a white space character
         \S         a character that is not a white space character
         \v         a vertical white space character
         \V         a character that is not a vertical white space character
         \w         a "word" character
         \W         a "non-word" character
         \X         a Unicode extended grapheme cluster

       By default, \d, \s, and \w match only ASCII characters, even  in  UTF-8
       mode  or  in  the 16- bit and 32-bit libraries. However, if locale-spe-
       cific matching is happening, \s and \w may also match  characters  with
       code  points  in  the range 128-255. If the PCRE_UCP option is set, the
       behaviour of these escape sequences is changed to use  Unicode  proper-
       ties and they match many more characters.


GENERAL CATEGORY PROPERTIES FOR \p and \P

         C          Other
         Cc         Control
         Cf         Format
         Cn         Unassigned
         Co         Private use
         Cs         Surrogate

         L          Letter
         Ll         Lower case letter
         Lm         Modifier letter
         Lo         Other letter
         Lt         Title case letter
         Lu         Upper case letter
         L&         Ll, Lu, or Lt

         M          Mark
         Mc         Spacing mark
         Me         Enclosing mark
         Mn         Non-spacing mark

         N          Number
         Nd         Decimal number
         Nl         Letter number
         No         Other number

         P          Punctuation
         Pc         Connector punctuation
         Pd         Dash punctuation
         Pe         Close punctuation
         Pf         Final punctuation
         Pi         Initial punctuation
         Po         Other punctuation
         Ps         Open punctuation

         S          Symbol
         Sc         Currency symbol
         Sk         Modifier symbol
         Sm         Mathematical symbol
         So         Other symbol

         Z          Separator
         Zl         Line separator
         Zp         Paragraph separator
         Zs         Space separator


PCRE SPECIAL CATEGORY PROPERTIES FOR \p and \P

         Xan        Alphanumeric: union of properties L and N
         Xps        POSIX space: property Z or tab, NL, VT, FF, CR
         Xsp        Perl space: property Z or tab, NL, VT, FF, CR
         Xuc        Univerally-named character: one that can be
                      represented by a Universal Character Name
         Xwd        Perl word: property Xan or underscore

       Perl and POSIX space are now the same. Perl added VT to its space char-
       acter set at release 5.18 and PCRE changed at release 8.34.


SCRIPT NAMES FOR \p AND \P

       Arabic, Armenian, Avestan, Balinese, Bamum, Bassa_Vah, Batak,  Bengali,
       Bopomofo,  Brahmi,  Braille, Buginese, Buhid, Canadian_Aboriginal, Car-
       ian, Caucasian_Albanian, Chakma, Cham, Cherokee, Common, Coptic, Cunei-
       form, Cypriot, Cyrillic, Deseret, Devanagari, Duployan, Egyptian_Hiero-
       glyphs,  Elbasan,  Ethiopic,  Georgian,  Glagolitic,  Gothic,  Grantha,
       Greek,  Gujarati,  Gurmukhi,  Han,  Hangul,  Hanunoo, Hebrew, Hiragana,
       Imperial_Aramaic,    Inherited,     Inscriptional_Pahlavi,     Inscrip-
       tional_Parthian,   Javanese,   Kaithi,   Kannada,  Katakana,  Kayah_Li,
       Kharoshthi, Khmer, Khojki, Khudawadi, Lao, Latin, Lepcha,  Limbu,  Lin-
       ear_A,  Linear_B,  Lisu,  Lycian, Lydian, Mahajani, Malayalam, Mandaic,
       Manichaean,     Meetei_Mayek,     Mende_Kikakui,      Meroitic_Cursive,
       Meroitic_Hieroglyphs,  Miao,  Modi, Mongolian, Mro, Myanmar, Nabataean,
       New_Tai_Lue,  Nko,  Ogham,  Ol_Chiki,  Old_Italic,   Old_North_Arabian,
       Old_Permic, Old_Persian, Old_South_Arabian, Old_Turkic, Oriya, Osmanya,
       Pahawh_Hmong,    Palmyrene,    Pau_Cin_Hau,    Phags_Pa,    Phoenician,
       Psalter_Pahlavi,  Rejang,  Runic,  Samaritan, Saurashtra, Sharada, Sha-
       vian, Siddham, Sinhala, Sora_Sompeng, Sundanese, Syloti_Nagri,  Syriac,
       Tagalog,  Tagbanwa,  Tai_Le,  Tai_Tham, Tai_Viet, Takri, Tamil, Telugu,
       Thaana, Thai, Tibetan, Tifinagh, Tirhuta, Ugaritic,  Vai,  Warang_Citi,
       Yi.


CHARACTER CLASSES

         [...]       positive character class
         [^...]      negative character class
         [x-y]       range (can be used for hex characters)
         [[:xxx:]]   positive POSIX named set
         [[:^xxx:]]  negative POSIX named set

         alnum       alphanumeric
         alpha       alphabetic
         ascii       0-127
         blank       space or tab
         cntrl       control character
         digit       decimal digit
         graph       printing, excluding space
         lower       lower case letter
         print       printing, including space
         punct       printing, excluding alphanumeric
         space       white space
         upper       upper case letter
         word        same as \w
         xdigit      hexadecimal digit

       In  PCRE,  POSIX character set names recognize only ASCII characters by
       default, but some of them use Unicode properties if  PCRE_UCP  is  set.
       You can use \Q...\E inside a character class.


QUANTIFIERS

         ?           0 or 1, greedy
         ?+          0 or 1, possessive
         ??          0 or 1, lazy
         *           0 or more, greedy
         *+          0 or more, possessive
         *?          0 or more, lazy
         +           1 or more, greedy
         ++          1 or more, possessive
         +?          1 or more, lazy
         {n}         exactly n
         {n,m}       at least n, no more than m, greedy
         {n,m}+      at least n, no more than m, possessive
         {n,m}?      at least n, no more than m, lazy
         {n,}        n or more, greedy
         {n,}+       n or more, possessive
         {n,}?       n or more, lazy


ANCHORS AND SIMPLE ASSERTIONS

         \b          word boundary
         \B          not a word boundary
         ^           start of subject
                      also after internal newline in multiline mode
         \A          start of subject
         $           end of subject
                      also before newline at end of subject
                      also before internal newline in multiline mode
         \Z          end of subject
                      also before newline at end of subject
         \z          end of subject
         \G          first matching position in subject


MATCH POINT RESET

         \K          reset start of match

       \K is honoured in positive assertions, but ignored in negative ones.


ALTERNATION

         expr|expr|expr...


CAPTURING

         (...)           capturing group
         (?<name>...)    named capturing group (Perl)
         (?'name'...)    named capturing group (Perl)
         (?P<name>...)   named capturing group (Python)
         (?:...)         non-capturing group
         (?|...)         non-capturing group; reset group numbers for
                          capturing groups in each alternative


ATOMIC GROUPS

         (?>...)         atomic, non-capturing group


COMMENT

         (?#....)        comment (not nestable)


OPTION SETTING

         (?i)            caseless
         (?J)            allow duplicate names
         (?m)            multiline
         (?s)            single line (dotall)
         (?U)            default ungreedy (lazy)
         (?x)            extended (ignore white space)
         (?-...)         unset option(s)

       The  following  are  recognized  only at the very start of a pattern or
       after one of the newline or \R options with similar syntax.  More  than
       one of them may appear.

         (*LIMIT_MATCH=d) set the match limit to d (decimal number)
         (*LIMIT_RECURSION=d) set the recursion limit to d (decimal number)
         (*NO_AUTO_POSSESS) no auto-possessification (PCRE_NO_AUTO_POSSESS)
         (*NO_START_OPT) no start-match optimization (PCRE_NO_START_OPTIMIZE)
         (*UTF8)         set UTF-8 mode: 8-bit library (PCRE_UTF8)
         (*UTF16)        set UTF-16 mode: 16-bit library (PCRE_UTF16)
         (*UTF32)        set UTF-32 mode: 32-bit library (PCRE_UTF32)
         (*UTF)          set appropriate UTF mode for the library in use
         (*UCP)          set PCRE_UCP (use Unicode properties for \d etc)

       Note  that LIMIT_MATCH and LIMIT_RECURSION can only reduce the value of
       the limits set by the caller of pcre_exec(), not increase them.


NEWLINE CONVENTION

       These are recognized only at the very start of  the  pattern  or  after
       option settings with a similar syntax.

         (*CR)           carriage return only
         (*LF)           linefeed only
         (*CRLF)         carriage return followed by linefeed
         (*ANYCRLF)      all three of the above
         (*ANY)          any Unicode newline sequence


WHAT \R MATCHES

       These  are  recognized  only  at the very start of the pattern or after
       option setting with a similar syntax.

         (*BSR_ANYCRLF)  CR, LF, or CRLF
         (*BSR_UNICODE)  any Unicode newline sequence


LOOKAHEAD AND LOOKBEHIND ASSERTIONS

         (?=...)         positive look ahead
         (?!...)         negative look ahead
         (?<=...)        positive look behind
         (?<!...)        negative look behind

       Each top-level branch of a look behind must be of a fixed length.


BACKREFERENCES

         \n              reference by number (can be ambiguous)
         \gn             reference by number
         \g{n}           reference by number
         \g{-n}          relative reference by number
         \k<name>        reference by name (Perl)
         \k'name'        reference by name (Perl)
         \g{name}        reference by name (Perl)
         \k{name}        reference by name (.NET)
         (?P=name)       reference by name (Python)


SUBROUTINE REFERENCES (POSSIBLY RECURSIVE)

         (?R)            recurse whole pattern
         (?n)            call subpattern by absolute number
         (?+n)           call subpattern by relative number
         (?-n)           call subpattern by relative number
         (?&name)        call subpattern by name (Perl)
         (?P>name)       call subpattern by name (Python)
         \g<name>        call subpattern by name (Oniguruma)
         \g'name'        call subpattern by name (Oniguruma)
         \g<n>           call subpattern by absolute number (Oniguruma)
         \g'n'           call subpattern by absolute number (Oniguruma)
         \g<+n>          call subpattern by relative number (PCRE extension)
         \g'+n'          call subpattern by relative number (PCRE extension)
         \g<-n>          call subpattern by relative number (PCRE extension)
         \g'-n'          call subpattern by relative number (PCRE extension)


CONDITIONAL PATTERNS

         (?(condition)yes-pattern)
         (?(condition)yes-pattern|no-pattern)

         (?(n)...        absolute reference condition
         (?(+n)...       relative reference condition
         (?(-n)...       relative reference condition
         (?(<name>)...   named reference condition (Perl)
         (?('name')...   named reference condition (Perl)
         (?(name)...     named reference condition (PCRE)
         (?(R)...        overall recursion condition
         (?(Rn)...       specific group recursion condition
         (?(R&name)...   specific recursion condition
         (?(DEFINE)...   define subpattern for reference
         (?(assert)...   assertion condition


BACKTRACKING CONTROL

       The following act immediately they are reached:

         (*ACCEPT)       force successful match
         (*FAIL)         force backtrack; synonym (*F)
         (*MARK:NAME)    set name to be passed back; synonym (*:NAME)

       The following act only when a subsequent match failure causes  a  back-
       track to reach them. They all force a match failure, but they differ in
       what happens afterwards. Those that advance the start-of-match point do
       so only if the pattern is not anchored.

         (*COMMIT)       overall failure, no advance of starting point
         (*PRUNE)        advance to next starting character
         (*PRUNE:NAME)   equivalent to (*MARK:NAME)(*PRUNE)
         (*SKIP)         advance to current matching position
         (*SKIP:NAME)    advance to position corresponding to an earlier
                         (*MARK:NAME); if not found, the (*SKIP) is ignored
         (*THEN)         local failure, backtrack to next alternation
         (*THEN:NAME)    equivalent to (*MARK:NAME)(*THEN)


CALLOUTS

         (?C)      callout
         (?Cn)     callout with data n


SEE ALSO

       pcrepattern(3), pcreapi(3), pcrecallout(3), pcrematching(3), pcre(3).


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 08 January 2014
       Copyright (c) 1997-2014 University of Cambridge.
------------------------------------------------------------------------------


PCREUNICODE(3)             Library Functions Manual             PCREUNICODE(3)



NAME
       PCRE - Perl-compatible regular expressions

UTF-8, UTF-16, UTF-32, AND UNICODE PROPERTY SUPPORT

       As well as UTF-8 support, PCRE also supports UTF-16 (from release 8.30)
       and UTF-32 (from release 8.32), by means of two  additional  libraries.
       They can be built as well as, or instead of, the 8-bit library.


UTF-8 SUPPORT

       In  order  process  UTF-8  strings, you must build PCRE's 8-bit library
       with UTF support, and, in addition, you must call  pcre_compile()  with
       the  PCRE_UTF8 option flag, or the pattern must start with the sequence
       (*UTF8) or (*UTF). When either of these is the case, both  the  pattern
       and  any  subject  strings  that  are matched against it are treated as
       UTF-8 strings instead of strings of individual 1-byte characters.


UTF-16 AND UTF-32 SUPPORT

       In order process UTF-16 or UTF-32 strings, you must build PCRE's 16-bit
       or  32-bit  library  with  UTF support, and, in addition, you must call
       pcre16_compile() or pcre32_compile() with the PCRE_UTF16 or  PCRE_UTF32
       option flag, as appropriate. Alternatively, the pattern must start with
       the sequence (*UTF16), (*UTF32), as appropriate, or (*UTF),  which  can
       be used with either library. When UTF mode is set, both the pattern and
       any subject strings that are matched against it are treated  as  UTF-16
       or  UTF-32  strings  instead  of strings of individual 16-bit or 32-bit
       characters.


UTF SUPPORT OVERHEAD

       If you compile PCRE with UTF support, but do not use it  at  run  time,
       the  library will be a bit bigger, but the additional run time overhead
       is limited to  testing  the  PCRE_UTF[8|16|32]  flag  occasionally,  so
       should not be very big.


UNICODE PROPERTY SUPPORT

       If PCRE is built with Unicode character property support (which implies
       UTF support), the escape sequences \p{..}, \P{..}, and \X can be  used.
       The  available properties that can be tested are limited to the general
       category properties such as Lu for an upper case letter  or  Nd  for  a
       decimal number, the Unicode script names such as Arabic or Han, and the
       derived properties Any and L&. Full lists is given in  the  pcrepattern
       and  pcresyntax  documentation. Only the short names for properties are
       supported. For example, \p{L}  matches  a  letter.  Its  Perl  synonym,
       \p{Letter},  is  not  supported.  Furthermore, in Perl, many properties
       may optionally be prefixed by "Is", for compatibility  with  Perl  5.6.
       PCRE does not support this.

   Validity of UTF-8 strings

       When  you  set  the PCRE_UTF8 flag, the byte strings passed as patterns
       and subjects are (by default) checked for validity on entry to the rel-
       evant functions. The entire string is checked before any other process-
       ing takes place. From release 7.3 of PCRE, the check is  according  the
       rules of RFC 3629, which are themselves derived from the Unicode speci-
       fication. Earlier releases of PCRE followed  the  rules  of  RFC  2279,
       which  allows  the  full  range of 31-bit values (0 to 0x7FFFFFFF). The
       current check allows only values in the range U+0 to U+10FFFF,  exclud-
       ing  the  surrogate area. (From release 8.33 the so-called "non-charac-
       ter" code points are no longer excluded because Unicode corrigendum  #9
       makes it clear that they should not be.)

       Characters  in  the "Surrogate Area" of Unicode are reserved for use by
       UTF-16, where they are used in pairs to encode codepoints  with  values
       greater  than  0xFFFF. The code points that are encoded by UTF-16 pairs
       are available independently in the  UTF-8  and  UTF-32  encodings.  (In
       other  words,  the  whole  surrogate  thing is a fudge for UTF-16 which
       unfortunately messes up UTF-8 and UTF-32.)

       If an invalid UTF-8 string is passed to PCRE, an error return is given.
       At  compile  time, the only additional information is the offset to the
       first byte of the failing character. The run-time functions pcre_exec()
       and  pcre_dfa_exec() also pass back this information, as well as a more
       detailed reason code if the caller has provided memory in which  to  do
       this.

       In  some  situations, you may already know that your strings are valid,
       and therefore want to skip these checks in  order  to  improve  perfor-
       mance,  for  example in the case of a long subject string that is being
       scanned repeatedly.  If you set the PCRE_NO_UTF8_CHECK flag at  compile
       time  or  at  run  time, PCRE assumes that the pattern or subject it is
       given (respectively) contains only valid UTF-8 codes. In this case,  it
       does not diagnose an invalid UTF-8 string.

       Note  that  passing  PCRE_NO_UTF8_CHECK to pcre_compile() just disables
       the check for the pattern; it does not also apply to  subject  strings.
       If  you  want  to  disable the check for a subject string you must pass
       this option to pcre_exec() or pcre_dfa_exec().

       If you pass an invalid UTF-8 string when PCRE_NO_UTF8_CHECK is set, the
       result is undefined and your program may crash.

   Validity of UTF-16 strings

       When you set the PCRE_UTF16 flag, the strings of 16-bit data units that
       are passed as patterns and subjects are (by default) checked for valid-
       ity  on entry to the relevant functions. Values other than those in the
       surrogate range U+D800 to U+DFFF are independent code points. Values in
       the surrogate range must be used in pairs in the correct manner.

       If  an  invalid  UTF-16  string  is  passed to PCRE, an error return is
       given. At compile time, the only additional information is  the  offset
       to the first data unit of the failing character. The run-time functions
       pcre16_exec() and pcre16_dfa_exec() also pass back this information, as
       well  as  a more detailed reason code if the caller has provided memory
       in which to do this.

       In some situations, you may already know that your strings  are  valid,
       and  therefore  want  to  skip these checks in order to improve perfor-
       mance. If you set the PCRE_NO_UTF16_CHECK flag at compile  time  or  at
       run time, PCRE assumes that the pattern or subject it is given (respec-
       tively) contains only valid UTF-16 sequences. In this case, it does not
       diagnose  an  invalid  UTF-16 string.  However, if an invalid string is
       passed, the result is undefined.

   Validity of UTF-32 strings

       When you set the PCRE_UTF32 flag, the strings of 32-bit data units that
       are passed as patterns and subjects are (by default) checked for valid-
       ity on entry to the relevant functions.  This check allows only  values
       in  the  range  U+0 to U+10FFFF, excluding the surrogate area U+D800 to
       U+DFFF.

       If an invalid UTF-32 string is passed  to  PCRE,  an  error  return  is
       given.  At  compile time, the only additional information is the offset
       to the first data unit of the failing character. The run-time functions
       pcre32_exec() and pcre32_dfa_exec() also pass back this information, as
       well as a more detailed reason code if the caller has  provided  memory
       in which to do this.

       In  some  situations, you may already know that your strings are valid,
       and therefore want to skip these checks in  order  to  improve  perfor-
       mance.  If  you  set the PCRE_NO_UTF32_CHECK flag at compile time or at
       run time, PCRE assumes that the pattern or subject it is given (respec-
       tively) contains only valid UTF-32 sequences. In this case, it does not
       diagnose an invalid UTF-32 string.  However, if an  invalid  string  is
       passed, the result is undefined.

   General comments about UTF modes

       1.  Codepoints  less  than  256  can be specified in patterns by either
       braced or unbraced hexadecimal escape sequences (for example, \x{b3} or
       \xb3). Larger values have to use braced sequences.

       2.  Octal  numbers  up  to  \777 are recognized, and in UTF-8 mode they
       match two-byte characters for values greater than \177.

       3. Repeat quantifiers apply to complete UTF characters, not to individ-
       ual data units, for example: \x{100}{3}.

       4.  The dot metacharacter matches one UTF character instead of a single
       data unit.

       5. The escape sequence \C can be used to match a single byte  in  UTF-8
       mode,  or  a single 16-bit data unit in UTF-16 mode, or a single 32-bit
       data unit in UTF-32 mode, but its use can lead to some strange  effects
       because  it  breaks up multi-unit characters (see the description of \C
       in the pcrepattern documentation). The use of \C is  not  supported  in
       the  alternative  matching  function  pcre[16|32]_dfa_exec(), nor is it
       supported in UTF mode by the JIT optimization of pcre[16|32]_exec(). If
       JIT  optimization  is  requested for a UTF pattern that contains \C, it
       will not succeed, and so the matching will be carried out by the normal
       interpretive function.

       6.  The  character escapes \b, \B, \d, \D, \s, \S, \w, and \W correctly
       test characters of any code value, but, by default, the characters that
       PCRE  recognizes  as digits, spaces, or word characters remain the same
       set as in non-UTF mode, all with values less  than  256.  This  remains
       true  even  when  PCRE  is  built  to include Unicode property support,
       because to do otherwise would slow down PCRE in many common cases. Note
       in  particular that this applies to \b and \B, because they are defined
       in terms of \w and \W. If you really want to test for a wider sense of,
       say,  "digit",  you  can  use  explicit  Unicode property tests such as
       \p{Nd}. Alternatively, if you set the PCRE_UCP option, the way that the
       character  escapes  work is changed so that Unicode properties are used
       to determine which characters match. There are more details in the sec-
       tion on generic character types in the pcrepattern documentation.

       7.  Similarly,  characters that match the POSIX named character classes
       are all low-valued characters, unless the PCRE_UCP option is set.

       8. However, the horizontal and vertical white  space  matching  escapes
       (\h,  \H,  \v, and \V) do match all the appropriate Unicode characters,
       whether or not PCRE_UCP is set.

       9. Case-insensitive matching applies only to  characters  whose  values
       are  less than 128, unless PCRE is built with Unicode property support.
       A few Unicode characters such as Greek sigma have more than  two  code-
       points that are case-equivalent. Up to and including PCRE release 8.31,
       only one-to-one case mappings were supported, but later releases  (with
       Unicode  property  support) do treat as case-equivalent all versions of
       characters such as Greek sigma.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 27 February 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREJIT(3)                 Library Functions Manual                 PCREJIT(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE JUST-IN-TIME COMPILER SUPPORT

       Just-in-time  compiling  is a heavyweight optimization that can greatly
       speed up pattern matching. However, it comes at the cost of extra  pro-
       cessing before the match is performed. Therefore, it is of most benefit
       when the same pattern is going to be matched many times. This does  not
       necessarily  mean  many calls of a matching function; if the pattern is
       not anchored, matching attempts may take place many  times  at  various
       positions  in  the  subject, even for a single call.  Therefore, if the
       subject string is very long, it may still pay to use  JIT  for  one-off
       matches.

       JIT  support  applies  only to the traditional Perl-compatible matching
       function.  It does not apply when the DFA matching  function  is  being
       used. The code for this support was written by Zoltan Herczeg.


8-BIT, 16-BIT AND 32-BIT SUPPORT

       JIT  support  is available for all of the 8-bit, 16-bit and 32-bit PCRE
       libraries. To keep this documentation simple, only the 8-bit  interface
       is described in what follows. If you are using the 16-bit library, sub-
       stitute the  16-bit  functions  and  16-bit  structures  (for  example,
       pcre16_jit_stack  instead  of  pcre_jit_stack).  If  you  are using the
       32-bit library, substitute the 32-bit functions and  32-bit  structures
       (for example, pcre32_jit_stack instead of pcre_jit_stack).


AVAILABILITY OF JIT SUPPORT

       JIT  support  is  an  optional  feature of PCRE. The "configure" option
       --enable-jit (or equivalent CMake option) must  be  set  when  PCRE  is
       built  if  you want to use JIT. The support is limited to the following
       hardware platforms:

         ARM v5, v7, and Thumb2
         Intel x86 32-bit and 64-bit
         MIPS 32-bit
         Power PC 32-bit and 64-bit
         SPARC 32-bit (experimental)

       If --enable-jit is set on an unsupported platform, compilation fails.

       A program that is linked with PCRE 8.20 or later can tell if  JIT  sup-
       port  is  available  by  calling pcre_config() with the PCRE_CONFIG_JIT
       option. The result is 1 when JIT is available, and  0  otherwise.  How-
       ever, a simple program does not need to check this in order to use JIT.
       The normal API is implemented in a way that falls back to the interpre-
       tive code if JIT is not available. For programs that need the best pos-
       sible performance, there is also a "fast path"  API  that  is  JIT-spe-
       cific.

       If  your program may sometimes be linked with versions of PCRE that are
       older than 8.20, but you want to use JIT when it is available, you  can
       test the values of PCRE_MAJOR and PCRE_MINOR, or the existence of a JIT
       macro such as PCRE_CONFIG_JIT, for compile-time control of your code.


SIMPLE USE OF JIT

       You have to do two things to make use of the JIT support  in  the  sim-
       plest way:

         (1) Call pcre_study() with the PCRE_STUDY_JIT_COMPILE option for
             each compiled pattern, and pass the resulting pcre_extra block to
             pcre_exec().

         (2) Use pcre_free_study() to free the pcre_extra block when it is
             no  longer  needed,  instead  of  just  freeing it yourself. This
       ensures that
             any JIT data is also freed.

       For a program that may be linked with pre-8.20 versions  of  PCRE,  you
       can insert

         #ifndef PCRE_STUDY_JIT_COMPILE
         #define PCRE_STUDY_JIT_COMPILE 0
         #endif

       so  that  no  option  is passed to pcre_study(), and then use something
       like this to free the study data:

         #ifdef PCRE_CONFIG_JIT
             pcre_free_study(study_ptr);
         #else
             pcre_free(study_ptr);
         #endif

       PCRE_STUDY_JIT_COMPILE requests the JIT compiler to generate  code  for
       complete  matches.  If  you  want  to  run  partial  matches  using the
       PCRE_PARTIAL_HARD or  PCRE_PARTIAL_SOFT  options  of  pcre_exec(),  you
       should  set  one  or  both  of the following options in addition to, or
       instead of, PCRE_STUDY_JIT_COMPILE when you call pcre_study():

         PCRE_STUDY_JIT_PARTIAL_HARD_COMPILE
         PCRE_STUDY_JIT_PARTIAL_SOFT_COMPILE

       The JIT compiler generates different optimized code  for  each  of  the
       three  modes  (normal, soft partial, hard partial). When pcre_exec() is
       called, the appropriate code is run if it is available. Otherwise,  the
       pattern is matched using interpretive code.

       In  some circumstances you may need to call additional functions. These
       are described in the  section  entitled  "Controlling  the  JIT  stack"
       below.

       If  JIT  support  is  not  available,  PCRE_STUDY_JIT_COMPILE  etc. are
       ignored, and no JIT data is created. Otherwise, the compiled pattern is
       passed  to the JIT compiler, which turns it into machine code that exe-
       cutes much faster than the normal interpretive code.  When  pcre_exec()
       is  passed  a  pcre_extra block containing a pointer to JIT code of the
       appropriate mode (normal or hard/soft  partial),  it  obeys  that  code
       instead  of  running  the interpreter. The result is identical, but the
       compiled JIT code runs much faster.

       There are some pcre_exec() options that are not supported for JIT  exe-
       cution.  There  are  also  some  pattern  items that JIT cannot handle.
       Details are given below. In both cases, execution  automatically  falls
       back  to  the  interpretive  code.  If you want to know whether JIT was
       actually used for a particular match, you  should  arrange  for  a  JIT
       callback  function  to  be  set up as described in the section entitled
       "Controlling the JIT stack" below, even if you do not need to supply  a
       non-default  JIT stack. Such a callback function is called whenever JIT
       code is about to be obeyed. If the execution options are not right  for
       JIT execution, the callback function is not obeyed.

       If  the  JIT  compiler finds an unsupported item, no JIT data is gener-
       ated. You can find out if JIT execution is available after  studying  a
       pattern  by  calling  pcre_fullinfo()  with the PCRE_INFO_JIT option. A
       result of 1 means that JIT compilation was successful. A  result  of  0
       means that JIT support is not available, or the pattern was not studied
       with PCRE_STUDY_JIT_COMPILE etc., or the JIT compiler was not  able  to
       handle the pattern.

       Once a pattern has been studied, with or without JIT, it can be used as
       many times as you like for matching different subject strings.


UNSUPPORTED OPTIONS AND PATTERN ITEMS

       The only pcre_exec() options that are supported for JIT  execution  are
       PCRE_NO_UTF8_CHECK, PCRE_NO_UTF16_CHECK, PCRE_NO_UTF32_CHECK, PCRE_NOT-
       BOL,  PCRE_NOTEOL,  PCRE_NOTEMPTY,   PCRE_NOTEMPTY_ATSTART,   PCRE_PAR-
       TIAL_HARD, and PCRE_PARTIAL_SOFT.

       The  only  unsupported  pattern items are \C (match a single data unit)
       when running in a UTF mode, and a callout immediately before an  asser-
       tion condition in a conditional group.


RETURN VALUES FROM JIT EXECUTION

       When  a  pattern  is matched using JIT execution, the return values are
       the same as those given by the interpretive pcre_exec() code, with  the
       addition  of  one new error code: PCRE_ERROR_JIT_STACKLIMIT. This means
       that the memory used for the JIT stack was insufficient. See  "Control-
       ling the JIT stack" below for a discussion of JIT stack usage. For com-
       patibility with the interpretive pcre_exec() code, no  more  than  two-
       thirds  of  the ovector argument is used for passing back captured sub-
       strings.

       The error code PCRE_ERROR_MATCHLIMIT is returned by  the  JIT  code  if
       searching  a  very large pattern tree goes on for too long, as it is in
       the same circumstance when JIT is not used, but the details of  exactly
       what  is  counted are not the same. The PCRE_ERROR_RECURSIONLIMIT error
       code is never returned by JIT execution.


SAVING AND RESTORING COMPILED PATTERNS

       The code that is generated by the  JIT  compiler  is  architecture-spe-
       cific,  and  is also position dependent. For those reasons it cannot be
       saved (in a file or database) and restored later like the bytecode  and
       other  data  of  a compiled pattern. Saving and restoring compiled pat-
       terns is not something many people do. More detail about this  facility
       is  given in the pcreprecompile documentation. It should be possible to
       run pcre_study() on a saved and restored pattern, and thereby  recreate
       the  JIT  data, but because JIT compilation uses significant resources,
       it is probably not worth doing this; you might as  well  recompile  the
       original pattern.


CONTROLLING THE JIT STACK

       When the compiled JIT code runs, it needs a block of memory to use as a
       stack.  By default, it uses 32K on the  machine  stack.  However,  some
       large   or   complicated  patterns  need  more  than  this.  The  error
       PCRE_ERROR_JIT_STACKLIMIT is given when  there  is  not  enough  stack.
       Three  functions  are provided for managing blocks of memory for use as
       JIT stacks. There is further discussion about the use of JIT stacks  in
       the section entitled "JIT stack FAQ" below.

       The  pcre_jit_stack_alloc() function creates a JIT stack. Its arguments
       are a starting size and a maximum size, and it returns a pointer to  an
       opaque  structure of type pcre_jit_stack, or NULL if there is an error.
       The pcre_jit_stack_free() function can be used to free a stack that  is
       no  longer  needed.  (For  the technically minded: the address space is
       allocated by mmap or VirtualAlloc.)

       JIT uses far less memory for recursion than the interpretive code,  and
       a  maximum  stack size of 512K to 1M should be more than enough for any
       pattern.

       The pcre_assign_jit_stack() function specifies  which  stack  JIT  code
       should use. Its arguments are as follows:

         pcre_extra         *extra
         pcre_jit_callback  callback
         void               *data

       The  extra  argument  must  be  the  result  of studying a pattern with
       PCRE_STUDY_JIT_COMPILE etc. There are three cases for the values of the
       other two options:

         (1) If callback is NULL and data is NULL, an internal 32K block
             on the machine stack is used.

         (2) If callback is NULL and data is not NULL, data must be
             a valid JIT stack, the result of calling pcre_jit_stack_alloc().

         (3) If callback is not NULL, it must point to a function that is
             called with data as an argument at the start of matching, in
             order to set up a JIT stack. If the return from the callback
             function is NULL, the internal 32K stack is used; otherwise the
             return value must be a valid JIT stack, the result of calling
             pcre_jit_stack_alloc().

       A  callback function is obeyed whenever JIT code is about to be run; it
       is not obeyed when pcre_exec() is called with options that  are  incom-
       patible for JIT execution. A callback function can therefore be used to
       determine whether a match operation was  executed  by  JIT  or  by  the
       interpreter.

       You may safely use the same JIT stack for more than one pattern (either
       by assigning directly or by callback), as long as the patterns are  all
       matched  sequentially in the same thread. In a multithread application,
       if you do not specify a JIT stack, or if you assign or pass  back  NULL
       from  a  callback, that is thread-safe, because each thread has its own
       machine stack. However, if you assign  or  pass  back  a  non-NULL  JIT
       stack,  this  must  be  a  different  stack for each thread so that the
       application is thread-safe.

       Strictly speaking, even more is allowed. You can assign the  same  non-
       NULL  stack  to any number of patterns as long as they are not used for
       matching by multiple threads at the same time.  For  example,  you  can
       assign  the same stack to all compiled patterns, and use a global mutex
       in the callback to wait until the stack is available for use.  However,
       this is an inefficient solution, and not recommended.

       This  is a suggestion for how a multithreaded program that needs to set
       up non-default JIT stacks might operate:

         During thread initalization
           thread_local_var = pcre_jit_stack_alloc(...)

         During thread exit
           pcre_jit_stack_free(thread_local_var)

         Use a one-line callback function
           return thread_local_var

       All the functions described in this section do nothing if  JIT  is  not
       available,  and  pcre_assign_jit_stack()  does nothing unless the extra
       argument is non-NULL and points to  a  pcre_extra  block  that  is  the
       result of a successful study with PCRE_STUDY_JIT_COMPILE etc.


JIT STACK FAQ

       (1) Why do we need JIT stacks?

       PCRE  (and JIT) is a recursive, depth-first engine, so it needs a stack
       where the local data of the current node is pushed before checking  its
       child nodes.  Allocating real machine stack on some platforms is diffi-
       cult. For example, the stack chain needs to be updated every time if we
       extend  the  stack  on  PowerPC.  Although it is possible, its updating
       time overhead decreases performance. So we do the recursion in memory.

       (2) Why don't we simply allocate blocks of memory with malloc()?

       Modern operating systems have a  nice  feature:  they  can  reserve  an
       address space instead of allocating memory. We can safely allocate mem-
       ory pages inside this address space, so the stack  could  grow  without
       moving memory data (this is important because of pointers). Thus we can
       allocate 1M address space, and use only a single memory  page  (usually
       4K)  if  that is enough. However, we can still grow up to 1M anytime if
       needed.

       (3) Who "owns" a JIT stack?

       The owner of the stack is the user program, not the JIT studied pattern
       or  anything else. The user program must ensure that if a stack is used
       by pcre_exec(), (that is, it is assigned to the pattern currently  run-
       ning), that stack must not be used by any other threads (to avoid over-
       writing the same memory area). The best practice for multithreaded pro-
       grams  is  to  allocate  a stack for each thread, and return this stack
       through the JIT callback function.

       (4) When should a JIT stack be freed?

       You can free a JIT stack at any time, as long as it will not be used by
       pcre_exec()  again.  When  you  assign  the  stack to a pattern, only a
       pointer is set. There is no reference counting or any other magic.  You
       can  free  the  patterns  and stacks in any order, anytime. Just do not
       call pcre_exec() with a pattern pointing to an already freed stack,  as
       that  will cause SEGFAULT. (Also, do not free a stack currently used by
       pcre_exec() in another thread). You can also replace the  stack  for  a
       pattern  at  any  time.  You  can  even  free the previous stack before
       assigning a replacement.

       (5) Should I allocate/free a  stack  every  time  before/after  calling
       pcre_exec()?

       No,  because  this  is  too  costly in terms of resources. However, you
       could implement some clever idea which release the stack if it  is  not
       used  in  let's  say  two minutes. The JIT callback can help to achieve
       this without keeping a list of the currently JIT studied patterns.

       (6) OK, the stack is for long term memory allocation. But what  happens
       if  a pattern causes stack overflow with a stack of 1M? Is that 1M kept
       until the stack is freed?

       Especially on embedded sytems, it might be a good idea to release  mem-
       ory  sometimes  without  freeing the stack. There is no API for this at
       the moment.  Probably a function call which returns with the  currently
       allocated  memory for any stack and another which allows releasing mem-
       ory (shrinking the stack) would be a good idea if someone needs this.

       (7) This is too much of a headache. Isn't there any better solution for
       JIT stack handling?

       No,  thanks to Windows. If POSIX threads were used everywhere, we could
       throw out this complicated API.


EXAMPLE CODE

       This is a single-threaded example that specifies a  JIT  stack  without
       using a callback.

         int rc;
         int ovector[30];
         pcre *re;
         pcre_extra *extra;
         pcre_jit_stack *jit_stack;

         re = pcre_compile(pattern, 0, &error, &erroffset, NULL);
         /* Check for errors */
         extra = pcre_study(re, PCRE_STUDY_JIT_COMPILE, &error);
         jit_stack = pcre_jit_stack_alloc(32*1024, 512*1024);
         /* Check for error (NULL) */
         pcre_assign_jit_stack(extra, NULL, jit_stack);
         rc = pcre_exec(re, extra, subject, length, 0, 0, ovector, 30);
         /* Check results */
         pcre_free(re);
         pcre_free_study(extra);
         pcre_jit_stack_free(jit_stack);


JIT FAST PATH API

       Because  the  API  described  above falls back to interpreted execution
       when JIT is not available, it is convenient for programs that are writ-
       ten  for  general  use  in  many environments. However, calling JIT via
       pcre_exec() does have a performance impact. Programs that  are  written
       for  use  where  JIT  is known to be available, and which need the best
       possible performance, can instead use a "fast path"  API  to  call  JIT
       execution  directly  instead of calling pcre_exec() (obviously only for
       patterns that have been successfully studied by JIT).

       The fast path function is called pcre_jit_exec(), and it takes  exactly
       the  same  arguments  as pcre_exec(), plus one additional argument that
       must point to a JIT stack. The JIT stack arrangements  described  above
       do not apply. The return values are the same as for pcre_exec().

       When  you  call  pcre_exec(), as well as testing for invalid options, a
       number of other sanity checks are performed on the arguments. For exam-
       ple,  if  the  subject  pointer  is NULL, or its length is negative, an
       immediate error is given. Also, unless PCRE_NO_UTF[8|16|32] is  set,  a
       UTF  subject  string is tested for validity. In the interests of speed,
       these checks do not happen on the JIT fast path, and if invalid data is
       passed, the result is undefined.

       Bypassing  the  sanity  checks  and  the  pcre_exec() wrapping can give
       speedups of more than 10%.


SEE ALSO

       pcreapi(3)


AUTHOR

       Philip Hazel (FAQ by Zoltan Herczeg)
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 17 March 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREPARTIAL(3)             Library Functions Manual             PCREPARTIAL(3)



NAME
       PCRE - Perl-compatible regular expressions

PARTIAL MATCHING IN PCRE

       In normal use of PCRE, if the subject string that is passed to a match-
       ing function matches as far as it goes, but is too short to  match  the
       entire pattern, PCRE_ERROR_NOMATCH is returned. There are circumstances
       where it might be helpful to distinguish this case from other cases  in
       which there is no match.

       Consider, for example, an application where a human is required to type
       in data for a field with specific formatting requirements.  An  example
       might be a date in the form ddmmmyy, defined by this pattern:

         ^\d?\d(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d\d$

       If the application sees the user's keystrokes one by one, and can check
       that what has been typed so far is potentially valid,  it  is  able  to
       raise  an  error  as  soon  as  a  mistake  is made, by beeping and not
       reflecting the character that has been typed, for example. This immedi-
       ate  feedback is likely to be a better user interface than a check that
       is delayed until the entire string has been entered.  Partial  matching
       can  also be useful when the subject string is very long and is not all
       available at once.

       PCRE supports partial matching by means of  the  PCRE_PARTIAL_SOFT  and
       PCRE_PARTIAL_HARD  options,  which  can  be set when calling any of the
       matching functions. For backwards compatibility, PCRE_PARTIAL is a syn-
       onym  for  PCRE_PARTIAL_SOFT.  The essential difference between the two
       options is whether or not a partial match is preferred to  an  alterna-
       tive complete match, though the details differ between the two types of
       matching function. If both options  are  set,  PCRE_PARTIAL_HARD  takes
       precedence.

       If  you  want to use partial matching with just-in-time optimized code,
       you must call pcre_study(), pcre16_study() or  pcre32_study() with  one
       or both of these options:

         PCRE_STUDY_JIT_PARTIAL_SOFT_COMPILE
         PCRE_STUDY_JIT_PARTIAL_HARD_COMPILE

       PCRE_STUDY_JIT_COMPILE  should also be set if you are going to run non-
       partial matches on the same pattern. If the appropriate JIT study  mode
       has not been set for a match, the interpretive matching code is used.

       Setting a partial matching option disables two of PCRE's standard opti-
       mizations. PCRE remembers the last literal data unit in a pattern,  and
       abandons  matching  immediately  if  it  is  not present in the subject
       string. This optimization cannot be used  for  a  subject  string  that
       might  match only partially. If the pattern was studied, PCRE knows the
       minimum length of a matching string, and does not  bother  to  run  the
       matching  function  on  shorter strings. This optimization is also dis-
       abled for partial matching.


PARTIAL MATCHING USING pcre_exec() OR pcre[16|32]_exec()

       A  partial   match   occurs   during   a   call   to   pcre_exec()   or
       pcre[16|32]_exec()  when  the end of the subject string is reached suc-
       cessfully, but matching cannot continue  because  more  characters  are
       needed.   However, at least one character in the subject must have been
       inspected. This character need not  form  part  of  the  final  matched
       string;  lookbehind  assertions and the \K escape sequence provide ways
       of inspecting characters before the start of a matched  substring.  The
       requirement  for  inspecting  at  least one character exists because an
       empty string can always be matched; without such  a  restriction  there
       would  always  be  a partial match of an empty string at the end of the
       subject.

       If there are at least two slots in the offsets vector  when  a  partial
       match  is returned, the first slot is set to the offset of the earliest
       character that was inspected. For convenience, the second offset points
       to the end of the subject so that a substring can easily be identified.
       If there are at least three slots in the offsets vector, the third slot
       is set to the offset of the character where matching started.

       For the majority of patterns, the contents of the first and third slots
       will be the same. However, for patterns that contain lookbehind  asser-
       tions, or begin with \b or \B, characters before the one where matching
       started may have been inspected while carrying out the match. For exam-
       ple, consider this pattern:

         /(?<=abc)123/

       This pattern matches "123", but only if it is preceded by "abc". If the
       subject string is "xyzabc12", the first two  offsets  after  a  partial
       match  are for the substring "abc12", because all these characters were
       inspected. However, the third offset is set to 6, because that  is  the
       offset where matching began.

       What happens when a partial match is identified depends on which of the
       two partial matching options are set.

   PCRE_PARTIAL_SOFT WITH pcre_exec() OR pcre[16|32]_exec()

       If PCRE_PARTIAL_SOFT is  set  when  pcre_exec()  or  pcre[16|32]_exec()
       identifies a partial match, the partial match is remembered, but match-
       ing continues as normal, and other  alternatives  in  the  pattern  are
       tried.  If  no  complete  match  can  be  found,  PCRE_ERROR_PARTIAL is
       returned instead of PCRE_ERROR_NOMATCH.

       This option is "soft" because it prefers a complete match over  a  par-
       tial  match.   All the various matching items in a pattern behave as if
       the subject string is potentially complete. For example, \z, \Z, and  $
       match  at  the end of the subject, as normal, and for \b and \B the end
       of the subject is treated as a non-alphanumeric.

       If there is more than one partial match, the first one that  was  found
       provides the data that is returned. Consider this pattern:

         /123\w+X|dogY/

       If  this is matched against the subject string "abc123dog", both alter-
       natives fail to match, but the end of the  subject  is  reached  during
       matching,  so  PCRE_ERROR_PARTIAL is returned. The offsets are set to 3
       and 9, identifying "123dog" as the first partial match that was  found.
       (In  this  example, there are two partial matches, because "dog" on its
       own partially matches the second alternative.)

   PCRE_PARTIAL_HARD WITH pcre_exec() OR pcre[16|32]_exec()

       If PCRE_PARTIAL_HARD is  set  for  pcre_exec()  or  pcre[16|32]_exec(),
       PCRE_ERROR_PARTIAL  is  returned  as  soon as a partial match is found,
       without continuing to search for possible complete matches. This option
       is "hard" because it prefers an earlier partial match over a later com-
       plete match. For this reason, the assumption is made that  the  end  of
       the  supplied  subject  string may not be the true end of the available
       data, and so, if \z, \Z, \b, \B, or $ are encountered at the end of the
       subject,  the  result is PCRE_ERROR_PARTIAL, provided that at least one
       character in the subject has been inspected.

       Setting PCRE_PARTIAL_HARD also affects the way UTF-8 and UTF-16 subject
       strings  are checked for validity. Normally, an invalid sequence causes
       the error PCRE_ERROR_BADUTF8 or PCRE_ERROR_BADUTF16.  However,  in  the
       special  case  of  a  truncated  character  at  the end of the subject,
       PCRE_ERROR_SHORTUTF8  or   PCRE_ERROR_SHORTUTF16   is   returned   when
       PCRE_PARTIAL_HARD is set.

   Comparing hard and soft partial matching

       The  difference  between the two partial matching options can be illus-
       trated by a pattern such as:

         /dog(sbody)?/

       This matches either "dog" or "dogsbody", greedily (that is, it  prefers
       the  longer  string  if  possible). If it is matched against the string
       "dog" with PCRE_PARTIAL_SOFT, it yields a  complete  match  for  "dog".
       However, if PCRE_PARTIAL_HARD is set, the result is PCRE_ERROR_PARTIAL.
       On the other hand, if the pattern is made ungreedy the result  is  dif-
       ferent:

         /dog(sbody)??/

       In  this  case  the  result  is always a complete match because that is
       found first, and matching never  continues  after  finding  a  complete
       match. It might be easier to follow this explanation by thinking of the
       two patterns like this:

         /dog(sbody)?/    is the same as  /dogsbody|dog/
         /dog(sbody)??/   is the same as  /dog|dogsbody/

       The second pattern will never match "dogsbody", because it will  always
       find the shorter match first.


PARTIAL MATCHING USING pcre_dfa_exec() OR pcre[16|32]_dfa_exec()

       The DFA functions move along the subject string character by character,
       without backtracking, searching for  all  possible  matches  simultane-
       ously.  If the end of the subject is reached before the end of the pat-
       tern, there is the possibility of a partial match, again provided  that
       at least one character has been inspected.

       When  PCRE_PARTIAL_SOFT  is set, PCRE_ERROR_PARTIAL is returned only if
       there have been no complete matches. Otherwise,  the  complete  matches
       are  returned.   However,  if PCRE_PARTIAL_HARD is set, a partial match
       takes precedence over any complete matches. The portion of  the  string
       that  was  inspected when the longest partial match was found is set as
       the first matching string, provided there are at least two slots in the
       offsets vector.

       Because  the  DFA functions always search for all possible matches, and
       there is no difference between greedy and  ungreedy  repetition,  their
       behaviour  is  different  from  the  standard  functions when PCRE_PAR-
       TIAL_HARD is  set.  Consider  the  string  "dog"  matched  against  the
       ungreedy pattern shown above:

         /dog(sbody)??/

       Whereas  the  standard functions stop as soon as they find the complete
       match for "dog", the DFA functions also  find  the  partial  match  for
       "dogsbody", and so return that when PCRE_PARTIAL_HARD is set.


PARTIAL MATCHING AND WORD BOUNDARIES

       If  a  pattern ends with one of sequences \b or \B, which test for word
       boundaries, partial matching with PCRE_PARTIAL_SOFT can  give  counter-
       intuitive results. Consider this pattern:

         /\bcat\b/

       This matches "cat", provided there is a word boundary at either end. If
       the subject string is "the cat", the comparison of the final "t" with a
       following  character  cannot  take  place, so a partial match is found.
       However, normal matching carries on, and \b matches at the end  of  the
       subject  when  the  last  character is a letter, so a complete match is
       found.  The  result,  therefore,  is  not   PCRE_ERROR_PARTIAL.   Using
       PCRE_PARTIAL_HARD  in  this case does yield PCRE_ERROR_PARTIAL, because
       then the partial match takes precedence.


FORMERLY RESTRICTED PATTERNS

       For releases of PCRE prior to 8.00, because of the way certain internal
       optimizations   were  implemented  in  the  pcre_exec()  function,  the
       PCRE_PARTIAL option (predecessor of  PCRE_PARTIAL_SOFT)  could  not  be
       used  with all patterns. From release 8.00 onwards, the restrictions no
       longer apply, and partial matching with can be requested for  any  pat-
       tern.

       Items that were formerly restricted were repeated single characters and
       repeated metasequences. If PCRE_PARTIAL was set for a pattern that  did
       not  conform  to  the restrictions, pcre_exec() returned the error code
       PCRE_ERROR_BADPARTIAL (-13). This error code is no longer in  use.  The
       PCRE_INFO_OKPARTIAL  call  to pcre_fullinfo() to find out if a compiled
       pattern can be used for partial matching now always returns 1.


EXAMPLE OF PARTIAL MATCHING USING PCRETEST

       If the escape sequence \P is present  in  a  pcretest  data  line,  the
       PCRE_PARTIAL_SOFT  option  is  used  for  the  match.  Here is a run of
       pcretest that uses the date example quoted above:

           re> /^\d?\d(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d\d$/
         data> 25jun04\P
          0: 25jun04
          1: jun
         data> 25dec3\P
         Partial match: 23dec3
         data> 3ju\P
         Partial match: 3ju
         data> 3juj\P
         No match
         data> j\P
         No match

       The first data string is matched  completely,  so  pcretest  shows  the
       matched  substrings.  The  remaining four strings do not match the com-
       plete pattern, but the first two are partial matches. Similar output is
       obtained if DFA matching is used.

       If  the escape sequence \P is present more than once in a pcretest data
       line, the PCRE_PARTIAL_HARD option is set for the match.


MULTI-SEGMENT MATCHING WITH pcre_dfa_exec() OR pcre[16|32]_dfa_exec()

       When a partial match has been found using a DFA matching  function,  it
       is  possible to continue the match by providing additional subject data
       and calling the function again with the same compiled  regular  expres-
       sion,  this time setting the PCRE_DFA_RESTART option. You must pass the
       same working space as before, because this is where details of the pre-
       vious  partial  match  are  stored.  Here is an example using pcretest,
       using the \R escape sequence to set  the  PCRE_DFA_RESTART  option  (\D
       specifies the use of the DFA matching function):

           re> /^\d?\d(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d\d$/
         data> 23ja\P\D
         Partial match: 23ja
         data> n05\R\D
          0: n05

       The  first  call has "23ja" as the subject, and requests partial match-
       ing; the second call  has  "n05"  as  the  subject  for  the  continued
       (restarted)  match.   Notice  that when the match is complete, only the
       last part is shown; PCRE does  not  retain  the  previously  partially-
       matched  string. It is up to the calling program to do that if it needs
       to.

       That means that, for an unanchored pattern, if a continued match fails,
       it  is  not  possible  to  try  again at a new starting point. All this
       facility is capable of doing is  continuing  with  the  previous  match
       attempt.  In  the previous example, if the second set of data is "ug23"
       the result is no match, even though there would be a match for  "aug23"
       if  the entire string were given at once. Depending on the application,
       this may or may not be what you want.  The only way to allow for start-
       ing  again  at  the next character is to retain the matched part of the
       subject and try a new complete match.

       You can set the PCRE_PARTIAL_SOFT  or  PCRE_PARTIAL_HARD  options  with
       PCRE_DFA_RESTART  to  continue partial matching over multiple segments.
       This facility can be used to pass very long subject strings to the  DFA
       matching functions.


MULTI-SEGMENT MATCHING WITH pcre_exec() OR pcre[16|32]_exec()

       From  release 8.00, the standard matching functions can also be used to
       do multi-segment matching. Unlike the DFA functions, it is not possible
       to  restart the previous match with a new segment of data. Instead, new
       data must be added to the previous subject string, and the entire match
       re-run,  starting from the point where the partial match occurred. Ear-
       lier data can be discarded.

       It is best to use PCRE_PARTIAL_HARD in this situation, because it  does
       not  treat the end of a segment as the end of the subject when matching
       \z, \Z, \b, \B, and $. Consider  an  unanchored  pattern  that  matches
       dates:

           re> /\d?\d(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d\d/
         data> The date is 23ja\P\P
         Partial match: 23ja

       At  this stage, an application could discard the text preceding "23ja",
       add on text from the next  segment,  and  call  the  matching  function
       again.  Unlike  the  DFA matching functions, the entire matching string
       must always be available, and the complete matching process occurs  for
       each call, so more memory and more processing time is needed.

       Note:  If  the pattern contains lookbehind assertions, or \K, or starts
       with \b or \B, the string that is returned for a partial match includes
       characters  that precede the start of what would be returned for a com-
       plete match, because it contains all the characters that were inspected
       during the partial match.


ISSUES WITH MULTI-SEGMENT MATCHING

       Certain types of pattern may give problems with multi-segment matching,
       whichever matching function is used.

       1. If the pattern contains a test for the beginning of a line, you need
       to  pass  the  PCRE_NOTBOL  option when the subject string for any call
       does start at the beginning of a line.  There  is  also  a  PCRE_NOTEOL
       option, but in practice when doing multi-segment matching you should be
       using PCRE_PARTIAL_HARD, which includes the effect of PCRE_NOTEOL.

       2. Lookbehind assertions that have already been obeyed are catered  for
       in the offsets that are returned for a partial match. However a lookbe-
       hind assertion later in the pattern could require even earlier  charac-
       ters   to  be  inspected.  You  can  handle  this  case  by  using  the
       PCRE_INFO_MAXLOOKBEHIND    option    of    the    pcre_fullinfo()    or
       pcre[16|32]_fullinfo()  functions  to  obtain the length of the longest
       lookbehind in the pattern. This length  is  given  in  characters,  not
       bytes.  If  you  always retain at least that many characters before the
       partially matched string, all should be  well.  (Of  course,  near  the
       start of the subject, fewer characters may be present; in that case all
       characters should be retained.)

       From release 8.33, there is a more accurate way of deciding which char-
       acters  to  retain.  Instead  of  subtracting the length of the longest
       lookbehind from the  earliest  inspected  character  (offsets[0]),  the
       match  start  position  (offsets[2]) should be used, and the next match
       attempt started at the offsets[2] character by setting the  startoffset
       argument of pcre_exec() or pcre_dfa_exec().

       For  example, if the pattern "(?<=123)abc" is partially matched against
       the string "xx123a", the three offset values returned are 2, 6, and  5.
       This  indicates  that  the  matching  process that gave a partial match
       started at offset 5, but the characters "123a" were all inspected.  The
       maximum  lookbehind  for  that pattern is 3, so taking that away from 5
       shows that we need only keep "123a", and the next match attempt can  be
       started at offset 3 (that is, at "a") when further characters have been
       added. When the match start is not the  earliest  inspected  character,
       pcretest shows it explicitly:

           re> "(?<=123)abc"
         data> xx123a\P\P
         Partial match at offset 5: 123a

       3.  Because a partial match must always contain at least one character,
       what might be considered a partial match of an  empty  string  actually
       gives a "no match" result. For example:

           re> /c(?<=abc)x/
         data> ab\P
         No match

       If the next segment begins "cx", a match should be found, but this will
       only happen if characters from the previous segment are  retained.  For
       this  reason,  a  "no  match"  result should be interpreted as "partial
       match of an empty string" when the pattern contains lookbehinds.

       4. Matching a subject string that is split into multiple  segments  may
       not  always produce exactly the same result as matching over one single
       long string, especially when PCRE_PARTIAL_SOFT  is  used.  The  section
       "Partial  Matching  and  Word Boundaries" above describes an issue that
       arises if the pattern ends with \b or \B. Another  kind  of  difference
       may  occur when there are multiple matching possibilities, because (for
       PCRE_PARTIAL_SOFT) a partial match result is given only when there  are
       no completed matches. This means that as soon as the shortest match has
       been found, continuation to a new subject segment is no  longer  possi-
       ble. Consider again this pcretest example:

           re> /dog(sbody)?/
         data> dogsb\P
          0: dog
         data> do\P\D
         Partial match: do
         data> gsb\R\P\D
          0: g
         data> dogsbody\D
          0: dogsbody
          1: dog

       The  first  data  line passes the string "dogsb" to a standard matching
       function, setting the PCRE_PARTIAL_SOFT option. Although the string  is
       a  partial  match for "dogsbody", the result is not PCRE_ERROR_PARTIAL,
       because the shorter string "dog" is a complete match.  Similarly,  when
       the  subject  is  presented to a DFA matching function in several parts
       ("do" and "gsb" being the first two) the match  stops  when  "dog"  has
       been  found, and it is not possible to continue.  On the other hand, if
       "dogsbody" is presented as a single string,  a  DFA  matching  function
       finds both matches.

       Because  of  these  problems,  it is best to use PCRE_PARTIAL_HARD when
       matching multi-segment data. The example  above  then  behaves  differ-
       ently:

           re> /dog(sbody)?/
         data> dogsb\P\P
         Partial match: dogsb
         data> do\P\D
         Partial match: do
         data> gsb\R\P\P\D
         Partial match: gsb

       5. Patterns that contain alternatives at the top level which do not all
       start with the  same  pattern  item  may  not  work  as  expected  when
       PCRE_DFA_RESTART is used. For example, consider this pattern:

         1234|3789

       If  the  first  part of the subject is "ABC123", a partial match of the
       first alternative is found at offset 3. There is no partial  match  for
       the second alternative, because such a match does not start at the same
       point in the subject string. Attempting to  continue  with  the  string
       "7890"  does  not  yield  a  match because only those alternatives that
       match at one point in the subject are remembered.  The  problem  arises
       because  the  start  of the second alternative matches within the first
       alternative. There is no problem with  anchored  patterns  or  patterns
       such as:

         1234|ABCD

       where  no  string can be a partial match for both alternatives. This is
       not a problem if a standard matching  function  is  used,  because  the
       entire match has to be rerun each time:

           re> /1234|3789/
         data> ABC123\P\P
         Partial match: 123
         data> 1237890
          0: 3789

       Of course, instead of using PCRE_DFA_RESTART, the same technique of re-
       running the entire match can also be used with the DFA  matching  func-
       tions.  Another  possibility  is to work with two buffers. If a partial
       match at offset n in the first buffer is followed by  "no  match"  when
       PCRE_DFA_RESTART  is  used on the second buffer, you can then try a new
       match starting at offset n+1 in the first buffer.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 02 July 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREPRECOMPILE(3)          Library Functions Manual          PCREPRECOMPILE(3)



NAME
       PCRE - Perl-compatible regular expressions

SAVING AND RE-USING PRECOMPILED PCRE PATTERNS

       If  you  are running an application that uses a large number of regular
       expression patterns, it may be useful to store them  in  a  precompiled
       form  instead  of  having to compile them every time the application is
       run.  If you are not  using  any  private  character  tables  (see  the
       pcre_maketables()  documentation),  this is relatively straightforward.
       If you are using private tables, it is a little bit  more  complicated.
       However,  if you are using the just-in-time optimization feature, it is
       not possible to save and reload the JIT data.

       If you save compiled patterns to a file, you can copy them to a differ-
       ent host and run them there. If the two hosts have different endianness
       (byte    order),    you     should     run     the     pcre[16|32]_pat-
       tern_to_host_byte_order()  function  on  the  new host before trying to
       match the pattern. The matching functions return  PCRE_ERROR_BADENDIAN-
       NESS if they detect a pattern with the wrong endianness.

       Compiling  regular  expressions with one version of PCRE for use with a
       different version is not guaranteed to work and may cause crashes,  and
       saving  and  restoring  a  compiled  pattern loses any JIT optimization
       data.


SAVING A COMPILED PATTERN

       The value returned by pcre[16|32]_compile() points to a single block of
       memory  that  holds  the  compiled pattern and associated data. You can
       find   the   length   of   this   block    in    bytes    by    calling
       pcre[16|32]_fullinfo() with an argument of PCRE_INFO_SIZE. You can then
       save the data in any appropriate manner. Here is sample  code  for  the
       8-bit  library  that  compiles  a  pattern  and writes it to a file. It
       assumes that the variable fd refers to a file that is open for output:

         int erroroffset, rc, size;
         char *error;
         pcre *re;

         re = pcre_compile("my pattern", 0, &error, &erroroffset, NULL);
         if (re == NULL) { ... handle errors ... }
         rc = pcre_fullinfo(re, NULL, PCRE_INFO_SIZE, &size);
         if (rc < 0) { ... handle errors ... }
         rc = fwrite(re, 1, size, fd);
         if (rc != size) { ... handle errors ... }

       In this example, the bytes  that  comprise  the  compiled  pattern  are
       copied  exactly.  Note that this is binary data that may contain any of
       the 256 possible byte  values.  On  systems  that  make  a  distinction
       between binary and non-binary data, be sure that the file is opened for
       binary output.

       If you want to write more than one pattern to a file, you will have  to
       devise  a  way of separating them. For binary data, preceding each pat-
       tern with its length is probably  the  most  straightforward  approach.
       Another  possibility is to write out the data in hexadecimal instead of
       binary, one pattern to a line.

       Saving compiled patterns in a file is only one possible way of  storing
       them  for later use. They could equally well be saved in a database, or
       in the memory of some daemon process that passes them  via  sockets  to
       the processes that want them.

       If the pattern has been studied, it is also possible to save the normal
       study data in a similar way to the compiled pattern itself. However, if
       the PCRE_STUDY_JIT_COMPILE was used, the just-in-time data that is cre-
       ated cannot be saved because it is too dependent on the  current  envi-
       ronment.    When    studying    generates    additional    information,
       pcre[16|32]_study() returns  a  pointer  to  a  pcre[16|32]_extra  data
       block.  Its  format  is defined in the section on matching a pattern in
       the pcreapi documentation. The study_data field points  to  the  binary
       study  data,  and this is what you must save (not the pcre[16|32]_extra
       block itself). The length of the study data can be obtained by  calling
       pcre[16|32]_fullinfo()  with an argument of PCRE_INFO_STUDYSIZE. Remem-
       ber to check that  pcre[16|32]_study()  did  return  a  non-NULL  value
       before trying to save the study data.


RE-USING A PRECOMPILED PATTERN

       Re-using  a  precompiled pattern is straightforward. Having reloaded it
       into main memory,  called  pcre[16|32]_pattern_to_host_byte_order()  if
       necessary,    you   pass   its   pointer   to   pcre[16|32]_exec()   or
       pcre[16|32]_dfa_exec() in the usual way.

       However, if you passed a pointer to custom character  tables  when  the
       pattern  was compiled (the tableptr argument of pcre[16|32]_compile()),
       you  must  now  pass  a  similar  pointer  to   pcre[16|32]_exec()   or
       pcre[16|32]_dfa_exec(),  because the value saved with the compiled pat-
       tern will obviously be nonsense. A field in a pcre[16|32]_extra() block
       is  used  to  pass this data, as described in the section on matching a
       pattern in the pcreapi documentation.

       Warning: The tables that pcre_exec() and pcre_dfa_exec()  use  must  be
       the same as those that were used when the pattern was compiled. If this
       is not the case, the behaviour is undefined.

       If you did not provide custom character tables  when  the  pattern  was
       compiled, the pointer in the compiled pattern is NULL, which causes the
       matching functions to use PCRE's internal tables. Thus, you do not need
       to take any special action at run time in this case.

       If  you  saved study data with the compiled pattern, you need to create
       your own pcre[16|32]_extra data block and set the study_data  field  to
       point   to   the   reloaded   study   data.   You  must  also  set  the
       PCRE_EXTRA_STUDY_DATA bit in the flags field  to  indicate  that  study
       data  is present. Then pass the pcre[16|32]_extra block to the matching
       function in the usual way. If the pattern was studied for  just-in-time
       optimization,  that  data  cannot  be  saved,  and  so  is  lost  by  a
       save/restore cycle.


COMPATIBILITY WITH DIFFERENT PCRE RELEASES

       In general, it is safest to  recompile  all  saved  patterns  when  you
       update  to  a new PCRE release, though not all updates actually require
       this.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 12 November 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCREPERFORM(3)             Library Functions Manual             PCREPERFORM(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE PERFORMANCE

       Two  aspects  of performance are discussed below: memory usage and pro-
       cessing time. The way you express your pattern as a regular  expression
       can affect both of them.


COMPILED PATTERN MEMORY USAGE

       Patterns  are compiled by PCRE into a reasonably efficient interpretive
       code, so that most simple patterns do not  use  much  memory.  However,
       there  is  one case where the memory usage of a compiled pattern can be
       unexpectedly large. If a parenthesized subpattern has a quantifier with
       a minimum greater than 1 and/or a limited maximum, the whole subpattern
       is repeated in the compiled code. For example, the pattern

         (abc|def){2,4}

       is compiled as if it were

         (abc|def)(abc|def)((abc|def)(abc|def)?)?

       (Technical aside: It is done this way so that backtrack  points  within
       each of the repetitions can be independently maintained.)

       For  regular expressions whose quantifiers use only small numbers, this
       is not usually a problem. However, if the numbers are large,  and  par-
       ticularly  if  such repetitions are nested, the memory usage can become
       an embarrassment. For example, the very simple pattern

         ((ab){1,1000}c){1,3}

       uses 51K bytes when compiled using the 8-bit library. When PCRE is com-
       piled  with  its  default  internal pointer size of two bytes, the size
       limit on a compiled pattern is 64K data units, and this is reached with
       the  above  pattern  if  the outer repetition is increased from 3 to 4.
       PCRE can be compiled to use larger internal pointers  and  thus  handle
       larger  compiled patterns, but it is better to try to rewrite your pat-
       tern to use less memory if you can.

       One way of reducing the memory usage for such patterns is to  make  use
       of PCRE's "subroutine" facility. Re-writing the above pattern as

         ((ab)(?2){0,999}c)(?1){0,2}

       reduces the memory requirements to 18K, and indeed it remains under 20K
       even with the outer repetition increased to 100. However, this  pattern
       is  not  exactly equivalent, because the "subroutine" calls are treated
       as atomic groups into which there can be no backtracking if there is  a
       subsequent  matching  failure.  Therefore,  PCRE cannot do this kind of
       rewriting automatically.  Furthermore, there is a  noticeable  loss  of
       speed  when executing the modified pattern. Nevertheless, if the atomic
       grouping is not a problem and the loss of  speed  is  acceptable,  this
       kind  of  rewriting will allow you to process patterns that PCRE cannot
       otherwise handle.


STACK USAGE AT RUN TIME

       When pcre_exec() or pcre[16|32]_exec() is used  for  matching,  certain
       kinds  of  pattern  can  cause  it  to use large amounts of the process
       stack. In some environments the default process stack is  quite  small,
       and  if it runs out the result is often SIGSEGV. This issue is probably
       the most frequently raised problem with PCRE.  Rewriting  your  pattern
       can  often  help.  The  pcrestack documentation discusses this issue in
       detail.


PROCESSING TIME

       Certain items in regular expression patterns are processed  more  effi-
       ciently than others. It is more efficient to use a character class like
       [aeiou]  than  a  set  of   single-character   alternatives   such   as
       (a|e|i|o|u).  In  general,  the simplest construction that provides the
       required behaviour is usually the most efficient. Jeffrey Friedl's book
       contains  a  lot  of useful general discussion about optimizing regular
       expressions for efficient performance. This  document  contains  a  few
       observations about PCRE.

       Using  Unicode  character  properties  (the  \p, \P, and \X escapes) is
       slow, because PCRE has to use a multi-stage table  lookup  whenever  it
       needs  a  character's  property. If you can find an alternative pattern
       that does not use character properties, it will probably be faster.

       By default, the escape sequences \b, \d, \s,  and  \w,  and  the  POSIX
       character  classes  such  as  [:alpha:]  do not use Unicode properties,
       partly for backwards compatibility, and partly for performance reasons.
       However,  you can set PCRE_UCP if you want Unicode character properties
       to be used. This can double the matching time for  items  such  as  \d,
       when matched with a traditional matching function; the performance loss
       is less with a DFA matching function, and in both cases  there  is  not
       much difference for \b.

       When  a  pattern  begins  with .* not in parentheses, or in parentheses
       that are not the subject of a backreference, and the PCRE_DOTALL option
       is  set, the pattern is implicitly anchored by PCRE, since it can match
       only at the start of a subject string. However, if PCRE_DOTALL  is  not
       set,  PCRE  cannot  make this optimization, because the . metacharacter
       does not then match a newline, and if the subject string contains  new-
       lines,  the  pattern may match from the character immediately following
       one of them instead of from the very start. For example, the pattern

         .*second

       matches the subject "first\nand second" (where \n stands for a  newline
       character),  with the match starting at the seventh character. In order
       to do this, PCRE has to retry the match starting after every newline in
       the subject.

       If  you  are using such a pattern with subject strings that do not con-
       tain newlines, the best performance is obtained by setting PCRE_DOTALL,
       or  starting  the pattern with ^.* or ^.*? to indicate explicit anchor-
       ing. That saves PCRE from having to scan along the subject looking  for
       a newline to restart at.

       Beware  of  patterns  that contain nested indefinite repeats. These can
       take a long time to run when applied to a string that does  not  match.
       Consider the pattern fragment

         ^(a+)*

       This  can  match "aaaa" in 16 different ways, and this number increases
       very rapidly as the string gets longer. (The * repeat can match  0,  1,
       2,  3, or 4 times, and for each of those cases other than 0 or 4, the +
       repeats can match different numbers of times.) When  the  remainder  of
       the pattern is such that the entire match is going to fail, PCRE has in
       principle to try  every  possible  variation,  and  this  can  take  an
       extremely long time, even for relatively short strings.

       An optimization catches some of the more simple cases such as

         (a+)*b

       where  a  literal  character  follows. Before embarking on the standard
       matching procedure, PCRE checks that there is a "b" later in  the  sub-
       ject  string, and if there is not, it fails the match immediately. How-
       ever, when there is no following literal this  optimization  cannot  be
       used. You can see the difference by comparing the behaviour of

         (a+)*\d

       with  the  pattern  above.  The former gives a failure almost instantly
       when applied to a whole line of  "a"  characters,  whereas  the  latter
       takes an appreciable time with strings longer than about 20 characters.

       In many cases, the solution to this kind of performance issue is to use
       an atomic group or a possessive quantifier.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 25 August 2012
       Copyright (c) 1997-2012 University of Cambridge.
------------------------------------------------------------------------------


PCREPOSIX(3)               Library Functions Manual               PCREPOSIX(3)



NAME
       PCRE - Perl-compatible regular expressions.

SYNOPSIS

       #include <pcreposix.h>

       int regcomp(regex_t *preg, const char *pattern,
            int cflags);

       int regexec(regex_t *preg, const char *string,
            size_t nmatch, regmatch_t pmatch[], int eflags);
            size_t regerror(int errcode, const regex_t *preg,
            char *errbuf, size_t errbuf_size);

       void regfree(regex_t *preg);


DESCRIPTION

       This  set  of functions provides a POSIX-style API for the PCRE regular
       expression 8-bit library. See the pcreapi documentation for a  descrip-
       tion  of  PCRE's native API, which contains much additional functional-
       ity. There is no POSIX-style  wrapper  for  PCRE's  16-bit  and  32-bit
       library.

       The functions described here are just wrapper functions that ultimately
       call  the  PCRE  native  API.  Their  prototypes  are  defined  in  the
       pcreposix.h  header  file,  and  on  Unix systems the library itself is
       called pcreposix.a, so can be accessed by  adding  -lpcreposix  to  the
       command  for  linking  an application that uses them. Because the POSIX
       functions call the native ones, it is also necessary to add -lpcre.

       I have implemented only those POSIX option bits that can be  reasonably
       mapped  to PCRE native options. In addition, the option REG_EXTENDED is
       defined with the value zero. This has no  effect,  but  since  programs
       that  are  written  to  the POSIX interface often use it, this makes it
       easier to slot in PCRE as a replacement library.  Other  POSIX  options
       are not even defined.

       There  are also some other options that are not defined by POSIX. These
       have been added at the request of users who want to make use of certain
       PCRE-specific features via the POSIX calling interface.

       When  PCRE  is  called  via these functions, it is only the API that is
       POSIX-like in style. The syntax and semantics of  the  regular  expres-
       sions  themselves  are  still  those of Perl, subject to the setting of
       various PCRE options, as described below. "POSIX-like in  style"  means
       that  the  API  approximates  to  the POSIX definition; it is not fully
       POSIX-compatible, and in multi-byte encoding  domains  it  is  probably
       even less compatible.

       The  header for these functions is supplied as pcreposix.h to avoid any
       potential clash with other POSIX  libraries.  It  can,  of  course,  be
       renamed or aliased as regex.h, which is the "correct" name. It provides
       two structure types, regex_t for  compiled  internal  forms,  and  reg-
       match_t  for  returning  captured substrings. It also defines some con-
       stants whose names start  with  "REG_";  these  are  used  for  setting
       options and identifying error codes.


COMPILING A PATTERN

       The  function regcomp() is called to compile a pattern into an internal
       form. The pattern is a C string terminated by a  binary  zero,  and  is
       passed  in  the  argument  pattern. The preg argument is a pointer to a
       regex_t structure that is used as a base for storing information  about
       the compiled regular expression.

       The argument cflags is either zero, or contains one or more of the bits
       defined by the following macros:

         REG_DOTALL

       The PCRE_DOTALL option is set when the regular expression is passed for
       compilation to the native function. Note that REG_DOTALL is not part of
       the POSIX standard.

         REG_ICASE

       The PCRE_CASELESS option is set when the regular expression  is  passed
       for compilation to the native function.

         REG_NEWLINE

       The  PCRE_MULTILINE option is set when the regular expression is passed
       for compilation to the native function. Note that this does  not  mimic
       the  defined  POSIX  behaviour  for REG_NEWLINE (see the following sec-
       tion).

         REG_NOSUB

       The PCRE_NO_AUTO_CAPTURE option is set when the regular  expression  is
       passed for compilation to the native function. In addition, when a pat-
       tern that is compiled with this flag is passed to regexec() for  match-
       ing,  the  nmatch  and  pmatch  arguments  are ignored, and no captured
       strings are returned.

         REG_UCP

       The PCRE_UCP option is set when the regular expression  is  passed  for
       compilation  to  the  native  function. This causes PCRE to use Unicode
       properties when matchine \d, \w,  etc.,  instead  of  just  recognizing
       ASCII values. Note that REG_UTF8 is not part of the POSIX standard.

         REG_UNGREEDY

       The  PCRE_UNGREEDY  option is set when the regular expression is passed
       for compilation to the native function. Note that REG_UNGREEDY  is  not
       part of the POSIX standard.

         REG_UTF8

       The  PCRE_UTF8  option is set when the regular expression is passed for
       compilation to the native function. This causes the pattern itself  and
       all  data  strings used for matching it to be treated as UTF-8 strings.
       Note that REG_UTF8 is not part of the POSIX standard.

       In the absence of these flags, no options  are  passed  to  the  native
       function.   This  means  the  the  regex  is compiled with PCRE default
       semantics. In particular, the way it handles newline characters in  the
       subject  string  is  the Perl way, not the POSIX way. Note that setting
       PCRE_MULTILINE has only some of the effects specified for  REG_NEWLINE.
       It  does not affect the way newlines are matched by . (they are not) or
       by a negative class such as [^a] (they are).

       The yield of regcomp() is zero on success, and non-zero otherwise.  The
       preg structure is filled in on success, and one member of the structure
       is public: re_nsub contains the number of capturing subpatterns in  the
       regular expression. Various error codes are defined in the header file.

       NOTE:  If  the  yield of regcomp() is non-zero, you must not attempt to
       use the contents of the preg structure. If, for example, you pass it to
       regexec(), the result is undefined and your program is likely to crash.


MATCHING NEWLINE CHARACTERS

       This area is not simple, because POSIX and Perl take different views of
       things.  It is not possible to get PCRE to obey  POSIX  semantics,  but
       then  PCRE was never intended to be a POSIX engine. The following table
       lists the different possibilities for matching  newline  characters  in
       PCRE:

                                 Default   Change with

         . matches newline          no     PCRE_DOTALL
         newline matches [^a]       yes    not changeable
         $ matches \n at end        yes    PCRE_DOLLARENDONLY
         $ matches \n in middle     no     PCRE_MULTILINE
         ^ matches \n in middle     no     PCRE_MULTILINE

       This is the equivalent table for POSIX:

                                 Default   Change with

         . matches newline          yes    REG_NEWLINE
         newline matches [^a]       yes    REG_NEWLINE
         $ matches \n at end        no     REG_NEWLINE
         $ matches \n in middle     no     REG_NEWLINE
         ^ matches \n in middle     no     REG_NEWLINE

       PCRE's behaviour is the same as Perl's, except that there is no equiva-
       lent for PCRE_DOLLAR_ENDONLY in Perl. In both PCRE and Perl,  there  is
       no way to stop newline from matching [^a].

       The   default  POSIX  newline  handling  can  be  obtained  by  setting
       PCRE_DOTALL and PCRE_DOLLAR_ENDONLY, but there is no way to  make  PCRE
       behave exactly as for the REG_NEWLINE action.


MATCHING A PATTERN

       The  function  regexec()  is  called  to  match a compiled pattern preg
       against a given string, which is by default terminated by a  zero  byte
       (but  see  REG_STARTEND below), subject to the options in eflags. These
       can be:

         REG_NOTBOL

       The PCRE_NOTBOL option is set when calling the underlying PCRE matching
       function.

         REG_NOTEMPTY

       The PCRE_NOTEMPTY option is set when calling the underlying PCRE match-
       ing function. Note that REG_NOTEMPTY is not part of the POSIX standard.
       However, setting this option can give more POSIX-like behaviour in some
       situations.

         REG_NOTEOL

       The PCRE_NOTEOL option is set when calling the underlying PCRE matching
       function.

         REG_STARTEND

       The  string  is  considered to start at string + pmatch[0].rm_so and to
       have a terminating NUL located at string + pmatch[0].rm_eo (there  need
       not  actually  be  a  NUL at that location), regardless of the value of
       nmatch. This is a BSD extension, compatible with but not  specified  by
       IEEE  Standard  1003.2  (POSIX.2),  and  should be used with caution in
       software intended to be portable to other systems. Note that a non-zero
       rm_so does not imply REG_NOTBOL; REG_STARTEND affects only the location
       of the string, not how it is matched.

       If the pattern was compiled with the REG_NOSUB flag, no data about  any
       matched  strings  is  returned.  The  nmatch  and  pmatch  arguments of
       regexec() are ignored.

       If the value of nmatch is zero, or if the value pmatch is NULL, no data
       about any matched strings is returned.

       Otherwise,the portion of the string that was matched, and also any cap-
       tured substrings, are returned via the pmatch argument, which points to
       an  array  of nmatch structures of type regmatch_t, containing the mem-
       bers rm_so and rm_eo. These contain the offset to the  first  character
       of  each  substring and the offset to the first character after the end
       of each substring, respectively. The 0th element of the vector  relates
       to  the  entire portion of string that was matched; subsequent elements
       relate to the capturing subpatterns of the regular  expression.  Unused
       entries in the array have both structure members set to -1.

       A  successful  match  yields  a  zero  return;  various error codes are
       defined in the header file, of  which  REG_NOMATCH  is  the  "expected"
       failure code.


ERROR MESSAGES

       The regerror() function maps a non-zero errorcode from either regcomp()
       or regexec() to a printable message. If preg is  not  NULL,  the  error
       should have arisen from the use of that structure. A message terminated
       by a binary zero is placed  in  errbuf.  The  length  of  the  message,
       including  the  zero, is limited to errbuf_size. The yield of the func-
       tion is the size of buffer needed to hold the whole message.


MEMORY USAGE

       Compiling a regular expression causes memory to be allocated and  asso-
       ciated  with  the preg structure. The function regfree() frees all such
       memory, after which preg may no longer be used as  a  compiled  expres-
       sion.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 09 January 2012
       Copyright (c) 1997-2012 University of Cambridge.
------------------------------------------------------------------------------


PCRECPP(3)                 Library Functions Manual                 PCRECPP(3)



NAME
       PCRE - Perl-compatible regular expressions.

SYNOPSIS OF C++ WRAPPER

       #include <pcrecpp.h>


DESCRIPTION

       The  C++  wrapper  for PCRE was provided by Google Inc. Some additional
       functionality was added by Giuseppe Maxia. This brief man page was con-
       structed  from  the  notes  in the pcrecpp.h file, which should be con-
       sulted for further details. Note that the C++ wrapper supports only the
       original  8-bit  PCRE  library. There is no 16-bit or 32-bit support at
       present.


MATCHING INTERFACE

       The "FullMatch" operation checks that supplied text matches a  supplied
       pattern  exactly.  If pointer arguments are supplied, it copies matched
       sub-strings that match sub-patterns into them.

         Example: successful match
            pcrecpp::RE re("h.*o");
            re.FullMatch("hello");

         Example: unsuccessful match (requires full match):
            pcrecpp::RE re("e");
            !re.FullMatch("hello");

         Example: creating a temporary RE object:
            pcrecpp::RE("h.*o").FullMatch("hello");

       You can pass in a "const char*" or a "string" for "text". The  examples
       below  tend to use a const char*. You can, as in the different examples
       above, store the RE object explicitly in a variable or use a  temporary
       RE  object.  The  examples below use one mode or the other arbitrarily.
       Either could correctly be used for any of these examples.

       You must supply extra pointer arguments to extract matched subpieces.

         Example: extracts "ruby" into "s" and 1234 into "i"
            int i;
            string s;
            pcrecpp::RE re("(\\w+):(\\d+)");
            re.FullMatch("ruby:1234", &s, &i);

         Example: does not try to extract any extra sub-patterns
            re.FullMatch("ruby:1234", &s);

         Example: does not try to extract into NULL
            re.FullMatch("ruby:1234", NULL, &i);

         Example: integer overflow causes failure
            !re.FullMatch("ruby:1234567891234", NULL, &i);

         Example: fails because there aren't enough sub-patterns:
            !pcrecpp::RE("\\w+:\\d+").FullMatch("ruby:1234", &s);

         Example: fails because string cannot be stored in integer
            !pcrecpp::RE("(.*)").FullMatch("ruby", &i);

       The provided pointer arguments can be pointers to  any  scalar  numeric
       type, or one of:

          string        (matched piece is copied to string)
          StringPiece   (StringPiece is mutated to point to matched piece)
          T             (where "bool T::ParseFrom(const char*, int)" exists)
          NULL          (the corresponding matched sub-pattern is not copied)

       The  function returns true iff all of the following conditions are sat-
       isfied:

         a. "text" matches "pattern" exactly;

         b. The number of matched sub-patterns is >= number of supplied
            pointers;

         c. The "i"th argument has a suitable type for holding the
            string captured as the "i"th sub-pattern. If you pass in
            void * NULL for the "i"th argument, or a non-void * NULL
            of the correct type, or pass fewer arguments than the
            number of sub-patterns, "i"th captured sub-pattern is
            ignored.

       CAVEAT: An optional sub-pattern that does  not  exist  in  the  matched
       string  is  assigned  the  empty  string. Therefore, the following will
       return false (because the empty string is not a valid number):

          int number;
          pcrecpp::RE::FullMatch("abc", "[a-z]+(\\d+)?", &number);

       The matching interface supports at most 16 arguments per call.  If  you
       need    more,    consider    using    the    more   general   interface
       pcrecpp::RE::DoMatch. See pcrecpp.h for the signature for DoMatch.

       NOTE: Do not use no_arg, which is used internally to mark the end of  a
       list  of optional arguments, as a placeholder for missing arguments, as
       this can lead to segfaults.


QUOTING METACHARACTERS

       You can use the "QuoteMeta" operation to insert backslashes before  all
       potentially  meaningful  characters  in  a string. The returned string,
       used as a regular expression, will exactly match the original string.

         Example:
            string quoted = RE::QuoteMeta(unquoted);

       Note that it's legal to escape a character even if it  has  no  special
       meaning  in  a  regular expression -- so this function does that. (This
       also makes it identical to the perl function  of  the  same  name;  see
       "perldoc    -f    quotemeta".)    For   example,   "1.5-2.0?"   becomes
       "1\.5\-2\.0\?".


PARTIAL MATCHES

       You can use the "PartialMatch" operation when you want the  pattern  to
       match any substring of the text.

         Example: simple search for a string:
            pcrecpp::RE("ell").PartialMatch("hello");

         Example: find first number in a string:
            int number;
            pcrecpp::RE re("(\\d+)");
            re.PartialMatch("x*100 + 20", &number);
            assert(number == 100);


UTF-8 AND THE MATCHING INTERFACE

       By  default,  pattern  and text are plain text, one byte per character.
       The UTF8 flag, passed to  the  constructor,  causes  both  pattern  and
       string to be treated as UTF-8 text, still a byte stream but potentially
       multiple bytes per character. In practice, the text is likelier  to  be
       UTF-8  than  the pattern, but the match returned may depend on the UTF8
       flag, so always use it when matching UTF8 text. For example,  "."  will
       match  one  byte normally but with UTF8 set may match up to three bytes
       of a multi-byte character.

         Example:
            pcrecpp::RE_Options options;
            options.set_utf8();
            pcrecpp::RE re(utf8_pattern, options);
            re.FullMatch(utf8_string);

         Example: using the convenience function UTF8():
            pcrecpp::RE re(utf8_pattern, pcrecpp::UTF8());
            re.FullMatch(utf8_string);

       NOTE: The UTF8 flag is ignored if pcre was not configured with the
             --enable-utf8 flag.


PASSING MODIFIERS TO THE REGULAR EXPRESSION ENGINE

       PCRE defines some modifiers to  change  the  behavior  of  the  regular
       expression   engine.  The  C++  wrapper  defines  an  auxiliary  class,
       RE_Options, as a vehicle to pass such modifiers to  a  RE  class.  Cur-
       rently, the following modifiers are supported:

          modifier              description               Perl corresponding

          PCRE_CASELESS         case insensitive match      /i
          PCRE_MULTILINE        multiple lines match        /m
          PCRE_DOTALL           dot matches newlines        /s
          PCRE_DOLLAR_ENDONLY   $ matches only at end       N/A
          PCRE_EXTRA            strict escape parsing       N/A
          PCRE_EXTENDED         ignore white spaces         /x
          PCRE_UTF8             handles UTF8 chars          built-in
          PCRE_UNGREEDY         reverses * and *?           N/A
          PCRE_NO_AUTO_CAPTURE  disables capturing parens   N/A (*)

       (*)  Both Perl and PCRE allow non capturing parentheses by means of the
       "?:" modifier within the pattern itself. e.g. (?:ab|cd) does  not  cap-
       ture, while (ab|cd) does.

       For  a  full  account on how each modifier works, please check the PCRE
       API reference page.

       For each modifier, there are two member functions whose  name  is  made
       out  of  the  modifier  in  lowercase,  without the "PCRE_" prefix. For
       instance, PCRE_CASELESS is handled by

         bool caseless()

       which returns true if the modifier is set, and

         RE_Options & set_caseless(bool)

       which sets or unsets the modifier. Moreover, PCRE_EXTRA_MATCH_LIMIT can
       be  accessed  through  the  set_match_limit()  and match_limit() member
       functions. Setting match_limit to a non-zero value will limit the  exe-
       cution  of pcre to keep it from doing bad things like blowing the stack
       or taking an eternity to return a result.  A  value  of  5000  is  good
       enough  to stop stack blowup in a 2MB thread stack. Setting match_limit
       to  zero  disables  match  limiting.  Alternatively,   you   can   call
       match_limit_recursion()  which uses PCRE_EXTRA_MATCH_LIMIT_RECURSION to
       limit how much  PCRE  recurses.  match_limit()  limits  the  number  of
       matches PCRE does; match_limit_recursion() limits the depth of internal
       recursion, and therefore the amount of stack that is used.

       Normally, to pass one or more modifiers to a RE class,  you  declare  a
       RE_Options object, set the appropriate options, and pass this object to
       a RE constructor. Example:

          RE_Options opt;
          opt.set_caseless(true);
          if (RE("HELLO", opt).PartialMatch("hello world")) ...

       RE_options has two constructors. The default constructor takes no argu-
       ments  and creates a set of flags that are off by default. The optional
       parameter option_flags is to facilitate transfer of legacy code from  C
       programs.  This lets you do

          RE(pattern,
            RE_Options(PCRE_CASELESS|PCRE_MULTILINE)).PartialMatch(str);

       However, new code is better off doing

          RE(pattern,
            RE_Options().set_caseless(true).set_multiline(true))
              .PartialMatch(str);

       If you are going to pass one of the most used modifiers, there are some
       convenience functions that return a RE_Options class with the appropri-
       ate  modifier  already  set: CASELESS(), UTF8(), MULTILINE(), DOTALL(),
       and EXTENDED().

       If you need to set several options at once, and you don't  want  to  go
       through  the pains of declaring a RE_Options object and setting several
       options, there is a parallel method that give you such ability  on  the
       fly.  You  can  concatenate several set_xxxxx() member functions, since
       each of them returns a reference to its class object. For  example,  to
       pass  PCRE_CASELESS, PCRE_EXTENDED, and PCRE_MULTILINE to a RE with one
       statement, you may write:

          RE(" ^ xyz \\s+ .* blah$",
            RE_Options()
              .set_caseless(true)
              .set_extended(true)
              .set_multiline(true)).PartialMatch(sometext);


SCANNING TEXT INCREMENTALLY

       The "Consume" operation may be useful if you want to  repeatedly  match
       regular expressions at the front of a string and skip over them as they
       match. This requires use of the "StringPiece" type, which represents  a
       sub-range  of  a  real  string.  Like RE, StringPiece is defined in the
       pcrecpp namespace.

         Example: read lines of the form "var = value" from a string.
            string contents = ...;                 // Fill string somehow
            pcrecpp::StringPiece input(contents);  // Wrap in a StringPiece

            string var;
            int value;
            pcrecpp::RE re("(\\w+) = (\\d+)\n");
            while (re.Consume(&input, &var, &value)) {
              ...;
            }

       Each successful call  to  "Consume"  will  set  "var/value",  and  also
       advance "input" so it points past the matched text.

       The  "FindAndConsume"  operation  is  similar to "Consume" but does not
       anchor your match at the beginning of  the  string.  For  example,  you
       could extract all words from a string by repeatedly calling

         pcrecpp::RE("(\\w+)").FindAndConsume(&input, &word)


PARSING HEX/OCTAL/C-RADIX NUMBERS

       By default, if you pass a pointer to a numeric value, the corresponding
       text is interpreted as a base-10  number.  You  can  instead  wrap  the
       pointer with a call to one of the operators Hex(), Octal(), or CRadix()
       to interpret the text in another base. The CRadix  operator  interprets
       C-style  "0"  (base-8)  and  "0x"  (base-16)  prefixes, but defaults to
       base-10.

         Example:
           int a, b, c, d;
           pcrecpp::RE re("(.*) (.*) (.*) (.*)");
           re.FullMatch("100 40 0100 0x40",
                        pcrecpp::Octal(&a), pcrecpp::Hex(&b),
                        pcrecpp::CRadix(&c), pcrecpp::CRadix(&d));

       will leave 64 in a, b, c, and d.


REPLACING PARTS OF STRINGS

       You can replace the first match of "pattern" in "str"  with  "rewrite".
       Within  "rewrite",  backslash-escaped  digits (\1 to \9) can be used to
       insert text matching corresponding parenthesized group  from  the  pat-
       tern. \0 in "rewrite" refers to the entire matching text. For example:

         string s = "yabba dabba doo";
         pcrecpp::RE("b+").Replace("d", &s);

       will  leave  "s" containing "yada dabba doo". The result is true if the
       pattern matches and a replacement occurs, false otherwise.

       GlobalReplace is like Replace except that it replaces  all  occurrences
       of  the  pattern  in  the string with the rewrite. Replacements are not
       subject to re-matching. For example:

         string s = "yabba dabba doo";
         pcrecpp::RE("b+").GlobalReplace("d", &s);

       will leave "s" containing "yada dada doo". It  returns  the  number  of
       replacements made.

       Extract  is like Replace, except that if the pattern matches, "rewrite"
       is copied into "out" (an additional argument) with substitutions.   The
       non-matching  portions  of "text" are ignored. Returns true iff a match
       occurred and the extraction happened successfully;  if no match occurs,
       the string is left unaffected.


AUTHOR

       The C++ wrapper was contributed by Google Inc.
       Copyright (c) 2007 Google Inc.


REVISION

       Last updated: 08 January 2012
------------------------------------------------------------------------------


PCRESAMPLE(3)              Library Functions Manual              PCRESAMPLE(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE SAMPLE PROGRAM

       A simple, complete demonstration program, to get you started with using
       PCRE, is supplied in the file pcredemo.c in the  PCRE  distribution.  A
       listing  of this program is given in the pcredemo documentation. If you
       do not have a copy of the PCRE distribution, you can save this  listing
       to re-create pcredemo.c.

       The  demonstration program, which uses the original PCRE 8-bit library,
       compiles the regular expression that is its first argument, and matches
       it  against  the subject string in its second argument. No PCRE options
       are set, and default character tables are used. If  matching  succeeds,
       the  program  outputs the portion of the subject that matched, together
       with the contents of any captured substrings.

       If the -g option is given on the command line, the program then goes on
       to check for further matches of the same regular expression in the same
       subject string. The logic is a little bit tricky because of the  possi-
       bility  of  matching an empty string. Comments in the code explain what
       is going on.

       If PCRE is installed in the standard include  and  library  directories
       for your operating system, you should be able to compile the demonstra-
       tion program using this command:

         gcc -o pcredemo pcredemo.c -lpcre

       If PCRE is installed elsewhere, you may need to add additional  options
       to  the  command line. For example, on a Unix-like system that has PCRE
       installed in /usr/local, you  can  compile  the  demonstration  program
       using a command like this:

         gcc -o pcredemo -I/usr/local/include pcredemo.c \
             -L/usr/local/lib -lpcre

       In  a  Windows  environment, if you want to statically link the program
       against a non-dll pcre.a file, you must uncomment the line that defines
       PCRE_STATIC  before  including  pcre.h, because otherwise the pcre_mal-
       loc()   and   pcre_free()   exported   functions   will   be   declared
       __declspec(dllimport), with unwanted results.

       Once  you  have  compiled and linked the demonstration program, you can
       run simple tests like this:

         ./pcredemo 'cat|dog' 'the cat sat on the mat'
         ./pcredemo -g 'cat|dog' 'the dog sat on the cat'

       Note that there is a  much  more  comprehensive  test  program,  called
       pcretest,  which  supports  many  more  facilities  for testing regular
       expressions and both PCRE libraries. The pcredemo program  is  provided
       as a simple coding example.

       If  you  try to run pcredemo when PCRE is not installed in the standard
       library directory, you may get an error like  this  on  some  operating
       systems (e.g. Solaris):

         ld.so.1:  a.out:  fatal:  libpcre.so.0:  open failed: No such file or
       directory

       This is caused by the way shared library support works  on  those  sys-
       tems. You need to add

         -R/usr/local/lib

       (for example) to the compile command to get round this problem.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 10 January 2012
       Copyright (c) 1997-2012 University of Cambridge.
------------------------------------------------------------------------------
PCRELIMITS(3)              Library Functions Manual              PCRELIMITS(3)



NAME
       PCRE - Perl-compatible regular expressions

SIZE AND OTHER LIMITATIONS

       There  are some size limitations in PCRE but it is hoped that they will
       never in practice be relevant.

       The maximum length of a compiled  pattern  is  approximately  64K  data
       units  (bytes  for  the  8-bit  library,  16-bit  units  for the 16-bit
       library, and 32-bit units for the 32-bit library) if PCRE  is  compiled
       with  the default internal linkage size, which is 2 bytes for the 8-bit
       and 16-bit libraries, and 4 bytes for the 32-bit library. If  you  want
       to process regular expressions that are truly enormous, you can compile
       PCRE with an internal linkage size of 3 or 4 (when building the  16-bit
       or  32-bit  library,  3 is rounded up to 4). See the README file in the
       source distribution and the pcrebuild  documentation  for  details.  In
       these  cases  the limit is substantially larger.  However, the speed of
       execution is slower.

       All values in repeating quantifiers must be less than 65536.

       There is no limit to the number of parenthesized subpatterns, but there
       can  be  no more than 65535 capturing subpatterns. There is, however, a
       limit to the depth of  nesting  of  parenthesized  subpatterns  of  all
       kinds.  This  is  imposed  in order to limit the amount of system stack
       used at compile time. The limit can be specified when  PCRE  is  built;
       the default is 250.

       There is a limit to the number of forward references to subsequent sub-
       patterns of around 200,000.  Repeated  forward  references  with  fixed
       upper  limits,  for example, (?2){0,100} when subpattern number 2 is to
       the right, are included in the count. There is no limit to  the  number
       of backward references.

       The maximum length of name for a named subpattern is 32 characters, and
       the maximum number of named subpatterns is 10000.

       The maximum length of a  name  in  a  (*MARK),  (*PRUNE),  (*SKIP),  or
       (*THEN)  verb is 255 for the 8-bit library and 65535 for the 16-bit and
       32-bit libraries.

       The maximum length of a subject string is the largest  positive  number
       that  an integer variable can hold. However, when using the traditional
       matching function, PCRE uses recursion to handle subpatterns and indef-
       inite  repetition.  This means that the available stack space may limit
       the size of a subject string that can be processed by certain patterns.
       For a discussion of stack issues, see the pcrestack documentation.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 05 November 2013
       Copyright (c) 1997-2013 University of Cambridge.
------------------------------------------------------------------------------


PCRESTACK(3)               Library Functions Manual               PCRESTACK(3)



NAME
       PCRE - Perl-compatible regular expressions

PCRE DISCUSSION OF STACK USAGE

       When  you call pcre[16|32]_exec(), it makes use of an internal function
       called match(). This calls itself recursively at branch points  in  the
       pattern,  in  order  to  remember the state of the match so that it can
       back up and try a different alternative if  the  first  one  fails.  As
       matching proceeds deeper and deeper into the tree of possibilities, the
       recursion depth increases. The match() function is also called in other
       circumstances,  for  example,  whenever  a parenthesized sub-pattern is
       entered, and in certain cases of repetition.

       Not all calls of match() increase the recursion depth; for an item such
       as  a* it may be called several times at the same level, after matching
       different numbers of a's. Furthermore, in a number of cases  where  the
       result  of  the  recursive call would immediately be passed back as the
       result of the current call (a "tail recursion"), the function  is  just
       restarted instead.

       The  above  comments apply when pcre[16|32]_exec() is run in its normal
       interpretive  manner.   If   the   pattern   was   studied   with   the
       PCRE_STUDY_JIT_COMPILE  option, and just-in-time compiling was success-
       ful, and the options passed to pcre[16|32]_exec() were  not  incompati-
       ble,  the  matching  process  uses the JIT-compiled code instead of the
       match() function. In this case, the  memory  requirements  are  handled
       entirely differently. See the pcrejit documentation for details.

       The  pcre[16|32]_dfa_exec()  function operates in an entirely different
       way, and uses recursion only when there is a regular expression  recur-
       sion or subroutine call in the pattern. This includes the processing of
       assertion and "once-only" subpatterns, which are handled  like  subrou-
       tine  calls.  Normally, these are never very deep, and the limit on the
       complexity of pcre[16|32]_dfa_exec() is controlled  by  the  amount  of
       workspace  it is given.  However, it is possible to write patterns with
       runaway    infinite    recursions;    such    patterns    will    cause
       pcre[16|32]_dfa_exec()  to  run  out  of stack. At present, there is no
       protection against this.

       The comments that follow do NOT apply to  pcre[16|32]_dfa_exec();  they
       are relevant only for pcre[16|32]_exec() without the JIT optimization.

   Reducing pcre[16|32]_exec()'s stack usage

       Each  time  that match() is actually called recursively, it uses memory
       from the process stack. For certain kinds of  pattern  and  data,  very
       large  amounts of stack may be needed, despite the recognition of "tail
       recursion".  You can often reduce the amount of recursion,  and  there-
       fore  the  amount of stack used, by modifying the pattern that is being
       matched. Consider, for example, this pattern:

         ([^<]|<(?!inet))+

       It matches from wherever it starts until it encounters "<inet"  or  the
       end  of  the  data,  and is the kind of pattern that might be used when
       processing an XML file. Each iteration of the outer parentheses matches
       either  one  character that is not "<" or a "<" that is not followed by
       "inet". However, each time a  parenthesis  is  processed,  a  recursion
       occurs, so this formulation uses a stack frame for each matched charac-
       ter. For a long string, a lot of stack is required. Consider  now  this
       rewritten pattern, which matches exactly the same strings:

         ([^<]++|<(?!inet))+

       This  uses very much less stack, because runs of characters that do not
       contain "<" are "swallowed" in one item inside the parentheses.  Recur-
       sion  happens  only when a "<" character that is not followed by "inet"
       is encountered (and we assume this is relatively  rare).  A  possessive
       quantifier  is  used  to stop any backtracking into the runs of non-"<"
       characters, but that is not related to stack usage.

       This example shows that one way of avoiding stack problems when  match-
       ing long subject strings is to write repeated parenthesized subpatterns
       to match more than one character whenever possible.

   Compiling PCRE to use heap instead of stack for pcre[16|32]_exec()

       In environments where stack memory is constrained, you  might  want  to
       compile  PCRE to use heap memory instead of stack for remembering back-
       up points when pcre[16|32]_exec() is running. This makes it run  a  lot
       more slowly, however.  Details of how to do this are given in the pcre-
       build documentation. When built in  this  way,  instead  of  using  the
       stack,  PCRE obtains and frees memory by calling the functions that are
       pointed to by the pcre[16|32]_stack_malloc  and  pcre[16|32]_stack_free
       variables.  By default, these point to malloc() and free(), but you can
       replace the pointers to cause PCRE to use your own functions. Since the
       block sizes are always the same, and are always freed in reverse order,
       it may be possible to implement customized  memory  handlers  that  are
       more efficient than the standard functions.

   Limiting pcre[16|32]_exec()'s stack usage

       You  can set limits on the number of times that match() is called, both
       in total and recursively. If a limit  is  exceeded,  pcre[16|32]_exec()
       returns  an  error code. Setting suitable limits should prevent it from
       running out of stack. The default values of the limits are very  large,
       and  unlikely  ever to operate. They can be changed when PCRE is built,
       and they can also be set when pcre[16|32]_exec() is called. For details
       of these interfaces, see the pcrebuild documentation and the section on
       extra data for pcre[16|32]_exec() in the pcreapi documentation.

       As a very rough rule of thumb, you should reckon on about 500 bytes per
       recursion.  Thus,  if  you  want  to limit your stack usage to 8Mb, you
       should set the limit at 16000 recursions. A 64Mb stack,  on  the  other
       hand, can support around 128000 recursions.

       In Unix-like environments, the pcretest test program has a command line
       option (-S) that can be used to increase the size of its stack. As long
       as  the  stack is large enough, another option (-M) can be used to find
       the smallest limits that allow a particular pattern to  match  a  given
       subject  string.  This is done by calling pcre[16|32]_exec() repeatedly
       with different limits.

   Obtaining an estimate of stack usage

       The actual amount of stack used per recursion can  vary  quite  a  lot,
       depending on the compiler that was used to build PCRE and the optimiza-
       tion or debugging options that were set for it. The rule of thumb value
       of  500  bytes  mentioned  above  may be larger or smaller than what is
       actually needed. A better approximation can be obtained by running this
       command:

         pcretest -m -C

       The  -C  option causes pcretest to output information about the options
       with which PCRE was compiled. When -m is also given (before -C), infor-
       mation about stack use is given in a line like this:

         Match recursion uses stack: approximate frame size = 640 bytes

       The value is approximate because some recursions need a bit more (up to
       perhaps 16 more bytes).

       If the above command is given when PCRE is compiled  to  use  the  heap
       instead  of  the  stack  for recursion, the value that is output is the
       size of each block that is obtained from the heap.

   Changing stack size in Unix-like systems

       In Unix-like environments, there is not often a problem with the  stack
       unless  very  long  strings  are  involved, though the default limit on
       stack size varies from system to system. Values from 8Mb  to  64Mb  are
       common. You can find your default limit by running the command:

         ulimit -s

       Unfortunately,  the  effect  of  running out of stack is often SIGSEGV,
       though sometimes a more explicit error message is given. You  can  nor-
       mally increase the limit on stack size by code such as this:

         struct rlimit rlim;
         getrlimit(RLIMIT_STACK, &rlim);
         rlim.rlim_cur = 100*1024*1024;
         setrlimit(RLIMIT_STACK, &rlim);

       This  reads  the current limits (soft and hard) using getrlimit(), then
       attempts to increase the soft limit to  100Mb  using  setrlimit().  You
       must do this before calling pcre[16|32]_exec().

   Changing stack size in Mac OS X

       Using setrlimit(), as described above, should also work on Mac OS X. It
       is also possible to set a stack size when linking a program. There is a
       discussion   about   stack  sizes  in  Mac  OS  X  at  this  web  site:
       http://developer.apple.com/qa/qa2005/qa1419.html.


AUTHOR

       Philip Hazel
       University Computing Service
       Cambridge CB2 3QH, England.


REVISION

       Last updated: 24 June 2012
       Copyright (c) 1997-2012 University of Cambridge.
------------------------------------------------------------------------------


