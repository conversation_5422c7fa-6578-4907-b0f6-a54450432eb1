.TH PCRE_COMPILE2 3 "01 October 2013" "PCRE 8.34"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B pcre *pcre_compile2(const char *\fIpattern\fP, int \fIoptions\fP,
.B "     int *\fIerrorcodeptr\fP,"
.B "     const char **\fIerrptr\fP, int *\fIerroffset\fP,"
.B "     const unsigned char *\fItableptr\fP);"
.sp
.B pcre16 *pcre16_compile2(PCRE_SPTR16 \fIpattern\fP, int \fIoptions\fP,
.B "     int *\fIerrorcodeptr\fP,"
.B "     const char **\fIerrptr\fP, int *\fIerroffset\fP,"
.B "     const unsigned char *\fItableptr\fP);"
.sp
.B pcre32 *pcre32_compile2(PCRE_SPTR32 \fIpattern\fP, int \fIoptions\fP,
.B "     int *\fIerrorcodeptr\fP,£
.B "     const char **\fIerrptr\fP, int *\fIerroffset\fP,"
.B "     const unsigned char *\fItableptr\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This function compiles a regular expression into an internal form. It is the
same as \fBpcre[16|32]_compile()\fP, except for the addition of the
\fIerrorcodeptr\fP argument. The arguments are:
.
.sp
  \fIpattern\fP       A zero-terminated string containing the
                  regular expression to be compiled
  \fIoptions\fP       Zero or more option bits
  \fIerrorcodeptr\fP  Where to put an error code
  \fIerrptr\fP        Where to put an error message
  \fIerroffset\fP     Offset in pattern where error was found
  \fItableptr\fP      Pointer to character tables, or NULL to
                  use the built-in default
.sp
The option bits are:
.sp
  PCRE_ANCHORED           Force pattern anchoring
  PCRE_AUTO_CALLOUT       Compile automatic callouts
  PCRE_BSR_ANYCRLF        \eR matches only CR, LF, or CRLF
  PCRE_BSR_UNICODE        \eR matches all Unicode line endings
  PCRE_CASELESS           Do caseless matching
  PCRE_DOLLAR_ENDONLY     $ not to match newline at end
  PCRE_DOTALL             . matches anything including NL
  PCRE_DUPNAMES           Allow duplicate names for subpatterns
  PCRE_EXTENDED           Ignore white space and # comments
  PCRE_EXTRA              PCRE extra features
                            (not much use currently)
  PCRE_FIRSTLINE          Force matching to be before newline
  PCRE_JAVASCRIPT_COMPAT  JavaScript compatibility
  PCRE_MULTILINE          ^ and $ match newlines within data
  PCRE_NEVER_UTF          Lock out UTF, e.g. via (*UTF)
  PCRE_NEWLINE_ANY        Recognize any Unicode newline sequence
  PCRE_NEWLINE_ANYCRLF    Recognize CR, LF, and CRLF as newline
                            sequences
  PCRE_NEWLINE_CR         Set CR as the newline sequence
  PCRE_NEWLINE_CRLF       Set CRLF as the newline sequence
  PCRE_NEWLINE_LF         Set LF as the newline sequence
  PCRE_NO_AUTO_CAPTURE    Disable numbered capturing paren-
                            theses (named ones available)
  PCRE_NO_AUTO_POSSESS    Disable auto-possessification
  PCRE_NO_START_OPTIMIZE  Disable match-time start optimizations
  PCRE_NO_UTF16_CHECK     Do not check the pattern for UTF-16
                            validity (only relevant if
                            PCRE_UTF16 is set)
  PCRE_NO_UTF32_CHECK     Do not check the pattern for UTF-32
                            validity (only relevant if
                            PCRE_UTF32 is set)
  PCRE_NO_UTF8_CHECK      Do not check the pattern for UTF-8
                            validity (only relevant if
                            PCRE_UTF8 is set)
  PCRE_UCP                Use Unicode properties for \ed, \ew, etc.
  PCRE_UNGREEDY           Invert greediness of quantifiers
  PCRE_UTF16              Run \fBpcre16_compile()\fP in UTF-16 mode
  PCRE_UTF32              Run \fBpcre32_compile()\fP in UTF-32 mode
  PCRE_UTF8               Run \fBpcre_compile()\fP in UTF-8 mode
.sp
PCRE must be built with UTF support in order to use PCRE_UTF8/16/32 and
PCRE_NO_UTF8/16/32_CHECK, and with UCP support if PCRE_UCP is used.
.P
The yield of the function is a pointer to a private data structure that
contains the compiled pattern, or NULL if an error was detected. Note that
compiling regular expressions with one version of PCRE for use with a different
version is not guaranteed to work and may cause crashes.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
