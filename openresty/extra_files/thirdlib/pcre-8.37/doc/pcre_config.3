.TH PCRE_CONFIG 3 "20 April 2014" "PCRE 8.36"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.SM
.B int pcre_config(int \fIwhat\fP, void *\fIwhere\fP);
.PP
.B int pcre16_config(int \fIwhat\fP, void *\fIwhere\fP);
.PP
.B int pcre32_config(int \fIwhat\fP, void *\fIwhere\fP);
.
.SH DESCRIPTION
.rs
.sp
This function makes it possible for a client program to find out which optional
features are available in the version of the PCRE library it is using. The
arguments are as follows:
.sp
  \fIwhat\fP     A code specifying what information is required
  \fIwhere\fP    Points to where to put the data
.sp
The \fIwhere\fP argument must point to an integer variable, except for
PCRE_CONFIG_MATCH_LIMIT, PCRE_CONFIG_MATCH_LIMIT_RECURSION, and
PCRE_CONFIG_PARENS_LIMIT, when it must point to an unsigned long integer,
and for PCRE_CONFIG_JITTARGET, when it must point to a const char*.
The available codes are:
.sp
  PCRE_CONFIG_JIT           Availability of just-in-time compiler
                              support (1=yes 0=no)
  PCRE_CONFIG_JITTARGET     String containing information about the
                              target architecture for the JIT compiler,
                              or NULL if there is no JIT support
  PCRE_CONFIG_LINK_SIZE     Internal link size: 2, 3, or 4
  PCRE_CONFIG_PARENS_LIMIT  Parentheses nesting limit
  PCRE_CONFIG_MATCH_LIMIT   Internal resource limit
  PCRE_CONFIG_MATCH_LIMIT_RECURSION
                            Internal recursion depth limit
  PCRE_CONFIG_NEWLINE       Value of the default newline sequence:
                                13 (0x000d)    for CR
                                10 (0x000a)    for LF
                              3338 (0x0d0a)    for CRLF
                                -2             for ANYCRLF
                                -1             for ANY
  PCRE_CONFIG_BSR           Indicates what \eR matches by default:
                                 0             all Unicode line endings
                                 1             CR, LF, or CRLF only
  PCRE_CONFIG_POSIX_MALLOC_THRESHOLD
                            Threshold of return slots, above which
                              \fBmalloc()\fP is used by the POSIX API
  PCRE_CONFIG_STACKRECURSE  Recursion implementation (1=stack 0=heap)
  PCRE_CONFIG_UTF16         Availability of UTF-16 support (1=yes
                               0=no); option for \fBpcre16_config()\fP
  PCRE_CONFIG_UTF32         Availability of UTF-32 support (1=yes
                               0=no); option for \fBpcre32_config()\fP
  PCRE_CONFIG_UTF8          Availability of UTF-8 support (1=yes 0=no);
                              option for \fBpcre_config()\fP
  PCRE_CONFIG_UNICODE_PROPERTIES
                            Availability of Unicode property support
                              (1=yes 0=no)
.sp
The function yields 0 on success or PCRE_ERROR_BADOPTION otherwise. That error
is also given if PCRE_CONFIG_UTF16 or PCRE_CONFIG_UTF32 is passed to
\fBpcre_config()\fP, if PCRE_CONFIG_UTF8 or PCRE_CONFIG_UTF32 is passed to
\fBpcre16_config()\fP, or if PCRE_CONFIG_UTF8 or PCRE_CONFIG_UTF16 is passed to
\fBpcre32_config()\fP.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
