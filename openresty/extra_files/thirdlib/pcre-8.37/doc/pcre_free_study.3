.TH PCRE_FREE_STUDY 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.SM
.B void pcre_free_study(pcre_extra *\fIextra\fP);
.PP
.B void pcre16_free_study(pcre16_extra *\fIextra\fP);
.PP
.B void pcre32_free_study(pcre32_extra *\fIextra\fP);
.
.SH DESCRIPTION
.rs
.sp
This function is used to free the memory used for the data generated by a call
to \fBpcre[16|32]_study()\fP when it is no longer needed. The argument must be the
result of such a call.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
