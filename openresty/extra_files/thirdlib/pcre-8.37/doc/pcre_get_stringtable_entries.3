.TH PCRE_GET_STRINGTABLE_ENTRIES 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B int pcre_get_stringtable_entries(const pcre *\fIcode\fP,
.B "     const char *\fIname\fP, char **\fIfirst\fP, char **\fIlast\fP);"
.sp
.B int pcre16_get_stringtable_entries(const pcre16 *\fIcode\fP,
.B "     PCRE_SPTR16 \fIname\fP, PCRE_UCHAR16 **\fIfirst\fP, PCRE_UCHAR16 **\fIlast\fP);"
.sp
.B int pcre32_get_stringtable_entries(const pcre32 *\fIcode\fP,
.B "     PCRE_SPTR32 \fIname\fP, PCRE_UCHAR32 **\fIfirst\fP, PCRE_UCHAR32 **\fIlast\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This convenience function finds, for a compiled pattern, the first and last
entries for a given name in the table that translates capturing parenthesis
names into numbers. When names are required to be unique (PCRE_DUPNAMES is
\fInot\fP set), it is usually easier to use \fBpcre[16|32]_get_stringnumber()\fP
instead.
.sp
  \fIcode\fP    Compiled regular expression
  \fIname\fP    Name whose entries required
  \fIfirst\fP   Where to return a pointer to the first entry
  \fIlast\fP    Where to return a pointer to the last entry
.sp
The yield of the function is the length of each entry, or
PCRE_ERROR_NOSUBSTRING if none are found.
.P
There is a complete description of the PCRE native API, including the format of
the table entries, in the
.\" HREF
\fBpcreapi\fP
.\"
page, and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
