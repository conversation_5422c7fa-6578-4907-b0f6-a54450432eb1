.TH PCRE_GET_SUBSTRING_LIST 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B int pcre_get_substring_list(const char *\fIsubject\fP,
.B "     int *\fIovector\fP, int \fIstringcount\fP, const char ***\fIlistptr\fP);"
.sp
.B int pcre16_get_substring_list(PCRE_SPTR16 \fIsubject\fP,
.B "     int *\fIovector\fP, int \fIstringcount\fP, PCRE_SPTR16 **\fIlistptr\fP);"
.sp
.B int pcre32_get_substring_list(PCRE_SPTR32 \fIsubject\fP,
.B "     int *\fIovector\fP, int \fIstringcount\fP, PCRE_SPTR32 **\fIlistptr\fP);"
.fi
.
.SH DESCRIPTION
.rs
.sp
This is a convenience function for extracting a list of all the captured
substrings. The arguments are:
.sp
  \fIsubject\fP       Subject that has been successfully matched
  \fIovector\fP       Offset vector that \fBpcre[16|32]_exec\fP used
  \fIstringcount\fP   Value returned by \fBpcre[16|32]_exec\fP
  \fIlistptr\fP       Where to put a pointer to the list
.sp
The memory in which the substrings and the list are placed is obtained by
calling \fBpcre[16|32]_malloc()\fP. The convenience function
\fBpcre[16|32]_free_substring_list()\fP can be used to free it when it is no
longer needed. A pointer to a list of pointers is put in the variable whose
address is in \fIlistptr\fP. The list is terminated by a NULL pointer. The
yield of the function is zero on success or PCRE_ERROR_NOMEMORY if sufficient
memory could not be obtained.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
