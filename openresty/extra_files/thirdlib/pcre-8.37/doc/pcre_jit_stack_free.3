.TH PCRE_JIT_STACK_FREE 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.SM
.B void pcre_jit_stack_free(pcre_jit_stack *\fIstack\fP);
.PP
.B void pcre16_jit_stack_free(pcre16_jit_stack *\fIstack\fP);
.PP
.B void pcre32_jit_stack_free(pcre32_jit_stack *\fIstack\fP);
.
.SH DESCRIPTION
.rs
.sp
This function is used to free a JIT stack that was created by
\fBpcre[16|32]_jit_stack_alloc()\fP when it is no longer needed. For more details,
see the
.\" HREF
\fBpcrejit\fP
.\"
page.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
