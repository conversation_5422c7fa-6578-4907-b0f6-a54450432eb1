.TH PCRE_UTF32_TO_HOST_BYTE_ORDER 3 "24 June 2012" "PCRE 8.30"
.SH NAME
PCRE - Perl-compatible regular expressions
.SH SYNOPSIS
.rs
.sp
.B #include <pcre.h>
.PP
.nf
.B int pcre32_utf32_to_host_byte_order(PCRE_UCHAR32 *\fIoutput\fP,
.B "     PCRE_SPTR32 \fIinput\fP, int \fIlength\fP, int *\fIhost_byte_order\fP,"
.B "     int \fIkeep_boms\fP);"
.fi
.
.
.SH DESCRIPTION
.rs
.sp
This function, which exists only in the 32-bit library, converts a UTF-32
string to the correct order for the current host, taking account of any byte
order marks (BOMs) within the string. Its arguments are:
.sp
  \fIoutput\fP           pointer to output buffer, may be the same as \fIinput\fP
  \fIinput\fP            pointer to input buffer
  \fIlength\fP           number of 32-bit units in the input, or negative for
                     a zero-terminated string
  \fIhost_byte_order\fP  a NULL value or a non-zero value pointed to means
                     start in host byte order
  \fIkeep_boms\fP        if non-zero, BOMs are copied to the output string
.sp
The result of the function is the number of 32-bit units placed into the output
buffer, including the zero terminator if the string was zero-terminated.
.P
If \fIhost_byte_order\fP is not NULL, it is set to indicate the byte order that
is current at the end of the string.
.P
There is a complete description of the PCRE native API in the
.\" HREF
\fBpcreapi\fP
.\"
page and a description of the POSIX API in the
.\" HREF
\fBpcreposix\fP
.\"
page.
