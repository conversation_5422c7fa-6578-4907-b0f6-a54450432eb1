---------------------------- Test 1 ------------------------------
PATTERN at the start of a line.
In the middle of a line, PATTERN appears.
Check up on PATTERN near the end.
RC=0
---------------------------- Test 2 ------------------------------
PATTERN at the start of a line.
RC=0
---------------------------- Test 3 ------------------------------
7:PATTERN at the start of a line.
8:In the middle of a line, PATTERN appears.
10:This pattern is in lower case.
610:Check up on PATTERN near the end.
RC=0
---------------------------- Test 4 ------------------------------
4
RC=0
---------------------------- Test 5 ------------------------------
./testdata/grepinput:7:PATTERN at the start of a line.
./testdata/grepinput:8:In the middle of a line, PATTERN appears.
./testdata/grepinput:10:This pattern is in lower case.
./testdata/grepinput:610:Check up on PATTERN near the end.
./testdata/grepinputx:3:Here is the pattern again.
./testdata/grepinputx:5:Pattern
./testdata/grepinputx:42:This line contains pattern not on a line by itself.
RC=0
---------------------------- Test 6 ------------------------------
7:PATTERN at the start of a line.
8:In the middle of a line, PATTERN appears.
10:This pattern is in lower case.
610:Check up on PATTERN near the end.
3:Here is the pattern again.
5:Pattern
42:This line contains pattern not on a line by itself.
RC=0
---------------------------- Test 7 ------------------------------
./testdata/grepinput
./testdata/grepinputx
RC=0
---------------------------- Test 8 ------------------------------
./testdata/grepinput
RC=0
---------------------------- Test 9 ------------------------------
RC=0
---------------------------- Test 10 -----------------------------
RC=1
---------------------------- Test 11 -----------------------------
1:This is a second file of input for the pcregrep tests.
2:
4:
5:Pattern
6:That time it was on a line by itself.
7:
8:To pat or not to pat, that is the question.
9:
10:complete pair
11:of lines
12:
13:That was a complete pair
14:of lines all by themselves.
15:
16:complete pair
17:of lines
18:
19:And there they were again, to check line numbers.
20:
21:one
22:two
23:three
24:four
25:five
26:six
27:seven
28:eight
29:nine
30:ten
31:eleven
32:twelve
33:thirteen
34:fourteen
35:fifteen
36:sixteen
37:seventeen
38:eighteen
39:nineteen
40:twenty
41:
43:This is the last line of this file.
RC=0
---------------------------- Test 12 -----------------------------
Pattern
RC=0
---------------------------- Test 13 -----------------------------
Here is the pattern again.
That time it was on a line by itself.
seventeen
This line contains pattern not on a line by itself.
RC=0
---------------------------- Test 14 -----------------------------
./testdata/grepinputx:To pat or not to pat, that is the question.
RC=0
---------------------------- Test 15 -----------------------------
pcregrep: Error in command-line regex at offset 4: nothing to repeat
RC=2
---------------------------- Test 16 -----------------------------
pcregrep: Failed to open ./testdata/nonexistfile: No such file or directory
RC=2
---------------------------- Test 17 -----------------------------
features should be added at the end, because some of the tests involve the
output of line numbers, and we don't want these to change.
RC=0
---------------------------- Test 18 -----------------------------
4:features should be added at the end, because some of the tests involve the
output of line numbers, and we don't want these to change.
583:brown fox jumps over the lazy dog. The quick brown fox jumps over the lazy dog.
-------------------------------------------------------------------------------
RC=0
---------------------------- Test 19 -----------------------------
Pattern
RC=0
---------------------------- Test 20 -----------------------------
10:complete pair
of lines
16:complete pair
of lines
RC=0
---------------------------- Test 21 -----------------------------
24:four
25-five
26-six
27-seven
--
34:fourteen
35-fifteen
36-sixteen
37-seventeen
RC=0
---------------------------- Test 22 -----------------------------
21-one
22-two
23-three
24:four
--
31-eleven
32-twelve
33-thirteen
34:fourteen
RC=0
---------------------------- Test 23 -----------------------------
one
two
three
four
five
six
seven
--
eleven
twelve
thirteen
fourteen
fifteen
sixteen
seventeen
RC=0
---------------------------- Test 24 -----------------------------
four
five
six
seven
eight
nine
ten
eleven
twelve
thirteen
fourteen
fifteen
sixteen
seventeen
eighteen
nineteen
twenty

This line contains pattern not on a line by itself.
This is the last line of this file.
RC=0
---------------------------- Test 25 -----------------------------
15-
16-complete pair
17-of lines
18-
19-And there they were again, to check line numbers.
20-
21-one
22-two
23-three
24:four
25-five
26-six
27-seven
28-eight
29-nine
30-ten
31-eleven
32-twelve
33-thirteen
34:fourteen
RC=0
---------------------------- Test 26 -----------------------------

complete pair
of lines

And there they were again, to check line numbers.

one
two
three
four
five
six
seven
eight
nine
ten
eleven
twelve
thirteen
fourteen
fifteen
sixteen
seventeen
eighteen
nineteen
twenty

This line contains pattern not on a line by itself.
This is the last line of this file.
RC=0
---------------------------- Test 27 -----------------------------
four
five
six
seven
eight
nine
ten
eleven
twelve
thirteen
fourteen
fifteen
sixteen
seventeen
eighteen
nineteen
twenty

This line contains pattern not on a line by itself.
This is the last line of this file.
RC=0
---------------------------- Test 28 -----------------------------
14-of lines all by themselves.
15-
16-complete pair
17-of lines
18-
19-And there they were again, to check line numbers.
20-
21-one
22-two
23-three
24:four
25-five
26-six
27-seven
28-eight
29-nine
30-ten
31-eleven
32-twelve
33-thirteen
34:fourteen
RC=0
---------------------------- Test 29 -----------------------------
of lines all by themselves.

complete pair
of lines

And there they were again, to check line numbers.

one
two
three
four
five
six
seven
eight
nine
ten
eleven
twelve
thirteen
fourteen
fifteen
sixteen
seventeen
eighteen
nineteen
twenty

This line contains pattern not on a line by itself.
This is the last line of this file.
RC=0
---------------------------- Test 30 -----------------------------
./testdata/grepinput-4-features should be added at the end, because some of the tests involve the
./testdata/grepinput-5-output of line numbers, and we don't want these to change.
./testdata/grepinput-6-
./testdata/grepinput:7:PATTERN at the start of a line.
./testdata/grepinput:8:In the middle of a line, PATTERN appears.
./testdata/grepinput-9-
./testdata/grepinput:10:This pattern is in lower case.
--
./testdata/grepinput-607-PUT NEW DATA ABOVE THIS LINE.
./testdata/grepinput-608-=============================
./testdata/grepinput-609-
./testdata/grepinput:610:Check up on PATTERN near the end.
--
./testdata/grepinputx-1-This is a second file of input for the pcregrep tests.
./testdata/grepinputx-2-
./testdata/grepinputx:3:Here is the pattern again.
./testdata/grepinputx-4-
./testdata/grepinputx:5:Pattern
--
./testdata/grepinputx-39-nineteen
./testdata/grepinputx-40-twenty
./testdata/grepinputx-41-
./testdata/grepinputx:42:This line contains pattern not on a line by itself.
RC=0
---------------------------- Test 31 -----------------------------
./testdata/grepinput:7:PATTERN at the start of a line.
./testdata/grepinput:8:In the middle of a line, PATTERN appears.
./testdata/grepinput-9-
./testdata/grepinput:10:This pattern is in lower case.
./testdata/grepinput-11-
./testdata/grepinput-12-Here follows a whole lot of stuff that makes the file over 24K long.
./testdata/grepinput-13-
--
./testdata/grepinput:610:Check up on PATTERN near the end.
./testdata/grepinput-611-This is the last line of this file.
--
./testdata/grepinputx:3:Here is the pattern again.
./testdata/grepinputx-4-
./testdata/grepinputx:5:Pattern
./testdata/grepinputx-6-That time it was on a line by itself.
./testdata/grepinputx-7-
./testdata/grepinputx-8-To pat or not to pat, that is the question.
--
./testdata/grepinputx:42:This line contains pattern not on a line by itself.
./testdata/grepinputx-43-This is the last line of this file.
RC=0
---------------------------- Test 32 -----------------------------
./testdata/grepinputx
RC=0
---------------------------- Test 33 -----------------------------
pcregrep: Failed to open ./testdata/grepnonexist: No such file or directory
RC=2
---------------------------- Test 34 -----------------------------
RC=2
---------------------------- Test 35 -----------------------------
./testdata/grepinput8
./testdata/grepinputx
RC=0
---------------------------- Test 36 -----------------------------
./testdata/grepinput3
./testdata/grepinputx
RC=0
---------------------------- Test 37 -----------------------------
aaaaa0
aaaaa2
010203040506
RC=0
======== STDERR ========
pcregrep: pcre_exec() gave error -8 while matching this text:

aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

pcregrep: pcre_exec() gave error -8 while matching this text:

aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

pcregrep: Error -8, -21 or -27 means that a resource limit was exceeded.
pcregrep: Check your regex for nested unlimited loops.
---------------------------- Test 38 ------------------------------
This line contains a binary zero here > < for testing.
RC=0
---------------------------- Test 39 ------------------------------
This is a line before the binary zero.
This line contains a binary zero here > < for testing.
RC=0
---------------------------- Test 40 ------------------------------
This line contains a binary zero here > < for testing.
This is a line after the binary zero.
RC=0
---------------------------- Test 41 ------------------------------
before the binary zero
after the binary zero
RC=0
---------------------------- Test 42 ------------------------------
./testdata/grepinput:595:before the binary zero
./testdata/grepinput:597:after the binary zero
RC=0
---------------------------- Test 43 ------------------------------
595:before
595:zero
596:zero
597:after
597:zero
RC=0
---------------------------- Test 44 ------------------------------
595:before
595:zero
596:zero
597:zero
RC=0
---------------------------- Test 45 ------------------------------
10:pattern
595:binary
596:binary
597:binary
RC=0
---------------------------- Test 46 ------------------------------
pcregrep: Error in 2nd command-line regex at offset 9: missing )
RC=2
---------------------------- Test 47 ------------------------------
AB.VE
RC=0
---------------------------- Test 48 ------------------------------
ABOVE the elephant 
AB.VE
AB.VE the turtle
RC=0
---------------------------- Test 49 ------------------------------
ABOVE the elephant 
AB.VE
AB.VE the turtle
PUT NEW DATA ABOVE THIS LINE.
RC=0
---------------------------- Test 50 ------------------------------
RC=1
---------------------------- Test 51 ------------------------------
over the lazy dog.
This time it jumps and jumps and jumps.
RC=0
---------------------------- Test 52 ------------------------------
fox [1;31mjumps[00m
This time it [1;31mjumps[00m and [1;31mjumps[00m and [1;31mjumps[00m.
RC=0
---------------------------- Test 53 ------------------------------
36972,6
36990,4
37024,4
37066,5
37083,4
RC=0
---------------------------- Test 54 ------------------------------
595:15,6
595:33,4
596:28,4
597:15,5
597:32,4
RC=0
---------------------------- Test 55 -----------------------------
Here is the [1;31mpattern[00m again.
That time it was on a [1;31mline by itself[00m.
This line contains [1;31mpattern[00m not on a [1;31mline by itself[00m.
RC=0
---------------------------- Test 56 -----------------------------
./testdata/grepinput:456
./testdata/grepinput3:0
./testdata/grepinput8:0
./testdata/grepinputv:1
./testdata/grepinputx:0
RC=0
---------------------------- Test 57 -----------------------------
./testdata/grepinput:456
./testdata/grepinputv:1
RC=0
---------------------------- Test 58 -----------------------------
PATTERN at the start of a line.
In the middle of a line, PATTERN appears.
Check up on PATTERN near the end.
RC=0
---------------------------- Test 59 -----------------------------
PATTERN at the start of a line.
In the middle of a line, PATTERN appears.
Check up on PATTERN near the end.
RC=0
---------------------------- Test 60 -----------------------------
PATTERN at the start of a line.
In the middle of a line, PATTERN appears.
Check up on PATTERN near the end.
RC=0
---------------------------- Test 61 -----------------------------
PATTERN at the start of a line.
In the middle of a line, PATTERN appears.
Check up on PATTERN near the end.
RC=0
---------------------------- Test 62 -----------------------------
pcregrep: pcre_exec() gave error -8 while matching text that starts:

This is a file of miscellaneous text that is used as test data for checking
that the pcregrep command is working correctly. The file must be more than 24K
long so that it needs more than a single read

pcregrep: Error -8, -21 or -27 means that a resource limit was exceeded.
pcregrep: Check your regex for nested unlimited loops.
RC=1
---------------------------- Test 63 -----------------------------
pcregrep: pcre_exec() gave error -21 while matching text that starts:

This is a file of miscellaneous text that is used as test data for checking
that the pcregrep command is working correctly. The file must be more than 24K
long so that it needs more than a single read

pcregrep: Error -8, -21 or -27 means that a resource limit was exceeded.
pcregrep: Check your regex for nested unlimited loops.
RC=1
---------------------------- Test 64 ------------------------------
appears
RC=0
---------------------------- Test 65 ------------------------------
pear
RC=0
---------------------------- Test 66 ------------------------------
RC=0
---------------------------- Test 67 ------------------------------
RC=0
---------------------------- Test 68 ------------------------------
pear
RC=0
---------------------------- Test 69 -----------------------------
1:This is a second file of input for the pcregrep tests.
2:
4:
5:Pattern
6:That time it was on a line by itself.
7:
8:To pat or not to pat, that is the question.
9:
10:complete pair
11:of lines
12:
13:That was a complete pair
14:of lines all by themselves.
15:
16:complete pair
17:of lines
18:
19:And there they were again, to check line numbers.
20:
21:one
22:two
23:three
24:four
25:five
26:six
27:seven
28:eight
29:nine
30:ten
31:eleven
32:twelve
33:thirteen
34:fourteen
35:fifteen
36:sixteen
37:seventeen
38:eighteen
39:nineteen
40:twenty
41:
43:This is the last line of this file.
RC=0
---------------------------- Test 70 -----------------------------
[1;31mtriple:	t1_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	o_txt

[00m[1;31mtriple:	t3_txt	s2_tag	s_txt	p_tag	p_txt	o_tag	o_txt

[00m[1;31mtriple:	t4_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	o_txt

[00m[1;31mtriple:	t6_txt	s2_tag	s_txt	p_tag	p_txt	o_tag	o_txt

[00mRC=0
---------------------------- Test 71 -----------------------------
01
RC=0
---------------------------- Test 72 -----------------------------
[1;31m01[00m0203040506
RC=0
---------------------------- Test 73 -----------------------------
[1;31m01[00m
RC=0
---------------------------- Test 74 -----------------------------
01
<USER>
<GROUP>=0
---------------------------- Test 75 -----------------------------
[1;31m01[00m[1;31m02[00m03040506
RC=0
---------------------------- Test 76 -----------------------------
[1;31m01[00m
[1;31m02[00m
RC=0
---------------------------- Test 77 -----------------------------
01
<USER>
<GROUP>=0
---------------------------- Test 78 -----------------------------
[1;31m01[00m02[1;31m03[00m040506
RC=0
---------------------------- Test 79 -----------------------------
[1;31m01[00m
[1;31m03[00m
RC=0
---------------------------- Test 80 -----------------------------
01
RC=0
---------------------------- Test 81 -----------------------------
[1;31m01[00m0203040506
RC=0
---------------------------- Test 82 -----------------------------
[1;31m01[00m
RC=0
---------------------------- Test 83 -----------------------------
pcregrep: line 4 of file ./testdata/grepinput3 is too long for the internal buffer
pcregrep: check the --buffer-size option
RC=2
---------------------------- Test 84 -----------------------------
testdata/grepinputv:fox jumps
testdata/grepinputx:complete pair
testdata/grepinputx:That was a complete pair
testdata/grepinputx:complete pair
testdata/grepinput3:triple:	t7_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	o_txt
RC=0
---------------------------- Test 85 -----------------------------
./testdata/grepinput3:Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
RC=0
---------------------------- Test 86 -----------------------------
Binary file ./testdata/grepbinary matches
RC=0
---------------------------- Test 87 -----------------------------
RC=1
---------------------------- Test 88 -----------------------------
Binary file ./testdata/grepbinary matches
RC=0
---------------------------- Test 89 -----------------------------
RC=1
---------------------------- Test 90 -----------------------------
RC=1
---------------------------- Test 91 -----------------------------
The quick brown f x jumps over the lazy dog.
RC=0
---------------------------- Test 92 -----------------------------
The quick brown f x jumps over the lazy dog.
RC=0
---------------------------- Test 93 -----------------------------
The quick brown f x jumps over the lazy dog.
RC=0
---------------------------- Test 94 -----------------------------
./testdata/grepinput8
./testdata/grepinputx
RC=0
---------------------------- Test 95 -----------------------------
testdata/grepinputx:complete pair
testdata/grepinputx:That was a complete pair
testdata/grepinputx:complete pair
RC=0
---------------------------- Test 96 -----------------------------
./testdata/grepinput3
./testdata/grepinput8
./testdata/grepinputx
RC=0
---------------------------- Test 97 -----------------------------
./testdata/grepinput3
./testdata/grepinputx
RC=0
---------------------------- Test 98 -----------------------------
./testdata/grepinputx
RC=0
---------------------------- Test 99 -----------------------------
./testdata/grepinput3
./testdata/grepinputx
RC=0
---------------------------- Test 100 ------------------------------
./testdata/grepinput:zerothe.
./testdata/grepinput:zeroa
./testdata/grepinput:zerothe.
RC=0
---------------------------- Test 101 ------------------------------
./testdata/grepinput:[1;31m.[00m|[1;31mzero[00m|[1;31mthe[00m|[1;31m.[00m
./testdata/grepinput:[1;31mzero[00m|[1;31ma[00m
./testdata/grepinput:[1;31m.[00m|[1;31mzero[00m|[1;31mthe[00m|[1;31m.[00m
RC=0
---------------------------- Test 102 -----------------------------
2:
5:
7:
9:
12:
14:
RC=0
---------------------------- Test 103 -----------------------------
RC=0
---------------------------- Test 104 -----------------------------
2:
5:
7:
9:
12:
14:
RC=0
---------------------------- Test 105 -----------------------------
[1;31m[00mtriple:	t1_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	o_txt
[1;31m[00m
[1;31m[00mtriple:	t2_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	
[1;31m[00mLorem [1;31mipsum[00m dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
[1;31m[00m
[1;31m[00mtriple:	t3_txt	s2_tag	s_txt	p_tag	p_txt	o_tag	o_txt
[1;31m[00m
[1;31m[00mtriple:	t4_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	o_txt
[1;31m[00m
[1;31m[00mtriple:	t5_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	
[1;31m[00mo_txt
[1;31m[00m
[1;31m[00mtriple:	t6_txt	s2_tag	s_txt	p_tag	p_txt	o_tag	o_txt
[1;31m[00m
[1;31m[00mtriple:	t7_txt	s1_tag	s_txt	p_tag	p_txt	o_tag	o_txt
RC=0
---------------------------- Test 106 -----------------------------
a
RC=0
---------------------------- Test 107 -----------------------------
1:0,1
2:0,1
2:1,1
2:2,1
2:3,1
2:4,1
RC=0
