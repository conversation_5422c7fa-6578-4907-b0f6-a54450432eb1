/-- This set of tests check Unicode property support with the DFA matching 
    functionality of pcre_dfa_exec(). The -dfa flag must be used with pcretest
    when running it. --/

/\pL\P{Nd}/8
    AB
    *** Failers
    A0
    00   

/\X./8
    AB
    A\x{300}BC 
    A\x{300}\x{301}\x{302}BC 
    *** Failers
    \x{300}  

/\X\X/8
    ABC
    A\x{300}B\x{300}\x{301}C 
    A\x{300}\x{301}\x{302}BC 
    *** Failers
    \x{300}  

/^\pL+/8
    abcd
    a 
    *** Failers 

/^\PL+/8
    1234
    = 
    *** Failers 
    abcd 

/^\X+/8
    abcdA\x{300}\x{301}\x{302}
    A\x{300}\x{301}\x{302}
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}
    a 
    *** Failers 
    \x{300}\x{301}\x{302}

/\X?abc/8
    abc
    A\x{300}abc
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
    \x{300}abc  
    *** Failers

/^\X?abc/8
    abc
    A\x{300}abc
    *** Failers
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
    \x{300}abc  

/\X*abc/8
    abc
    A\x{300}abc
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
    \x{300}abc  
    *** Failers

/^\X*abc/8
    abc
    A\x{300}abc
    A\x{300}\x{301}\x{302}A\x{300}A\x{300}A\x{300}abcxyz
    *** Failers
    \x{300}abc  

/^\pL?=./8
    A=b
    =c 
    *** Failers
    1=2 
    AAAA=b  

/^\pL*=./8
    AAAA=b
    =c 
    *** Failers
    1=2  

/^\X{2,3}X/8
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X 
    *** Failers
    X
    A\x{300}\x{301}\x{302}X
    A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}A\x{300}\x{301}\x{302}X

/^\pC\pL\pM\pN\pP\pS\pZ</8
    \x7f\x{c0}\x{30f}\x{660}\x{66c}\x{f01}\x{1680}<
    \np\x{300}9!\$ < 
    ** Failers 
    ap\x{300}9!\$ < 
  
/^\PC/8
    X
    ** Failers 
    \x7f
  
/^\PL/8
    9
    ** Failers 
    \x{c0}
  
/^\PM/8
    X
    ** Failers 
    \x{30f}
  
/^\PN/8
    X
    ** Failers 
    \x{660}
  
/^\PP/8
    X
    ** Failers 
    \x{66c}
  
/^\PS/8
    X
    ** Failers 
    \x{f01}
  
/^\PZ/8
    X
    ** Failers 
    \x{1680}
    
/^\p{Cc}/8
    \x{017}
    \x{09f} 
    ** Failers
    \x{0600} 
  
/^\p{Cf}/8
    \x{601}
    \x{180e}
    \x{061c}
    \x{2066}
    \x{2067}
    \x{2068}
    \x{2069}
    ** Failers
    \x{09f} 
  
/^\p{Cn}/8
    ** Failers
    \x{09f} 
  
/^\p{Co}/8
    \x{f8ff}
    ** Failers
    \x{09f} 
  
/^\p{Cs}/8
    \?\x{dfff}
    ** Failers
    \x{09f} 
  
/^\p{Ll}/8
    a
    ** Failers 
    Z
    \x{e000}  
  
/^\p{Lm}/8
    \x{2b0}
    ** Failers
    a 
  
/^\p{Lo}/8
    \x{1bb}
    ** Failers
    a 
    \x{2b0}
  
/^\p{Lt}/8
    \x{1c5}
    ** Failers
    a 
    \x{2b0}
  
/^\p{Lu}/8
    A
    ** Failers
    \x{2b0}
  
/^\p{Mc}/8
    \x{903}
    ** Failers
    X
    \x{300}
       
/^\p{Me}/8
    \x{488}
    ** Failers
    X
    \x{903}
    \x{300}
  
/^\p{Mn}/8
    \x{300}
    \x{1a1b}
    ** Failers
    X
    \x{903}
  
/^\p{Nd}+/8O
    0123456789\x{660}\x{661}\x{662}\x{663}\x{664}\x{665}\x{666}\x{667}\x{668}\x{669}\x{66a}
    \x{6f0}\x{6f1}\x{6f2}\x{6f3}\x{6f4}\x{6f5}\x{6f6}\x{6f7}\x{6f8}\x{6f9}\x{6fa}
    \x{966}\x{967}\x{968}\x{969}\x{96a}\x{96b}\x{96c}\x{96d}\x{96e}\x{96f}\x{970}
    ** Failers
    X
  
/^\p{Nl}/8
    \x{16ee}
    ** Failers
    X
    \x{966}
  
/^\p{No}/8
    \x{b2}
    \x{b3}
    ** Failers
    X
    \x{16ee}
  
/^\p{Pc}/8
    \x5f
    \x{203f}
    ** Failers
    X
    -
    \x{58a}
  
/^\p{Pd}/8
    -
    \x{58a}
    ** Failers
    X
    \x{203f}
  
/^\p{Pe}/8
    )
    ]
    }
    \x{f3b}
    \x{2309}
    \x{230b}
    ** Failers
    X
    \x{203f}
    (
    [
    {
    \x{f3c}

/^\p{Pf}/8
    \x{bb}
    \x{2019}
    ** Failers
    X
    \x{203f}
  
/^\p{Pi}/8
    \x{ab}
    \x{2018}
    ** Failers
    X
    \x{203f}
  
/^\p{Po}/8
    !
    \x{37e}
    ** Failers
    X
    \x{203f}
  
/^\p{Ps}/8
    (
    [
    {
    \x{f3c}
    \x{2308}
    \x{230a}
    ** Failers
    X
    )
    ]
    }
    \x{f3b}
  
/^\p{Sc}+/8
    $\x{a2}\x{a3}\x{a4}\x{a5}\x{a6}
    \x{9f2}
    ** Failers
    X
    \x{2c2}
  
/^\p{Sk}/8
    \x{2c2}
    ** Failers
    X
    \x{9f2}
  
/^\p{Sm}+/8
    +<|~\x{ac}\x{2044}
    ** Failers
    X
    \x{9f2}
  
/^\p{So}/8
    \x{a6}
    \x{482} 
    ** Failers
    X
    \x{9f2}
  
/^\p{Zl}/8
    \x{2028}
    ** Failers
    X
    \x{2029}
  
/^\p{Zp}/8
    \x{2029}
    ** Failers
    X
    \x{2028}
  
/^\p{Zs}/8
    \ \
    \x{a0}
    \x{1680}
    \x{2000}
    \x{2001}     
    ** Failers
    \x{2028}
    \x{200d} 
  
/\p{Nd}+(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}+?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,}(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,}?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2}(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,3}(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}{2,3}?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}?(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}??(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(..)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(...)/8
      \x{660}\x{661}\x{662}ABC
  
/\p{Nd}*+(....)/8
      ** Failers
      \x{660}\x{661}\x{662}ABC
  
/\p{Lu}/8i
    A
    a\x{10a0}B 
    ** Failers 
    a
    \x{1d00}  

/\p{^Lu}/8i
    1234
    ** Failers
    ABC 

/\P{Lu}/8i
    1234
    ** Failers
    ABC 

/(?<=A\p{Nd})XYZ/8
    A2XYZ
    123A5XYZPQR
    ABA\x{660}XYZpqr
    ** Failers
    AXYZ
    XYZ     
    
/(?<!\pL)XYZ/8
    1XYZ
    AB=XYZ.. 
    XYZ 
    ** Failers
    WXYZ 

/[\p{Nd}]/8
    1234

/[\p{Nd}+-]+/8
    1234
    12-34
    12+\x{661}-34  
    ** Failers
    abcd  

/[\P{Nd}]+/8
    abcd
    ** Failers
    1234

/\D+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
     
/\P{Nd}+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\D]+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\P{Nd}]+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/[\D\P{Nd}]+/8O
    11111111111111111111111111111111111111111111111111111111111111111111111
    aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/\pL/8
    a
    A 

/\pL/8i
    a
    A 
    
/\p{Lu}/8 
    A
    aZ
    ** Failers
    abc   

/\p{Lu}/8i
    A
    aZ
    ** Failers
    abc   

/\p{Ll}/8 
    a
    Az
    ** Failers
    ABC   

/\p{Ll}/8i 
    a
    Az
    ** Failers
    ABC   

/^\x{c0}$/8i
    \x{c0}
    \x{e0} 

/^\x{e0}$/8i
    \x{c0}
    \x{e0} 

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8
    A\x{391}\x{10427}\x{ff3a}\x{1fb0}
    ** Failers
    a\x{391}\x{10427}\x{ff3a}\x{1fb0}   
    A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
    A\x{391}\x{1044F}\x{ff3a}\x{1fb0}
    A\x{391}\x{10427}\x{ff5a}\x{1fb0}
    A\x{391}\x{10427}\x{ff3a}\x{1fb8}

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8i
    A\x{391}\x{10427}\x{ff3a}\x{1fb0}
    a\x{391}\x{10427}\x{ff3a}\x{1fb0}   
    A\x{3b1}\x{10427}\x{ff3a}\x{1fb0}
    A\x{391}\x{1044F}\x{ff3a}\x{1fb0}
    A\x{391}\x{10427}\x{ff5a}\x{1fb0}
    A\x{391}\x{10427}\x{ff3a}\x{1fb8}

/\x{391}+/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}

/\x{391}{3,5}(.)/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X

/\x{391}{3,5}?(.)/8i
    \x{391}\x{3b1}\x{3b1}\x{3b1}\x{391}X

/[\x{391}\x{ff3a}]/8i
    \x{391}
    \x{ff3a}
    \x{3b1}
    \x{ff5a}   
    
/[\x{c0}\x{391}]/8i
    \x{c0}
    \x{e0} 

/[\x{105}-\x{109}]/8i
    \x{104}
    \x{105}
    \x{109}  
    ** Failers
    \x{100}
    \x{10a} 
    
/[z-\x{100}]/8i 
    Z
    z
    \x{39c}
    \x{178}
    |
    \x{80}
    \x{ff}
    \x{100}
    \x{101} 
    ** Failers
    \x{102}
    Y
    y           

/[z-\x{100}]/8i

/^\X/8
    A
    A\x{300}BC 
    A\x{300}\x{301}\x{302}BC 
    *** Failers
    \x{300}  

/^[\X]/8
    X123
    *** Failers
    AXYZ

/^(\X*)C/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^(\X*?)C/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^(\X*)(.)/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^(\X*?)(.)/8
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301} 
    A\x{300}\x{301}\x{302}BCA\x{300}\x{301}C 

/^\X(.)/8
    *** Failers
    A\x{300}\x{301}\x{302}

/^\X{2,3}(.)/8
    A\x{300}\x{301}B\x{300}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}DA\x{300}X
    
/^\X{2,3}?(.)/8
    A\x{300}\x{301}B\x{300}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}X
    A\x{300}\x{301}B\x{300}C\x{300}\x{301}DA\x{300}X

/^\pN{2,3}X/
    12X
    123X
    *** Failers
    X
    1X
    1234X     

/\x{100}/i8
    \x{100}   
    \x{101} 
    
/^\p{Han}+/8
    \x{2e81}\x{3007}\x{2f804}\x{31a0}
    ** Failers
    \x{2e7f}  

/^\P{Katakana}+/8
    \x{3105}
    ** Failers
    \x{30ff}  

/^[\p{Arabic}]/8
    \x{06e9}
    \x{060b}
    ** Failers
    X\x{06e9}   

/^[\P{Yi}]/8
    \x{2f800}
    ** Failers
    \x{a014}
    \x{a4c6}   

/^\p{Any}X/8
    AXYZ
    \x{1234}XYZ 
    ** Failers
    X  
    
/^\P{Any}X/8
    ** Failers
    AX
    
/^\p{Any}?X/8
    XYZ
    AXYZ
    \x{1234}XYZ 
    ** Failers
    ABXYZ   

/^\P{Any}?X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ 
    ABXYZ   

/^\p{Any}+X/8
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers
    XYZ

/^\P{Any}+X/8
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    XYZ

/^\p{Any}*X/8
    XYZ
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers

/^\P{Any}*X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ

/^[\p{Any}]X/8
    AXYZ
    \x{1234}XYZ 
    ** Failers
    X  
    
/^[\P{Any}]X/8
    ** Failers
    AX
    
/^[\p{Any}]?X/8
    XYZ
    AXYZ
    \x{1234}XYZ 
    ** Failers
    ABXYZ   

/^[\P{Any}]?X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ 
    ABXYZ   

/^[\p{Any}]+X/8
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers
    XYZ

/^[\P{Any}]+X/8
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    XYZ

/^[\p{Any}]*X/8
    XYZ
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ
    ** Failers

/^[\P{Any}]*X/8
    XYZ
    ** Failers
    AXYZ
    \x{1234}XYZ
    A\x{1234}XYZ

/^\p{Any}{3,5}?/8
    abcdefgh
    \x{1234}\n\r\x{3456}xyz 

/^\p{Any}{3,5}/8
    abcdefgh
    \x{1234}\n\r\x{3456}xyz 

/^\P{Any}{3,5}?/8
    ** Failers
    abcdefgh
    \x{1234}\n\r\x{3456}xyz 

/^\p{L&}X/8
     AXY
     aXY
     \x{1c5}XY
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^[\p{L&}]X/8
     AXY
     aXY
     \x{1c5}XY
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^\p{L&}+X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^[\p{L&}]+X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^\p{L&}+?X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^[\p{L&}]+?X/8
     AXY
     aXY
     AbcdeXyz 
     \x{1c5}AbXY
     abcDEXypqreXlmn 
     ** Failers
     \x{1bb}XY
     \x{2b0}XY
     !XY      

/^\P{L&}X/8
     !XY
     \x{1bb}XY
     \x{2b0}XY
     ** Failers
     \x{1c5}XY
     AXY      

/^[\P{L&}]X/8
     !XY
     \x{1bb}XY
     \x{2b0}XY
     ** Failers
     \x{1c5}XY
     AXY      

/^\x{023a}+?(\x{0130}+)/8i
  \x{023a}\x{2c65}\x{0130}
  
/^\x{023a}+([^X])/8i
  \x{023a}\x{2c65}X
 
/\x{c0}+\x{116}+/8i
    \x{c0}\x{e0}\x{116}\x{117}

/[\x{c0}\x{116}]+/8i
    \x{c0}\x{e0}\x{116}\x{117}

/Check property support in non-UTF-8 mode/
 
/\p{L}{4}/
    123abcdefg
    123abc\xc4\xc5zz

/\p{Carian}\p{Cham}\p{Kayah_Li}\p{Lepcha}\p{Lycian}\p{Lydian}\p{Ol_Chiki}\p{Rejang}\p{Saurashtra}\p{Sundanese}\p{Vai}/8
    \x{102A4}\x{AA52}\x{A91D}\x{1C46}\x{10283}\x{1092E}\x{1C6B}\x{A93B}\x{A8BF}\x{1BA0}\x{A50A}====

/\x{a77d}\x{1d79}/8i
    \x{a77d}\x{1d79}
    \x{1d79}\x{a77d} 

/\x{a77d}\x{1d79}/8
    \x{a77d}\x{1d79}
    ** Failers 
    \x{1d79}\x{a77d} 

/^\p{Xan}/8
    ABCD
    1234
    \x{6ca}
    \x{a6c}
    \x{10a7}   
    ** Failers
    _ABC   

/^\p{Xan}+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    ** Failers
    _ABC   

/^\p{Xan}*/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    
/^\p{Xan}{2,9}/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    
/^[\p{Xan}]/8
    ABCD1234_
    1234abcd_
    \x{6ca}
    \x{a6c}
    \x{10a7}   
    ** Failers
    _ABC   
 
/^[\p{Xan}]+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    ** Failers
    _ABC   

/^>\p{Xsp}/8
    >\x{1680}\x{2028}\x{0b}
    ** Failers
    \x{0b} 

/^>\p{Xsp}+/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xsp}*/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xsp}{2,9}/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>[\p{Xsp}]/8O
    >\x{2028}\x{0b}
 
/^>[\p{Xsp}]+/8O
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xps}/8
    >\x{1680}\x{2028}\x{0b}
    >\x{a0} 
    ** Failers
    \x{0b} 

/^>\p{Xps}+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^>\p{Xps}+?/8
    >\x{1680}\x{2028}\x{0b}

/^>\p{Xps}*/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xps}{2,9}/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>\p{Xps}{2,9}?/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}
    
/^>[\p{Xps}]/8
    >\x{2028}\x{0b}
 
/^>[\p{Xps}]+/8
    > \x{09}\x{0a}\x{0c}\x{0d}\x{a0}\x{1680}\x{2028}\x{0b}

/^\p{Xwd}/8
    ABCD
    1234
    \x{6ca}
    \x{a6c}
    \x{10a7}
    _ABC    
    ** Failers
    [] 

/^\p{Xwd}+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_

/^\p{Xwd}*/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_
    
/^\p{Xwd}{2,9}/8
    A_12\x{6ca}\x{a6c}\x{10a7}
    
/^[\p{Xwd}]/8
    ABCD1234_
    1234abcd_
    \x{6ca}
    \x{a6c}
    \x{10a7}   
    _ABC 
    ** Failers
    []   
 
/^[\p{Xwd}]+/8
    ABCD1234\x{6ca}\x{a6c}\x{10a7}_

/-- Unicode properties for \b abd \B --/

/\b...\B/8W
    abc_
    \x{37e}abc\x{376} 
    \x{37e}\x{376}\x{371}\x{393}\x{394} 
    !\x{c0}++\x{c1}\x{c2} 
    !\x{c0}+++++ 

/-- Without PCRE_UCP, non-ASCII always fail, even if < 256  --/

/\b...\B/8
    abc_
    ** Failers 
    \x{37e}abc\x{376} 
    \x{37e}\x{376}\x{371}\x{393}\x{394} 
    !\x{c0}++\x{c1}\x{c2} 
    !\x{c0}+++++ 

/-- With PCRE_UCP, non-UTF8 chars that are < 256 still check properties  --/

/\b...\B/W
    abc_
    !\x{c0}++\x{c1}\x{c2} 
    !\x{c0}+++++ 
    
/-- Caseless single negated characters > 127 need UCP support --/

/[^\x{100}]/8i
    \x{100}\x{101}X

/[^\x{100}]+/8i
    \x{100}\x{101}XX

/^\X/8
    A\P
    A\P\P 
    A\x{300}\x{301}\P
    A\x{300}\x{301}\P\P  
    A\x{301}\P
    A\x{301}\P\P  
    
/^\X{2,3}/8
    A\P
    A\P\P 
    AA\P
    AA\P\P  
    A\x{300}\x{301}\P
    A\x{300}\x{301}\P\P  
    A\x{300}\x{301}A\x{300}\x{301}\P
    A\x{300}\x{301}A\x{300}\x{301}\P\P  

/^\X{2}/8
    AA\P
    AA\P\P  
    A\x{300}\x{301}A\x{300}\x{301}\P
    A\x{300}\x{301}A\x{300}\x{301}\P\P  
    
/^\X+/8
    AA\P
    AA\P\P  

/^\X+?Z/8
    AA\P
    AA\P\P 

/-- These are tests for extended grapheme clusters --/ 

/^\X/8+
    G\x{34e}\x{34e}X
    \x{34e}\x{34e}X
    \x04X
    \x{1100}X
    \x{1100}\x{34e}X
    \x{1b04}\x{1b04}X 
    *These match up to the roman letters
    \x{1111}\x{1111}L,L
    \x{1111}\x{1111}\x{1169}L,L,V
    \x{1111}\x{ae4c}L, LV
    \x{1111}\x{ad89}L, LVT
    \x{1111}\x{ae4c}\x{1169}L, LV, V
    \x{1111}\x{ae4c}\x{1169}\x{1169}L, LV, V, V
    \x{1111}\x{ae4c}\x{1169}\x{11fe}L, LV, V, T
    \x{1111}\x{ad89}\x{11fe}L, LVT, T
    \x{1111}\x{ad89}\x{11fe}\x{11fe}L, LVT, T, T
    \x{ad89}\x{11fe}\x{11fe}LVT, T, T
    *These match just the first codepoint (invalid sequence)
    \x{1111}\x{11fe}L, T
    \x{ae4c}\x{1111}LV, L
    \x{ae4c}\x{ae4c}LV, LV
    \x{ae4c}\x{ad89}LV, LVT
    \x{1169}\x{1111}V, L
    \x{1169}\x{ae4c}V, LV
    \x{1169}\x{ad89}V, LVT
    \x{ad89}\x{1111}LVT, L
    \x{ad89}\x{1169}LVT, V
    \x{ad89}\x{ae4c}LVT, LV
    \x{ad89}\x{ad89}LVT, LVT
    \x{11fe}\x{1111}T, L
    \x{11fe}\x{1169}T, V
    \x{11fe}\x{ae4c}T, LV
    \x{11fe}\x{ad89}T, LVT
    *Test extend and spacing mark
    \x{1111}\x{ae4c}\x{0711}L, LV, extend
    \x{1111}\x{ae4c}\x{1b04}L, LV, spacing mark
    \x{1111}\x{ae4c}\x{1b04}\x{0711}\x{1b04}L, LV, spacing mark, extend, spacing mark
    *Test CR, LF, and control
    \x0d\x{0711}CR, extend
    \x0d\x{1b04}CR, spacingmark
    \x0a\x{0711}LF, extend
    \x0a\x{1b04}LF, spacingmark
    \x0b\x{0711}Control, extend
    \x09\x{1b04}Control, spacingmark
    *There are no Prepend characters, so we can't test Prepend, CR
    
/^(?>\X{2})X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    
/^\X{2,4}X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X

/^\X{2,4}?X/8+
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X
    \x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}\x{1111}\x{ae4c}X

/-- --/

/\x{1e9e}+/8i
    \x{1e9e}\x{00df}

/[z\x{1e9e}]+/8i
    \x{1e9e}\x{00df}

/\x{00df}+/8i
    \x{1e9e}\x{00df}

/[z\x{00df}]+/8i
    \x{1e9e}\x{00df}

/\x{1f88}+/8i
    \x{1f88}\x{1f80} 

/[z\x{1f88}]+/8i
    \x{1f88}\x{1f80} 

/-- Perl matches these --/

/\x{00b5}+/8i
    \x{00b5}\x{039c}\x{03bc}

/\x{039c}+/8i
    \x{00b5}\x{039c}\x{03bc}

/\x{03bc}+/8i
    \x{00b5}\x{039c}\x{03bc}


/\x{00c5}+/8i
    \x{00c5}\x{00e5}\x{212b}

/\x{00e5}+/8i
    \x{00c5}\x{00e5}\x{212b}

/\x{212b}+/8i
    \x{00c5}\x{00e5}\x{212b}


/\x{01c4}+/8i
    \x{01c4}\x{01c5}\x{01c6}

/\x{01c5}+/8i
    \x{01c4}\x{01c5}\x{01c6}

/\x{01c6}+/8i
    \x{01c4}\x{01c5}\x{01c6}


/\x{01c7}+/8i
    \x{01c7}\x{01c8}\x{01c9}

/\x{01c8}+/8i
    \x{01c7}\x{01c8}\x{01c9}

/\x{01c9}+/8i
    \x{01c7}\x{01c8}\x{01c9}


/\x{01ca}+/8i
    \x{01ca}\x{01cb}\x{01cc}

/\x{01cb}+/8i
    \x{01ca}\x{01cb}\x{01cc}

/\x{01cc}+/8i
    \x{01ca}\x{01cb}\x{01cc}


/\x{01f1}+/8i
    \x{01f1}\x{01f2}\x{01f3}

/\x{01f2}+/8i
    \x{01f1}\x{01f2}\x{01f3}

/\x{01f3}+/8i
    \x{01f1}\x{01f2}\x{01f3}


/\x{0345}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/\x{0399}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/\x{03b9}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}

/\x{1fbe}+/8i
    \x{0345}\x{0399}\x{03b9}\x{1fbe}


/\x{0392}+/8i
    \x{0392}\x{03b2}\x{03d0}

/\x{03b2}+/8i
    \x{0392}\x{03b2}\x{03d0}

/\x{03d0}+/8i
    \x{0392}\x{03b2}\x{03d0}
    

/\x{0395}+/8i
    \x{0395}\x{03b5}\x{03f5}

/\x{03b5}+/8i
    \x{0395}\x{03b5}\x{03f5}

/\x{03f5}+/8i
    \x{0395}\x{03b5}\x{03f5}


/\x{0398}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/\x{03b8}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/\x{03d1}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}

/\x{03f4}+/8i
    \x{0398}\x{03b8}\x{03d1}\x{03f4}
    

/\x{039a}+/8i
    \x{039a}\x{03ba}\x{03f0}

/\x{03ba}+/8i
    \x{039a}\x{03ba}\x{03f0}

/\x{03f0}+/8i
    \x{039a}\x{03ba}\x{03f0}
    

/\x{03a0}+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/\x{03c0}+/8i
    \x{03a0}\x{03c0}\x{03d6} 

/\x{03d6}+/8i
    \x{03a0}\x{03c0}\x{03d6} 


/\x{03a1}+/8i
    \x{03a1}\x{03c1}\x{03f1}

/\x{03c1}+/8i
    \x{03a1}\x{03c1}\x{03f1}

/\x{03f1}+/8i
    \x{03a1}\x{03c1}\x{03f1}


/\x{03a3}+/8i
    \x{03A3}\x{03C2}\x{03C3}

/\x{03c2}+/8i
    \x{03A3}\x{03C2}\x{03C3}

/\x{03c3}+/8i
    \x{03A3}\x{03C2}\x{03C3}
    

/\x{03a6}+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/\x{03c6}+/8i
    \x{03a6}\x{03c6}\x{03d5} 

/\x{03d5}+/8i
    \x{03a6}\x{03c6}\x{03d5} 


/\x{03c9}+/8i
    \x{03c9}\x{03a9}\x{2126}

/\x{03a9}+/8i
    \x{03c9}\x{03a9}\x{2126}

/\x{2126}+/8i
    \x{03c9}\x{03a9}\x{2126}
    

/\x{1e60}+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/\x{1e61}+/8i
    \x{1e60}\x{1e61}\x{1e9b}

/\x{1e9b}+/8i
    \x{1e60}\x{1e61}\x{1e9b}
    

/\x{1e9e}+/8i
    \x{1e9e}\x{00df}

/\x{00df}+/8i
    \x{1e9e}\x{00df}
    

/\x{1f88}+/8i
    \x{1f88}\x{1f80} 

/\x{1f80}+/8i
    \x{1f88}\x{1f80} 

/\x{004b}+/8i
    \x{004b}\x{006b}\x{212a}

/\x{006b}+/8i
    \x{004b}\x{006b}\x{212a}

/\x{212a}+/8i
    \x{004b}\x{006b}\x{212a}


/\x{0053}+/8i
    \x{0053}\x{0073}\x{017f}

/\x{0073}+/8i
    \x{0053}\x{0073}\x{017f}

/\x{017f}+/8i
    \x{0053}\x{0073}\x{017f}

/ist/8i
    ikt

/is+t/8i
    iSs\x{17f}t
    ikt

/is+?t/8i
    ikt

/is?t/8i
    ikt

/is{2}t/8i
    iskt

/^\p{Xuc}/8
    $abc
    @abc
    `abc
    \x{1234}abc
    ** Failers
    abc     

/^\p{Xuc}+/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^\p{Xuc}+?/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^\p{Xuc}+?\*/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^\p{Xuc}++/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^\p{Xuc}{3,5}/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^\p{Xuc}{3,5}?/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^[\p{Xuc}]/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^[\p{Xuc}]+/8
    $@`\x{a0}\x{1234}\x{e000}**
    ** Failers
    \x{9f}

/^\P{Xuc}/8
    abc
    ** Failers
    $abc
    @abc
    `abc
    \x{1234}abc

/^[\P{Xuc}]/8
    abc
    ** Failers
    $abc
    @abc
    `abc
    \x{1234}abc

/^A\s+Z/8W
    A\x{2005}Z
    A\x{85}\x{180e}\x{2005}Z

/^A[\s]+Z/8W
    A\x{2005}Z
    A\x{85}\x{180e}\x{2005}Z

/-- End of testinput10 --/ 
