/-- These are a few representative patterns whose lengths and offsets are to be 
shown when the link size is 2. This is just a doublecheck test to ensure the 
sizes don't go horribly wrong when something is changed. The pattern contents 
are all themselves checked in other tests. Unicode, including property support, 
is required for these tests. --/

/((?i)b)/BM

/(?s)(.*X|^B)/BM

/(?s:.*X|^B)/BM

/^[[:alnum:]]/BM

/#/IxMD

/a#/IxMD

/x?+/BM

/x++/BM

/x{1,3}+/BM 

/(x)*+/BM

/^((a+)(?U)([ab]+)(?-U)([bc]+)(\w*))/BM

|8J\$WE\<\.rX\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|BM

|\$\<\.X\+ix\[d1b\!H\#\?vV0vrK\:ZH1\=2M\>iV\;\?aPhFB\<\*vW\@QW\@sO9\}cfZA\-i\'w\%hKd6gt1UJP\,15_\#QY\$M\^Mss_U\/\]\&LK9\[5vQub\^w\[KDD\<EjmhUZ\?\.akp2dF\>qmj\;2\}YWFdYx\.Ap\]hjCPTP\(n28k\+3\;o\&WXqs\/gOXdr\$\:r\'do0\;b4c\(f_Gr\=\"\\4\)\[01T7ajQJvL\$W\~mL_sS\/4h\:x\*\[ZN\=KLs\&L5zX\/\/\>it\,o\:aU\(\;Z\>pW\&T7oP\'2K\^E\:x9\'c\[\%z\-\,64JQ5AeH_G\#KijUKghQw\^\\vea3a\?kka_G\$8\#\`\*kynsxzBLru\'\]k_\[7FrVx\}\^\=\$blx\>s\-N\%j\;D\*aZDnsw\:YKZ\%Q\.Kne9\#hP\?\+b3\(SOvL\,\^\;\&u5\@\?5C5Bhb\=m\-vEh_L15Jl\]U\)0RP6\{q\%L\^_z5E\'Dw6X\b|BM

/(a(?1)b)/BM

/(a(?1)+b)/BM

/a(?P<name1>b|c)d(?P<longername2>e)/BM

/(?:a(?P<c>c(?P<d>d)))(?P<a>a)/BM

/(?P<a>a)...(?P=a)bbb(?P>a)d/BM

/abc(?C255)de(?C)f/BM

/abcde/CBM

/\x{100}/8BM

/\x{1000}/8BM

/\x{10000}/8BM

/\x{100000}/8BM

/\x{10ffff}/8BM

/\x{110000}/8BM

/[\x{ff}]/8BM

/[\x{100}]/8BM

/\x80/8BM

/\xff/8BM

/\x{0041}\x{2262}\x{0391}\x{002e}/D8M
    
/\x{D55c}\x{ad6d}\x{C5B4}/D8M 

/\x{65e5}\x{672c}\x{8a9e}/D8M

/[\x{100}]/8BM

/[Z\x{100}]/8BM

/^[\x{100}\E-\Q\E\x{150}]/B8M

/^[\QĀ\E-\QŐ\E]/B8M

/^[\QĀ\E-\QŐ\E/B8M

/[\p{L}]/BM

/[\p{^L}]/BM

/[\P{L}]/BM

/[\P{^L}]/BM

/[abc\p{L}\x{0660}]/8BM

/[\p{Nd}]/8BM

/[\p{Nd}+-]+/8BM

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8iBM

/A\x{391}\x{10427}\x{ff3a}\x{1fb0}/8BM

/[\x{105}-\x{109}]/8iBM

/( ( (?(1)0|) )*   )/xBM

/(  (?(1)0|)*   )/xBM

/[a]/BM

/[a]/8BM

/[\xaa]/BM

/[\xaa]/8BM

/[^a]/BM

/[^a]/8BM

/[^\xaa]/BM

/[^\xaa]/8BM

/[^\d]/8WB

/[[:^alpha:][:^cntrl:]]+/8WB

/[[:^cntrl:][:^alpha:]]+/8WB

/[[:alpha:]]+/8WB

/[[:^alpha:]\S]+/8WB

/abc(d|e)(*THEN)x(123(*THEN)4|567(b|q)(*THEN)xx)/B

/(((a\2)|(a*)\g<-1>))*a?/B

/((?+1)(\1))/B

/-- End of testinput11 --/
