/-- This test is run only when JIT support is available. It checks for a
successful and an unsuccessful JIT compile and save and restore behaviour,
and a couple of things that are different with JIT. --/

/abc/S+I

/(?(?C1)(?=a)a)/S+I

/(?(?C1)(?=a)a)/S!+I

/abc/S+I>testsavedregex

<testsavedregex
    abc

/a*/SI

/(?(R)a*(?1)|((?R))b)/S+
    aaaabcde
    
/-- Test various compile modes --/ 
    
/abcd/S++
    abcd
    xyz  

/abcd/S+
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++1
    abcd
    ab\P
    ab\P\P
    xyz
    xyz\P

/abcd/S++2
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++3
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++4
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++5
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++6
    abcd
    ab\P
    ab\P\P
    xyz

/abcd/S++7
    abcd
    ab\P
    ab\P\P
    xyz
    
/abcd/S++2I 

/(*NO_START_OPT)a(*:m)b/KS++
    a

/^12345678abcd/mS++
    12345678abcd

/-- Test pattern compilation --/ 

/(?:a|b|c|d|e)(?R)/S++

/(?:a|b|c|d|e)(?R)(?R)/S++

/(a(?:a|b|c|d|e)b){8,16}/S++

/-- End of testinput12 --/
