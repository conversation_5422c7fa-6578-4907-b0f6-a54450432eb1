/-- Tests for reloading pre-compile patterns with UTF-16 or UTF-32 support. */

%-- Generated from: 
    /(?P<cbra1>[aZ\x{400}-\x{10ffff}]{4,}
      [\x{f123}\x{10039}\x{20000}-\x{21234}]?|
      [A-Cx-z\x{100000}-\x{1000a7}\x{101234}])
      (?<cb2>[^az])/x 
       
    In 16-bit mode with options:  S8>testdata/saved16LE-2
                                 FS8>testdata/saved16BE-2
    In 32-bit mode with options:  S8>testdata/saved32LE-2
                                 FS8>testdata/saved32BE-2
--%8x

<!testsaved16LE-2

<!testsaved16BE-2

<!testsaved32LE-2

<!testsaved32BE-2

/-- End of testinput22 --/
