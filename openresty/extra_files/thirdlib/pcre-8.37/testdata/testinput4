/-- This set of tests is for UTF support, excluding Unicode properties. It is
    compatible with all versions of Perl >= 5.10 and both the 8-bit and 16-bit
    PCRE libraries. --/
    
< forbid 9?=ABCDEFfGILMNPTUWXZ<
   
/a.b/8
    acb
    a\x7fb
    a\x{100}b 
    *** Failers
    a\nb  

/a(.{3})b/8
    a\x{4000}xyb 
    a\x{4000}\x7fyb 
    a\x{4000}\x{100}yb 
    *** Failers
    a\x{4000}b 
    ac\ncb 

/a(.*?)(.)/
    a\xc0\x88b

/a(.*?)(.)/8
    a\x{100}b

/a(.*)(.)/
    a\xc0\x88b

/a(.*)(.)/8
    a\x{100}b

/a(.)(.)/
    a\xc0\x92bcd

/a(.)(.)/8
    a\x{240}bcd

/a(.?)(.)/
    a\xc0\x92bcd

/a(.?)(.)/8
    a\x{240}bcd

/a(.??)(.)/
    a\xc0\x92bcd

/a(.??)(.)/8
    a\x{240}bcd

/a(.{3})b/8
    a\x{1234}xyb 
    a\x{1234}\x{4321}yb 
    a\x{1234}\x{4321}\x{3412}b 
    *** Failers
    a\x{1234}b 
    ac\ncb 

/a(.{3,})b/8
    a\x{1234}xyb 
    a\x{1234}\x{4321}yb 
    a\x{1234}\x{4321}\x{3412}b 
    axxxxbcdefghijb 
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
    *** Failers
    a\x{1234}b 

/a(.{3,}?)b/8
    a\x{1234}xyb 
    a\x{1234}\x{4321}yb 
    a\x{1234}\x{4321}\x{3412}b 
    axxxxbcdefghijb 
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
    *** Failers
    a\x{1234}b 

/a(.{3,5})b/8
    a\x{1234}xyb 
    a\x{1234}\x{4321}yb 
    a\x{1234}\x{4321}\x{3412}b 
    axxxxbcdefghijb 
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
    axbxxbcdefghijb 
    axxxxxbcdefghijb 
    *** Failers
    a\x{1234}b 
    axxxxxxbcdefghijb 

/a(.{3,5}?)b/8
    a\x{1234}xyb 
    a\x{1234}\x{4321}yb 
    a\x{1234}\x{4321}\x{3412}b 
    axxxxbcdefghijb 
    a\x{1234}\x{4321}\x{3412}\x{3421}b 
    axbxxbcdefghijb 
    axxxxxbcdefghijb 
    *** Failers
    a\x{1234}b 
    axxxxxxbcdefghijb 

/^[a\x{c0}]/8
    *** Failers
    \x{100}

/(?<=aXb)cd/8
    aXbcd

/(?<=a\x{100}b)cd/8
    a\x{100}bcd

/(?<=a\x{100000}b)cd/8
    a\x{100000}bcd
    
/(?:\x{100}){3}b/8
    \x{100}\x{100}\x{100}b
    *** Failers 
    \x{100}\x{100}b

/\x{ab}/8
    \x{ab} 
    \xc2\xab
    *** Failers 
    \x00{ab}

/(?<=(.))X/8
    WXYZ
    \x{256}XYZ 
    *** Failers
    XYZ 

/[^a]+/8g
    bcd
    \x{100}aY\x{256}Z 
    
/^[^a]{2}/8
    \x{100}bc
 
/^[^a]{2,}/8
    \x{100}bcAa

/^[^a]{2,}?/8
    \x{100}bca

/[^a]+/8ig
    bcd
    \x{100}aY\x{256}Z 
    
/^[^a]{2}/8i
    \x{100}bc
 
/^[^a]{2,}/8i
    \x{100}bcAa

/^[^a]{2,}?/8i
    \x{100}bca

/\x{100}{0,0}/8
    abcd
 
/\x{100}?/8
    abcd
    \x{100}\x{100} 

/\x{100}{0,3}/8 
    \x{100}\x{100} 
    \x{100}\x{100}\x{100}\x{100} 
    
/\x{100}*/8
    abce
    \x{100}\x{100}\x{100}\x{100} 

/\x{100}{1,1}/8
    abcd\x{100}\x{100}\x{100}\x{100} 

/\x{100}{1,3}/8
    abcd\x{100}\x{100}\x{100}\x{100} 

/\x{100}+/8
    abcd\x{100}\x{100}\x{100}\x{100} 

/\x{100}{3}/8
    abcd\x{100}\x{100}\x{100}XX

/\x{100}{3,5}/8
    abcd\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}XX

/\x{100}{3,}/8
    abcd\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}XX

/(?<=a\x{100}{2}b)X/8+
    Xyyya\x{100}\x{100}bXzzz

/\D*/8
  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa

/\D*/8
  \x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}\x{100}

/\D/8
    1X2
    1\x{100}2 
  
/>\S/8
    > >X Y
    > >\x{100} Y
  
/\d/8
    \x{100}3
    
/\s/8
    \x{100} X
    
/\D+/8
    12abcd34
    *** Failers
    1234  

/\D{2,3}/8
    12abcd34
    12ab34
    *** Failers  
    1234
    12a34  

/\D{2,3}?/8
    12abcd34
    12ab34
    *** Failers  
    1234
    12a34  

/\d+/8
    12abcd34
    *** Failers

/\d{2,3}/8
    12abcd34
    1234abcd
    *** Failers  
    1.4 

/\d{2,3}?/8
    12abcd34
    1234abcd
    *** Failers  
    1.4 

/\S+/8
    12abcd34
    *** Failers
    \    \ 

/\S{2,3}/8
    12abcd34
    1234abcd
    *** Failers
    \     \  

/\S{2,3}?/8
    12abcd34
    1234abcd
    *** Failers
    \     \  

/>\s+</8+
    12>      <34
    *** Failers

/>\s{2,3}</8+
    ab>  <cd
    ab>   <ce
    *** Failers
    ab>    <cd 

/>\s{2,3}?</8+
    ab>  <cd
    ab>   <ce
    *** Failers
    ab>    <cd 

/\w+/8
    12      34
    *** Failers
    +++=*! 

/\w{2,3}/8
    ab  cd
    abcd ce
    *** Failers
    a.b.c

/\w{2,3}?/8
    ab  cd
    abcd ce
    *** Failers
    a.b.c

/\W+/8
    12====34
    *** Failers
    abcd 

/\W{2,3}/8
    ab====cd
    ab==cd
    *** Failers
    a.b.c

/\W{2,3}?/8
    ab====cd
    ab==cd
    *** Failers
    a.b.c

/[\x{100}]/8
    \x{100}
    Z\x{100}
    \x{100}Z
    *** Failers 

/[Z\x{100}]/8
    Z\x{100}
    \x{100}
    \x{100}Z
    *** Failers 

/[\x{100}\x{200}]/8
   ab\x{100}cd
   ab\x{200}cd
   *** Failers  

/[\x{100}-\x{200}]/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{111}cd 
   *** Failers  

/[z-\x{200}]/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{111}cd 
   abzcd
   ab|cd  
   *** Failers  

/[Q\x{100}\x{200}]/8
   ab\x{100}cd
   ab\x{200}cd
   Q? 
   *** Failers  

/[Q\x{100}-\x{200}]/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{111}cd 
   Q? 
   *** Failers  

/[Qz-\x{200}]/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{111}cd 
   abzcd
   ab|cd  
   Q? 
   *** Failers  

/[\x{100}\x{200}]{1,3}/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{200}\x{100}\x{200}\x{100}cd
   *** Failers  

/[\x{100}\x{200}]{1,3}?/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{200}\x{100}\x{200}\x{100}cd
   *** Failers  

/[Q\x{100}\x{200}]{1,3}/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{200}\x{100}\x{200}\x{100}cd
   *** Failers  

/[Q\x{100}\x{200}]{1,3}?/8
   ab\x{100}cd
   ab\x{200}cd
   ab\x{200}\x{100}\x{200}\x{100}cd
   *** Failers  

/(?<=[\x{100}\x{200}])X/8
    abc\x{200}X
    abc\x{100}X 
    *** Failers
    X  

/(?<=[Q\x{100}\x{200}])X/8
    abc\x{200}X
    abc\x{100}X 
    abQX 
    *** Failers
    X  

/(?<=[\x{100}\x{200}]{3})X/8
    abc\x{100}\x{200}\x{100}X
    *** Failers
    abc\x{200}X
    X  

/[^\x{100}\x{200}]X/8
    AX
    \x{150}X
    \x{500}X 
    *** Failers
    \x{100}X
    \x{200}X   

/[^Q\x{100}\x{200}]X/8
    AX
    \x{150}X
    \x{500}X 
    *** Failers
    \x{100}X
    \x{200}X   
    QX 

/[^\x{100}-\x{200}]X/8
    AX
    \x{500}X 
    *** Failers
    \x{100}X
    \x{150}X
    \x{200}X   

/[z-\x{100}]/8i
    z
    Z 
    \x{100}
    *** Failers
    \x{102}
    y    

/[\xFF]/
    >\xff<

/[\xff]/8
    >\x{ff}<

/[^\xFF]/
    XYZ

/[^\xff]/8
    XYZ
    \x{123} 

/^[ac]*b/8
  xb

/^[ac\x{100}]*b/8
  xb

/^[^x]*b/8i
  xb

/^[^x]*b/8
  xb
  
/^\d*b/8
  xb 

/(|a)/g8
    catac
    a\x{256}a 

/^\x{85}$/8i
    \x{85}

/^ሴ/8
    ሴ 

/^\ሴ/8
    ሴ 

"(?s)(.{1,5})"8
    abcdefg
    ab

/a*\x{100}*\w/8
    a 

/\S\S/8g
    A\x{a3}BC
    
/\S{2}/8g
    A\x{a3}BC
    
/\W\W/8g
    +\x{a3}== 

/\W{2}/8g
    +\x{a3}== 

/\S/8g
    \x{442}\x{435}\x{441}\x{442}

/[\S]/8g
    \x{442}\x{435}\x{441}\x{442}

/\D/8g
    \x{442}\x{435}\x{441}\x{442}

/[\D]/8g
    \x{442}\x{435}\x{441}\x{442}

/\W/8g
    \x{2442}\x{2435}\x{2441}\x{2442}

/[\W]/8g
    \x{2442}\x{2435}\x{2441}\x{2442}
    
/[\S\s]*/8
    abc\n\r\x{442}\x{435}\x{441}\x{442}xyz 

/[\x{41f}\S]/8g
    \x{442}\x{435}\x{441}\x{442}

/.[^\S]./8g
    abc def\x{442}\x{443}xyz\npqr

/.[^\S\n]./8g
    abc def\x{442}\x{443}xyz\npqr

/[[:^alnum:]]/8g  
    +\x{2442}
    
/[[:^alpha:]]/8g 
    +\x{2442}
    
/[[:^ascii:]]/8g 
    A\x{442}
    
/[[:^blank:]]/8g 
    A\x{442}
    
/[[:^cntrl:]]/8g 
    A\x{442}
    
/[[:^digit:]]/8g 
    A\x{442}
    
/[[:^graph:]]/8g 
    \x19\x{e01ff}
    
/[[:^lower:]]/8g 
    A\x{422}
    
/[[:^print:]]/8g 
    \x{19}\x{e01ff}
    
/[[:^punct:]]/8g 
    A\x{442}
    
/[[:^space:]]/8g 
    A\x{442}
    
/[[:^upper:]]/8g 
    a\x{442}
    
/[[:^word:]]/8g  
    +\x{2442}
    
/[[:^xdigit:]]/8g
    M\x{442}

/[^ABCDEFGHIJKLMNOPQRSTUVWXYZÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞĀĂĄĆĈĊČĎĐĒĔĖĘĚĜĞĠĢĤĦĨĪĬĮİĲĴĶĹĻĽĿŁŃŅŇŊŌŎŐŒŔŖŘŚŜŞŠŢŤŦŨŪŬŮŰŲŴŶŸŹŻŽƁƂƄƆƇƉƊƋƎƏƐƑƓƔƖƗƘƜƝƟƠƢƤƦƧƩƬƮƯƱƲƳƵƷƸƼǄǇǊǍǏǑǓǕǗǙǛǞǠǢǤǦǨǪǬǮǱǴǶǷǸǺǼǾȀȂȄȆȈȊȌȎȐȒȔȖȘȚȜȞȠȢȤȦȨȪȬȮȰȲȺȻȽȾɁΆΈΉΊΌΎΏΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩΪΫϒϓϔϘϚϜϞϠϢϤϦϨϪϬϮϴϷϹϺϽϾϿЀЁЂЃЄЅІЇЈЉЊЋЌЍЎЏАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯѠѢѤѦѨѪѬѮѰѲѴѶѸѺѼѾҀҊҌҎҐҒҔҖҘҚҜҞҠҢҤҦҨҪҬҮҰҲҴҶҸҺҼҾӀӁӃӅӇӉӋӍӐӒӔӖӘӚӜӞӠӢӤӦӨӪӬӮӰӲӴӶӸԀԂԄԆԈԊԌԎԱԲԳԴԵԶԷԸԹԺԻԼԽԾԿՀՁՂՃՄՅՆՇՈՉՊՋՌՍՎՏՐՑՒՓՔՕՖႠႡႢႣႤႥႦႧႨႩႪႫႬႭႮႯႰႱႲႳႴႵႶႷႸႹႺႻႼႽႾႿჀჁჂჃჄჅḀḂḄḆḈḊḌḎḐḒḔḖḘḚḜḞḠḢḤḦḨḪḬḮḰḲḴḶḸḺḼḾṀṂṄṆṈṊṌṎṐṒṔṖṘṚṜṞṠṢṤṦṨṪṬṮṰṲṴṶṸṺṼṾẀẂẄẆẈẊẌẎẐẒẔẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼẾỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸἈἉἊἋἌἍἎἏἘἙἚἛἜἝἨἩἪἫἬἭἮἯἸἹἺἻἼἽἾἿὈὉὊὋὌὍὙὛὝὟὨὩὪὫὬὭὮὯᾸᾹᾺΆῈΈῊΉῘῙῚΊῨῩῪΎῬῸΌῺΏabcdefghijklmnopqrstuvwxyzªµºßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿāăąćĉċčďđēĕėęěĝğġģĥħĩīĭįıĳĵķĸĺļľŀłńņňŉŋōŏőœŕŗřśŝşšţťŧũūŭůűųŵŷźżžſƀƃƅƈƌƍƒƕƙƚƛƞơƣƥƨƪƫƭưƴƶƹƺƽƾƿǆǉǌǎǐǒǔǖǘǚǜǝǟǡǣǥǧǩǫǭǯǰǳǵǹǻǽǿȁȃȅȇȉȋȍȏȑȓȕȗșțȝȟȡȣȥȧȩȫȭȯȱȳȴȵȶȷȸȹȼȿɀɐɑɒɓɔɕɖɗɘəɚɛɜɝɞɟɠɡɢɣɤɥɦɧɨɩɪɫɬɭɮɯɰɱɲɳɴɵɶɷɸɹɺɻɼɽɾɿʀʁʂʃʄʅʆʇʈʉʊʋʌʍʎʏʐʑʒʓʔʕʖʗʘʙʚʛʜʝʞʟʠʡʢʣʤʥʦʧʨʩʪʫʬʭʮʯΐάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώϐϑϕϖϗϙϛϝϟϡϣϥϧϩϫϭϯϰϱϲϳϵϸϻϼабвгдежзийклмнопрстуфхцчшщъыьэюяѐёђѓєѕіїјљњћќѝўџѡѣѥѧѩѫѭѯѱѳѵѷѹѻѽѿҁҋҍҏґғҕҗҙқҝҟҡңҥҧҩҫҭүұҳҵҷҹһҽҿӂӄӆӈӊӌӎӑӓӕӗәӛӝӟӡӣӥӧөӫӭӯӱӳӵӷӹԁԃԅԇԉԋԍԏաբգդեզէըթժիլխծկհձղճմյնշոչպջռսվտրցւփքօֆևᴀᴁᴂᴃᴄᴅᴆᴇᴈᴉᴊᴋᴌᴍᴎᴏᴐᴑᴒᴓᴔᴕᴖᴗᴘᴙᴚᴛᴜᴝᴞᴟᴠᴡᴢᴣᴤᴥᴦᴧᴨᴩᴪᴫᵢᵣᵤᵥᵦᵧᵨᵩᵪᵫᵬᵭᵮᵯᵰᵱᵲᵳᵴᵵᵶᵷᵹᵺᵻᵼᵽᵾᵿᶀᶁᶂᶃᶄᶅᶆᶇᶈᶉᶊᶋᶌᶍᶎᶏᶐᶑᶒᶓᶔᶕᶖᶗᶘᶙᶚḁḃḅḇḉḋḍḏḑḓḕḗḙḛḝḟḡḣḥḧḩḫḭḯḱḳḵḷḹḻḽḿṁṃṅṇṉṋṍṏṑṓṕṗṙṛṝṟṡṣṥṧṩṫṭṯṱṳṵṷṹṻṽṿẁẃẅẇẉẋẍẏẑẓẕẖẗẘẙẚẛạảấầẩẫậắằẳẵặẹẻẽếềểễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹἀἁἂἃἄἅἆἇἐἑἒἓἔἕἠἡἢἣἤἥἦἧἰἱἲἳἴἵἶἷὀὁὂὃὄὅὐὑὒὓὔὕὖὗὠὡὢὣὤὥὦὧὰάὲέὴήὶίὸόὺύὼώᾀᾁᾂᾃᾄᾅᾆᾇᾐᾑᾒᾓᾔᾕᾖᾗᾠᾡᾢᾣᾤᾥᾦᾧᾰᾱᾲᾳᾴᾶᾷιῂῃῄῆῇῐῑῒΐῖῗῠῡῢΰῤῥῦῧῲῳῴῶῷⲁⲃⲅⲇⲉⲋⲍⲏⲑⲓⲕⲗⲙⲛⲝⲟⲡⲣⲥⲧⲩⲫⲭⲯⲱⲳⲵⲷⲹⲻⲽⲿⳁⳃⳅⳇⳉⳋⳍⳏⳑⳓⳕⳗⳙⳛⳝⳟⳡⳣⳤⴀⴁⴂⴃⴄⴅⴆⴇⴈⴉⴊⴋⴌⴍⴎⴏⴐⴑⴒⴓⴔⴕⴖⴗⴘⴙⴚⴛⴜⴝⴞⴟⴠⴡⴢⴣⴤⴥﬀﬁﬂﬃﬄﬅﬆﬓﬔﬕﬖﬗ\d-_^]/8

/^[^d]*?$/
    abc

/^[^d]*?$/8
    abc

/^[^d]*?$/i
    abc

/^[^d]*?$/8i
    abc

/(?i)[\xc3\xa9\xc3\xbd]|[\xc3\xa9\xc3\xbdA]/8

/^[a\x{c0}]b/8
    \x{c0}b
    
/^([a\x{c0}]*?)aa/8
    a\x{c0}aaaa/ 

/^([a\x{c0}]*?)aa/8
    a\x{c0}aaaa/ 
    a\x{c0}a\x{c0}aaa/ 

/^([a\x{c0}]*)aa/8
    a\x{c0}aaaa/ 
    a\x{c0}a\x{c0}aaa/ 

/^([a\x{c0}]*)a\x{c0}/8
    a\x{c0}aaaa/ 
    a\x{c0}a\x{c0}aaa/ 

/A*/g8
    AAB\x{123}BAA

/(abc)\1/8i
   abc

/(abc)\1/8
   abc

/a(*:a\x{1234}b)/8K
    abc

/a(*:a£b)/8K 
    abc

/-- Noncharacters --/

/./8
    \x{fffe}
    \x{ffff}
    \x{1fffe}
    \x{1ffff}
    \x{2fffe}
    \x{2ffff}
    \x{3fffe}
    \x{3ffff}
    \x{4fffe}
    \x{4ffff}
    \x{5fffe}
    \x{5ffff}
    \x{6fffe}
    \x{6ffff}
    \x{7fffe}
    \x{7ffff}
    \x{8fffe}
    \x{8ffff}
    \x{9fffe}
    \x{9ffff}
    \x{afffe}
    \x{affff}
    \x{bfffe}
    \x{bffff}
    \x{cfffe}
    \x{cffff}
    \x{dfffe}
    \x{dffff}
    \x{efffe}
    \x{effff}
    \x{ffffe}
    \x{fffff}
    \x{10fffe}
    \x{10ffff}
    \x{fdd0}
    \x{fdd1}
    \x{fdd2}
    \x{fdd3}
    \x{fdd4}
    \x{fdd5}
    \x{fdd6}
    \x{fdd7}
    \x{fdd8}
    \x{fdd9}
    \x{fdda}
    \x{fddb}
    \x{fddc}
    \x{fddd}
    \x{fdde}
    \x{fddf}
    \x{fde0}
    \x{fde1}
    \x{fde2}
    \x{fde3}
    \x{fde4}
    \x{fde5}
    \x{fde6}
    \x{fde7}
    \x{fde8}
    \x{fde9}
    \x{fdea}
    \x{fdeb}
    \x{fdec}
    \x{fded}
    \x{fdee}
    \x{fdef}

/^\d*\w{4}/8
    1234
    123 
    
/^[^b]*\w{4}/8
    aaaa
    aaa  
 
/^[^b]*\w{4}/8i
    aaaa
    aaa  
 
/^\x{100}*.{4}/8
    \x{100}\x{100}\x{100}\x{100}
    \x{100}\x{100}\x{100}

/^\x{100}*.{4}/8i
    \x{100}\x{100}\x{100}\x{100}
    \x{100}\x{100}\x{100}

/^a+[a\x{200}]/8
    aa

/^.\B.\B./8
    \x{10123}\x{10124}\x{10125}

/^#[^\x{ffff}]#[^\x{ffff}]#[^\x{ffff}]#/8
    #\x{10000}#\x{100}#\x{10ffff}#

"[\S\V\H]"8

/\C(\W?ſ)'?{{/8
    \\C(\\W?ſ)'?{{

/-- End of testinput4 --/
