local ngx_worker_cache = require "ngx_worker_cache"
local http = require "ufclib.Http"
local utils = require "utils"
local cjson = require "cjson.safe"
local tonumber = tonumber

local _M = {
	["doc"] = "dump interface",
}

-- 流量匹配，是否要落到db
function _M.dump_match(headers, from_service, to_service)
	-- check下配置
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		return false
	end

	local configs = static_cache["configs"]
	if not configs then 
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		return false
	end

	local dump = configs["dump"]
	if not dump then 
		return false
	end

	if not dump["enable"] or dump["enable"] == 0 then 
		return false
	end

	local contents = dump["dump"]
	if not contents then 
		return false
	end
	
	for dump_id , content in pairs(contents) do
		while true do
			local start_time = content["start_time"]
			local end_time = content["end_time"]
			local time_now = ngx.now()

			--只要当前时间不在指定时间范围内就不会触发故障注入
			--starttime 和 endtime可以全不指定或者只指定一个
			if start_time and time_now < start_time then
				break
			end

			if end_time and time_now > end_time then
				break
			end

            -- from service 和 to_service 必选
			if not content["from_service"] or not content["to_service"] then
				break
			end

			if content["from_service"] ~= from_service and content["from_service"] ~= "all" then
				break
			end

			if content["to_service"] ~= to_service then
				break
			end

			return true
		end
	end

	return false 
end

function _M.dump_data_cleanup(dump)
	local time_now = ngx.now()
	local dump_data = dump["dump"]
	local timeout_id = {}
	for id, content in pairs(dump_data) do
		local end_time = content["end_time"]
		if time_now > end_time then
			--故障信息过期
			table.insert(timeout_id, id)
		end
	end

	for _, id in pairs(timeout_id) do
		dump_data[id] = nil 
		ngx.log(ngx.INFO, string.format("dump %s is timeout, should be cleanup", id))
	end
end

function _M.get_dump_db_config()
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return nil
	end

	if not configs["dump_db"] then
		return nil
	end

	return configs["dump_db"]
end


function _M.send_dump_request_to_db(dummy, ctxs, from_service,  to_service)
	if ctxs["headers"]["x-ufc-dump-flag"] then
		ctxs["headers"]["x-ufc-dump-flag"] = nil 
	end

	local data = {
		["method"]		  =  ctxs["req_method"],
		["uri"] 		  =  ctxs["req_uri"],
		["headers"] 	  =  ctxs["headers"],
		["connTimeout"]   =  ctxs["x-ufc-connect-timeout"],
		["rwriteTimeout"] =  ctxs["x-ufc-rw-timeout"],
		["body"] 		  =  ctxs["req_body"] ,
	}
	local request_args = {
		["fromService"] =  from_service,
		["toService"] 	=  to_service,
		["logid"] 		=  ctxs["ufc_logid"],
		["timestamp"] 	=  tostring(ngx.now()),
		["fromIp"] 		=  ctxs["dump_from_ip"],
		["httpType"] 	=  "request",
		["data"]        =  data,
	}

    local dump_db_config = _M.get_dump_db_config()
	if dump_db_config == nil then
		ngx.log(ngx.ERR, string.format("_send_request_to_dump_request error [dump_db is nil]"))
		return
	end

	if not (dump_db_config["domain"] and dump_db_config["port"]) then
		ngx.log(ngx.ERR, string.format("_send_request_to_dump_request error domain or port error [to_service:%s] [uri:%s] ", dump_db_config["target"], dump_db_config["uri"]))
		return
	end

	local addr = {
		["domain"]  = dump_db_config["domain"],
		["port"]    = dump_db_config["port"],
	}

	local args_str = cjson.encode(request_args)
	local args_len = string.len(args_str)

	local header = {
    	["Content-Type"]    = "application/json",
		["Content-Length"]  = args_len,
	}

	local timeout = {
		["connect_timeout"] = 1000,
		["read_timeout"]    = 3000,
	}
        
	local ret, err = http.post(addr, "/rest/2.0/pvcp/table?method=insert", header, args_str, timeout)
	request_args["data"] = nil -- 具体请求header body等信息不打印日志
	local log_args_str = cjson.encode(request_args)
	ngx.log(ngx.NOTICE, string.format("_send_request_to_dump_db success [args:%s] [header:%s] [ret:%s] [err:%s]", log_args_str, cjson.encode(header), cjson.encode(ret), cjson.encode(err)))
end

function _M.send_dump_response_to_db(dummy, ctxs, from_service, to_service)
	local data = {
		["status"]  = ctxs["resp_status"],-- ngx.status,
		["headers"] = ctxs["resp_header"], -- ngx.resp.get_headers(),
		["body"]    = ctxs["resp_body"],
	}
	local request_args = {
		["fromService"] =  from_service,
		["toService"] 	=  to_service,
		["logid"] 		=  ctxs["ufc_logid"],
		["timestamp"] 	=  tostring(ngx.now()),
		["fromIp"] 		=  ctxs["dump_from_ip"],
		["httpType"] 	=  "response",
		["data"]        = data,
	}

	local dump_db_config = _M.get_dump_db_config()
	if dump_db_config == nil then
		ngx.log(ngx.ERR, string.format("_send_response_to_dump_db error [dump_db config is nil]"))
		return   
	end

	if not (dump_db_config["domain"] and dump_db_config["port"]) then
		ngx.log(ngx.ERR, string.format("_send_response_to_dump_db error domain or port error [to_service:%s] [uri:%s] ", dump_db_config["target"], dump_db_config["uri"]))
		return
	end

	local addr = {
		["domain"]  = dump_db_config["domain"],
		["port"]    = dump_db_config["port"],
	}

	local args_str = cjson.encode(request_args)
	local args_len = string.len(args_str)

	local header = {
		["Content-Type"]    = "application/json",
		["Content-Length"]  = args_len,
	}

	local timeout = {
		["connect_timeout"] = 1000,
		["read_timeout"]    = 3000,
	}
        
	local ret, err = http.post(addr, "/rest/2.0/pvcp/table?method=insert", header, args_str, timeout)

	request_args["data"] = nil
	local log_args_str = cjson.encode(request_args)  -- 具体请求header body等信息不打印日志
	ngx.log(ngx.NOTICE, string.format("_send_response_to_dump_db success [args:%s] [header:%s] [ret:%s] [err:%s]", log_args_str, cjson.encode(header), cjson.encode(ret), cjson.encode(err)))
end

return _M

