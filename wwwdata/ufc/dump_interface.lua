local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local lock = require "lock"
local cjson = require "cjson.safe"

local _M = {
	["doc"] = "ufc dump interface",
}

function _M.output(res)
	res["status"] = 200
	ngx.status = 200 
	ngx.say(cjson.encode(res))
end



function _M.start(args, res)
	ngx.req.read_body()
	local body = ngx.req.get_body_data()
	-- 加锁
	if not lock.lock_config("dump") then 
		ngx.log(ngx.WARN, string.format("interface:dump start, worker id:%d, try to get fault lock failed", ngx.worker.pid()))
		res["error_msg"] = "E_LOCK_FAILED"
		return _M.output(res)
	end

	local dump_data = cjson.decode(body)
	if not dump_data then
		ngx.log(ngx.WARN, string.format("dump data is not a json"))
		lock.unlock_config("dump")
		res["error_msg"] = "E_NOT_JSON"
		return _M.output(res)
	end

	-- 从共享内存读取数据，避免被覆盖
	local share_dump_str = ngx.shared.dump:get("dump")
	local share_dump

	local share_dump_is_valid = true 
	if not share_dump_str then
		share_dump_is_valid = false
		ngx.log(ngx.WARN, string.format("interface:dump start, worker id:%d, share_dump_str is nil", ngx.worker.pid()))
	else 
		share_dump = cjson.decode(share_dump_str)
		if not share_dump then
			share_dump_is_valid = false
			ngx.log(ngx.WARN, string.format("interface:dump start, worker id:%d, share_dump_str is not json", ngx.worker.pid()))
		end
	end

	if share_dump_is_valid == false then 
		share_dump = {}
		share_dump["enable"] = 1
		share_dump["dump"] = {}
	end 

	share_dump["enable"] = 1

	if not share_dump["dump"] then
		share_dump["dump"] = {}
	end

	local dump = share_dump["dump"]

	if dump_data["dump_id"] ~= nil then
		dump[tostring(dump_data["dump_id"])] = dump_data
		ngx.log(ngx.NOTICE, string.format("dump id %s, add dump config success", dump_data["dump_id"]))
	else 
		ngx.log(ngx.WARN, string.format("dump id %s not exist", dump_data["dump_id"]))
		res["error_msg"] = "E_DUMP_ID_MISS"
		lock.unlock_config("dump")
		return _M.output(res)
	end

	--更新共享内存中的dump信息
	local dump_data = cjson.encode(share_dump)
	local shared_dump_data = ngx.shared.dump
	shared_dump_data:set("dump", dump_data)
	lock.unlock_config("dump")
	res["error_msg"] = "E_OK"
	return _M.output(res)
end

function _M.stop(args, res)
	local dump_id = args["dump_id"]

	if not lock.lock_config("dump") then 
		ngx.log(ngx.WARN, string.format("interface:dump stop, worker id:%d, try to get fault lock failed", ngx.worker.pid()))
		res["error_msg"] = "E_LOCK_FAILED"
		return _M.output(res)
	end

	-- 从共享内存读取数据，避免被覆盖
	local share_dump_str = ngx.shared.dump:get("dump")
	local share_dump
	if not share_dump_str then
		share_dump = {}
		ngx.log(ngx.WARN, string.format("interface:dump stop, worker id:%d, share_dump_str is nil", ngx.worker.pid()))
	else 
		share_dump = cjson.decode(share_dump_str)
		if not share_dump then
			share_dump = {}
			ngx.log(ngx.WARN, string.format("interface:stop, worker id:%d, share_dump_str is not json", ngx.worker.pid()))
		end
	end

	if not share_dump["dump"] then
		res["error_msg"] = "E_OK"
		lock.unlock_config("dump")
		return _M.output(res)
	end

	if dump_id == nil then
		--没有指定 dump_id，所有dump信息都停止
		share_dump["enable"] = 0
		share_dump = nil
	else
		--指定故障id，只停止对应的故障
		share_dump["dump"][tostring(dump_id)] = nil
	end

	--更新共享内存中的故障信息
	local dump_data = cjson.encode(share_dump)
	local shared_dump_data = ngx.shared.dump
	shared_dump_data:set("dump", dump_data)
	lock.unlock_config("dump")
	ngx.log(ngx.WARN, string.format("dump id %s, stop dump success", dump_id))
	res["dump_id"] = dump_id
	res["error_msg"] = "E_OK"
	return _M.output(res)
end 

--查询dump配置是否已经写入ufc-agent
--E_QUERY_FAILED查询失败
--E_QUERY_YES故障已经写入
--E_QUERY_NO故障没有写入
function _M.query(args, res)
	local dump_id = args["dump_id"]
	if not dump_id then
		ngx.log(ngx.WARN, string.format("dump id miss"))
		res["error_msg"] = "E_QUERY_FAILED"
		return _M.output(res)
	end

	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		res["error_msg"] = "E_DUMP_NO"
		return _M.output(res)
	end

	local configs = static_cache["configs"]
	if not configs then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache][configs] is nil"))
		res["error_msg"] = "E_DUMP_NO"
		return _M.output(res)
	end

	local dump = configs["dump"]
	if not dump then
		res["error_msg"] = "E_DUMP_NO"
		return _M.output(res)
	end

	if dump["enable"] == nil or dump["enable"] == 0 then
		res["error_msg"] = "E_DUMP_NO"
		return _M.output(res)
	end

	if dump["dump"] == nil then
		res["error_msg"] = "E_DUMP_NO"
		return _M.output(res)
	end

	if dump["dump"][dump_id] == nil then
		res["error_msg"] = "E_DUMP_NO"
		return _M.output(res)
	end

	if dump["dump"][dump_id] then
		res["error_msg"] = "E_DUMP_YES"
		return _M.output(res)
	end

	res["error_msg"] = "E_DUMP_NO"
	return _M.output(res)
end


function _M.main()
	local start_time =  os.clock()
	ngx.var.ufc_time = math.floor(ngx.now())
	ngx.ctx.ufc_ctxs = {}
	local ctxs = ngx.ctx.ufc_ctxs 

	local args, cmd
	args = ngx.req.get_uri_args()
	local headers = ngx.req.get_headers()
	local res = utils.gen_ufc_log(headers, ctxs, args)

	if not ngx_worker_cache.is_inited() then
		res["error_msg"] = "E_RETRY"
		_M.output(res)
		return
	end

	cmd = args["cmd"]
	ctxs["method"] = cmd
	--ngx.log(ngx.DEBUG, string.format("bypass cmd is %s", cmd))
	if not _M.cmds[cmd] then
		res["error_msg"] = "E_NO_SUPPORT"
		_M.output(res)
	else
		_M.cmds[cmd](args, res, ctxs) -- args是get_uri_args()
	end

	local end_time = os.clock()
	ctxs["ufc_cost_time"] = math.floor((end_time - start_time) * 1000 * 1000) * 0.001
end


_M["cmds"] = {
    ["start"] = _M.start,
 	["stop"] = _M.stop,
	["query"] = _M.query,
}

_M.main()