local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local cjson = require "cjson.safe"
local lock = require "lock"

local _M = {
	["doc"] = "ufc fault inject",
}

function _M.output(res)
	res["status"] = 200
	ngx.status = 200 
	ngx.say(cjson.encode(res))
end

function _M.inject(args, res)
	ngx.req.read_body()
	local body = ngx.req.get_body_data()
	if not lock.lock_config("fault") then 
		ngx.log(ngx.WARN, string.format("interface:inject, worker id:%d, try to get fault lock failed", ngx.worker.pid()))
		res["error_msg"] = "E_LOCK_FAILED"
		return _M.output(res)
	end

	local fault_data = cjson.decode(body)
	if not fault_data then
		lock.unlock_config("fault")
		ngx.log(ngx.WARN, string.format("inject data is not a json"))
		res["error_msg"] = "E_NOT_JSON"
		return _M.output(res)
	end

	-- 从共享内存读取数据，避免被覆盖
	local share_fault_str = ngx.shared.fault:get("fault")
	local share_fault

	local config_is_valid = true 
	if not share_fault_str then
		config_is_valid = false
		ngx.log(ngx.WARN, string.format("interface:inject, worker id:%d, share_fault_str is nil", ngx.worker.pid()))
	else 
		share_fault = cjson.decode(share_fault_str)
		if not share_fault then
			config_is_valid = false
			ngx.log(ngx.WARN, string.format("interface:inject, worker id:%d, share_fault_str is not json", ngx.worker.pid()))
		end
	end


	if config_is_valid == false then
		share_fault = {}
		share_fault["enable"] = 1
		share_fault["fault"] = {}
	end
	
	share_fault["enable"] = 1
	if not share_fault["fault"] then
		share_fault["fault"] = {}
	end

	local fault = share_fault["fault"]

	if fault_data["id"] ~= nil then
		fault[fault_data["id"]] = fault_data
		ngx.log(ngx.NOTICE, string.format("fault id %d, inject fault success", fault_data["id"]))
		goto done
	end

	for fault_id, fault_content in pairs(fault_data) do
		if not fault_id then
			ngx.log(ngx.WARN, string.format("fault id miss"))
			res["error_msg"] = "E_NOT_FAULT_ID"
			lock.unlock_config("fault")
			return _M.output(res)
		end
		fault[fault_id] = fault_content
		ngx.log(ngx.NOTICE, string.format("fault id %d, inject fault success", fault_id))
	end

	:: done ::
	--更新共享内存中的故障信息
	local fault_data = cjson.encode(share_fault)
	local shared_fault_data = ngx.shared.fault
	shared_fault_data:set("fault", fault_data)
	lock.unlock_config("fault")
	res["error_msg"] = "E_OK"
	return _M.output(res)
end

function _M.hit(args, res)
	local fault_id = args["id"]
	if not fault_id then
		ngx.log(ngx.WARN, string.format("fault id miss"))
		res["error_msg"] = "E_NOT_FAULT_ID"
		return _M.output(res)
	end

	local shared_fault_data = ngx.shared.fault
	local id = "fault-hit-" .. fault_id
	local hit_val = shared_fault_data:get(id)

	if hit_val == nil then
		hit_val = 0
	end

	ngx.log(ngx.NOTICE, string.format("fault key %s, get hit %d", id, hit_val))

	res["hit"] = hit_val
	res["error_msg"] = "E_OK"
	return _M.output(res)
end


function _M.stop(args, res)
	local fault_id = args["id"]

	if not lock.lock_config("fault") then 
		ngx.log(ngx.WARN, string.format("interface:stop, worker id:%d, try to get fault lock failed", ngx.worker.pid()))
		res["error_msg"] = "E_LOCK_FAILED"
		return _M.output(res)
	end

	-- 从共享内存读取数据，避免被覆盖
	local share_fault_str = ngx.shared.fault:get("fault")
	local share_fault
	if not share_fault_str then
		share_fault = {}
		ngx.log(ngx.WARN, string.format("interface:stop, worker id:%d, share_fault_str is nil", ngx.worker.pid()))
	else 
		share_fault = cjson.decode(share_fault_str)
		if not share_fault then
			share_fault = {}
			ngx.log(ngx.WARN, string.format("interface:stop, worker id:%d, share_fault_str is not json", ngx.worker.pid()))
		end
	end

	if not share_fault then
		share_fault = {}
		share_fault["enable"] = 1
		share_fault["fault"] = {}
	end
	
	share_fault["enable"] = 1
	if not share_fault["fault"] then
		share_fault["fault"] = {}
	end

	if not share_fault["fault"] then
		res["error_msg"] = "E_OK"
		lock.unlock_config("fault")
		return _M.output(res)
	end

	if fault_id == nil then
		--没有指定fault_id，所有故障都停止
		share_fault["enable"] = 0
		share_fault = nil
	else
		--指定故障id，只停止对应的故障
		share_fault["fault"][fault_id] = nil
	end

	--更新共享内存中的故障信息
	local fault_data = cjson.encode(share_fault)
	local shared_fault_data = ngx.shared.fault
	shared_fault_data:set("fault", fault_data)
	lock.unlock_config("fault")

	ngx.log(ngx.WARN, string.format("fault id %d, stop fault success", fault_id))
	
	res["error_msg"] = "E_OK"
	return _M.output(res)
end 

--查询故障是否已经写入ufc-agent
--E_QUERY_FAILED查询失败
--E_QUERY_YES故障已经写入
--E_QUERY_NO故障没有写入
-- 查询接口从worker进程查看配置即可，不必调整为共享内存
function _M.query(args, res)
	local fault_id = args["id"]
	if not fault_id then	
		ngx.log(ngx.WARN, string.format("fault id miss"))
		res["error_msg"] = "E_QUERY_FAILED"
		return _M.output(res)
	end

	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		res["error_msg"] = "E_FAULT_NO"
		return _M.output(res)
	end

	local configs = static_cache["configs"]
	if not configs then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache][configs] is nil"))
		res["error_msg"] = "E_FAULT_NO"
		return _M.output(res)
	end

	local fault = configs["fault"]
	if not fault then
		res["error_msg"] = "E_FAULT_NO"
		return _M.output(res)
	end

	if fault["enable"] == nil or fault["enable"] == 0 then
		res["error_msg"] = "E_FAULT_NO"
		return _M.output(res)
	end

	if fault["fault"] == nil then
		res["error_msg"] = "E_FAULT_NO"
		return _M.output(res)
	end

	if fault["fault"][fault_id] == nil then
		res["error_msg"] = "E_FAULT_NO"
		return _M.output(res)
	end

	if fault["fault"][fault_id]["enable"] and fault["fault"][fault_id]["enable"] == 1 then
		res["error_msg"] = "E_FAULT_YES"
		return _M.output(res)
	end

	res["error_msg"] = "E_FAULT_NO"
	return _M.output(res)
end


function _M.main()
	local start_time =  os.clock()
	ngx.var.ufc_time = math.floor(ngx.now())
	ngx.ctx.ufc_ctxs = {}
	local ctxs = ngx.ctx.ufc_ctxs 

	local args, cmd
	args = ngx.req.get_uri_args()
	local headers = ngx.req.get_headers()
	local res = utils.gen_ufc_log(headers, ctxs, args)

	if not ngx_worker_cache.is_inited() then
		res["error_msg"] = "E_RETRY"
		_M.output(res)
		return
	end

	cmd = args["cmd"]
	ctxs["method"] = cmd
	--ngx.log(ngx.DEBUG, string.format("bypass cmd is %s", cmd))
	if not _M.cmds[cmd] then
		res["error_msg"] = "E_NO_SUPPORT"
		_M.output(res)
	else
		_M.cmds[cmd](args, res, ctxs) -- args是get_uri_args()
	end

	local end_time = os.clock()
	ctxs["ufc_cost_time"] = math.floor((end_time - start_time) * 1000 * 1000) * 0.001
end

_M["cmds"] = {
	["inject"] = _M.inject,
	["stop"] = _M.stop,
	["query"] = _M.query,
	["hit"] = _M.hit,
}

_M.main()