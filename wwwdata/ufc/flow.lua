local math = require "math"
local balancer = require "ngx.balancer"

local dynamic_conf = require "dynamic_conf"
local conf = require "conf"
local utils = require "utils"
local ngx_worker_cache = require "ngx_worker_cache"

local _M = {
	["doc"] = "main flow process for ufc",
}

function _M.redict_to_backup()
	local backup_ufc = conf["backup_ufc"]
	local host, port = utils.split_backend(backup_ufc)
	local backend = host .. ":" .. port

	--ngx.log(ngx.DEBUG, string.format("redict host %s port %d", host, port))

	-- when using backup mode, dont do retry
	balancer.set_current_peer(host, port)
end

--请求是从远端ufc过来的，应该将请求发送给本地服务
function _M.redict_to_local(ctxs)
	local headers = ctxs["headers"]
	local port = _M.get_local_port(headers)
	local ufc_service_name = ctxs["ufc_service_name"] or "-" 

	--双端模式下游，from idc从head获取, to_idc是本机所在物理机房
	local from_idc = headers["x-ufc-from-idc"]
	local to_idc = ngx_worker_cache["local_idc"]
	
	--设置一下ctx，在log的时候会用到
	ctxs["from_idc"] = from_idc
	ctxs["to_physics_idc"] = to_idc

	if not port then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "double ends model no backup"))
		return conf.fail("E_NO_BACKEND")
	end

	local re, err = balancer.set_current_peer("127.0.0.1", port)
	if re ~= true and type(err) == "string" then 
		local logid = ngx.var.ufc_logid or "-"
		local backend = "127.0.0.1" .. port
		ngx.log(ngx.WARN, string.format("logid %s backend %s  double ends model set_current_peer_error %s", logid, backend, err))
	end

	--本机别重试了
	if not ctxs["tries"] then
		ctxs["tries"] = 1
		balancer.set_more_tries(1)
	else
		local logid = ngx.var.ufc_logid or "-"
		local backend = "127.0.0.1" .. port
		ngx.log(ngx.WARN, string.format("logid %s server %s maximum retries reached", logid, ufc_service_name))
		return conf.fail("E_NO_BACKEND")
	end
	
	local connect_timeout = headers["x-ufc-connect-timeout"] or 500
	local send_timeout = headers["x-ufc-rw-timeout"] or 1000
	local read_timeout = send_timeout
	re, err = balancer.set_timeouts(connect_timeout/1000.0, send_timeout/1000.0, read_timeout/1000.0)
	if re ~= true and type(err) == "string" then 
		local logid = ngx.var.ufc_logid or "-"
		local backend = "127.0.0.1" .. port
		ngx.log(ngx.WARN, string.format("logid %s backend %s double ends model balancer_set_timeouts_err %s", logid, backend, err))
	end
end

--从http头中获取本机服务的端口
function _M.get_local_port(headers)
	local ip = ngx_worker_cache["local_ip"]
	if not ip then
		return nil
	end
	--将ip中的.换成成-
	local ip_temp = string.gsub(ip,"%.","-")
	local key = "x-ufc-port-" .. ip_temp
	return headers[key]
end

function _M.main()
	local ctxs = ngx.ctx.ufc_ctxs
	local start_time =  os.clock()
	--请求是从远端ufc过来的直接发到本机服务上去
	if ctxs["x-ufc-request-from-remote"] then
		ctxs["double_ends"] = 1
		return _M.redict_to_local(ctxs)
	end

	local ufc_service_name = ctxs["ufc_service_name"]

	----------------------------------------------------------------------------------------------------
	--redirect to backup if no service and backup not nil(check in access)
	if ufc_service_name == nil then
		ngx.ctx.tries = 1
		return _M.redict_to_backup()
	end
	----------------------------------------------------------------------------------------------------

	local headers = ctxs["headers"]

	local service_meta
	local bns, host, port, backend
	local connect_timeout, send_timeout, read_timeout, try_times, is_divided

	if not ctxs["tries"] then
		ctxs["tries"] = 1
		ctxs["bnss"] = {}
		balancer.set_more_tries(conf["defaults"]["backends_max_tries"] + 1)
	else
		ctxs["tries"] = ctxs["tries"] + 1
	end

	local pass
	local no_try = 0
	local is_bypass = 0
	local double_ends_backends = ctxs["double_ends_backends"]
	--double_ends_backends 为nil走原来的逻辑，不为空用上一个阶段选好的后端
	if not double_ends_backends then
		local not_exist_service_in_sandbox = ctxs["not_exist_service_in_sandbox"]
		service_meta, host, port, pass = dynamic_conf.select_backend(ufc_service_name, headers, is_bypass, nil, not_exist_service_in_sandbox)
	else
		local i = ctxs["tries"]
		local info_temp = double_ends_backends[i]
		--重试到最后一次的时候会出现info_temp 为空的情况
		if not info_temp then
			service_meta = nil
			host = nil
			port = nil
			pass = nil
		else
			service_meta = info_temp["service_meta"]
			host = info_temp["host"]
			port = info_temp["port"]
			pass = info_temp["pass"]
		end
		--pass为false说明熔断中不应该重试
		if pass ~= nil and pass == false then
			no_try = 1
		end
	end

	if pass then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "service is lawine"))
		return conf.fail("E_DEGRADE_4XX")
	end

	if not service_meta then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "service_meta is null"))
		return conf.fail("E_NO_SERVICE")
	end

	local max_tries = conf.get_max_retries(service_meta)
	if ctxs["tries"] > max_tries or no_try == 1 then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "no backup"))
		return conf.fail("E_NO_BACKEND")
	end

	bns = service_meta["bns"]
	if not host or not port then
		if host == nil then
			host = "-"
		end

		if port == nil then
			port = "-"
		end

		if not bns then
			bns = "-"
		end

		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s bns %s host %s port %s server %s message %s", logid, bns, host, port, ufc_service_name, "select backend faild"))
		return conf.fail("E_NO_BACKEND")
	end

	table.insert(ctxs["bnss"], bns)

	backend = host .. ":" .. port

	local platforms_idc = ctxs["idc"] or "-"
	dynamic_conf.request_before(ufc_service_name, bns, platforms_idc, backend, is_bypass)   -- 做一些计数处理

	local end_time = os.clock()
	ctxs["ufc_cost_time"] = math.floor((end_time - start_time) * 1000 * 1000) * 0.001

	ctxs["ufc_upstream_host"] = host
	ctxs["ufc_upstream_port"] = port

	if double_ends_backends ~= nil then
		--双端模式端口换成8240
		port = conf["ufc_port"]
	end

	if ctxs["to_dump_proxy"] then
		host = ctxs["dump_proxy_ip"]
		port = ctxs["dump_proxy_port"]
	end

	local re, err = balancer.set_current_peer(host, port)
	if re ~= true and type(err) == "string" then 
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s backend %s  set_current_peer_error %s", logid, backend, err))
	end

	if not ctxs["request_timestamp"] then
		ctxs["request_timestamp"] = {}
	end

	local tries = ctxs["tries"]
	
	if not ctxs["request_timestamp"][tries] then 
		ctxs["request_timestamp"][tries] = {}
	end 

	ctxs["request_timestamp"][tries]["start"] = ngx.now() * 1000 
	connect_timeout, send_timeout, read_timeout, try_times, is_divided = dynamic_conf.get_service_timeout(ufc_service_name, bns, platforms_idc)
	if ctxs["tries"] ~= 1 then 
		local last_tries = ctxs["tries"] - 1
		ctxs["request_timestamp"][last_tries]["end"] = ngx.now() * 1000
		ctxs["request_timestamp"][last_tries]["cost_time"] = ctxs["request_timestamp"][last_tries]["end"] - ctxs["request_timestamp"][last_tries]["start"]
		if not ctxs["remain_timeout"] then 
			if is_divided then 
				-- 如果是通过 header 传递，每次请求时间会被平均，计算总时间需要相乘
				ctxs["remain_timeout"] = ctxs["ufc-rw-timeout"]
			else 
				-- 没有通过 header 覆盖超时时间，此时 read_timeout 就是总超时，没有被均分
				ctxs["remain_timeout"] = read_timeout
			end
		end 

		ctxs["remain_timeout"] = ctxs["remain_timeout"] - ctxs["request_timestamp"][last_tries]["cost_time"]

		local remain_tries = try_times - ctxs["tries"] + 1
		local new_send_timeout = math.floor(ctxs["remain_timeout"] / remain_tries)
		local new_read_timeout = math.floor(ctxs["remain_timeout"] / remain_tries)

		-- 重试时重新分配超时时间
		local is_time_reset = false 
		if send_timeout < new_send_timeout then 
			send_timeout = new_send_timeout
			is_time_reset = true 
		end 
		if read_timeout < new_read_timeout then 
			read_timeout = new_read_timeout
			is_time_reset = true 
		end 
		if is_time_reset then 
			local logid = ngx.var.ufc_logid or "-"
			ngx.log(ngx.WARN, string.format("logid %s backend %s ufc %s is_time_reset %s send_timeout %d read_timeout %d", logid, backend, ufc_service_name,is_time_reset, send_timeout, read_timeout))
		end 
	end 


	re, err = balancer.set_timeouts(connect_timeout/1000.0, send_timeout/1000.0, read_timeout/1000.0)
	if re ~= true and type(err) == "string" then 
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s backend %s balancer_set_timeouts_err %s", logid, backend, err))
	end
end

_M.main()