local sha1  = require 'sha1'
local _M = {
    ["doc"] = ""
}

function _M.get_script_for_get_backend()
    return _M["script_for_get_backend"]
end

function _M.get_script_sha1_for_get_backend()
    return _M["script_sha1_for_get_backend"]
end

function _M.init_script()
    local script_for_get_backend = [[
        local hash_key = KEYS[1]
        local max_global_running_cnt = ARGV[1]
        local res, err = redis.pcall("ZRANGEBYSCORE", hash_key, 0, max_global_running_cnt)
        if err then
            return {-1, err}
        end
        if #res > 0 then
            math.randomseed(tonumber(ARGV[2]))
            local index = math.random(1, #res)
            local field = res[index]
            res, err = redis.pcall("ZINCRBY", hash_key, 1, field)
            if err then
                return {-1, err}
            end
            return {field, -1}
        end
        return {-1, "NO BACKEND"}
    ]]
    _M["script_for_get_backend"] = script_for_get_backend
    _M["script_sha1_for_get_backend"] = sha1(script_for_get_backend)
end

return _M
