local ngx_worker_cache = require "ngx_worker_cache"
local conf = require "conf"
local cjson = require("cjson.safe")
local shared_memory = require("shared_memory")
local ngx = ngx
local string = string

local _M = {
	["doc"] = "gossip",
	--用作权限校检的token
	["token"] = "aaabbbccc1234", 
	["last_index"] = nil,
	["lock_key"] = "update"
}

--从共享内存中批量获取相应的数据
function _M.batch_get_config_from_cache(config_type)
	ngx.req.read_body()
	local datastr = ngx.req.get_body_data()
	local data, err = cjson.decode(datastr)
	if err then
		return nil
	end
	--提前申请数组大小，避免动态扩张
	local res = table.new(#data, 0)
	for index, v in ipairs(data) do
		local one_res = shared_memory.get_static_data(config_type, v)
		if not one_res then
			return nil
		end
		res[index] = one_res
	end
	return res
end

--check是否允许gossip协议扩散
function _M.check_gossip_enable()
	if not ngx_worker_cache["static_cache"]["configs"] or not ngx_worker_cache["static_cache"]["configs"]["gossip"] then
		return false
	end

	local gossip_config = ngx_worker_cache["static_cache"]["configs"]["gossip"]
	if not gossip_config["enable"] then
		return false
	end

	return true
end

--共享内存里面设一个值告诉特权进程从peer拉配置
function _M.set_gossip_flag(peer_mtime, ip)
	local shared_data = ngx.shared["static_cache"]
	local ok, err = shared_data:set("gossip_peer", ip)
	if not ok then
		ngx.log(ngx.WARN, string.format("set gossip_peer failed, err is %s", err))
		return
	end

	ok, err = shared_data:set("gossip_mtime", peer_mtime)
	if not ok then
		ngx.log(ngx.WARN, string.format("set gossip_mtime failed, err is %s", err))
		return
	end
	return
end

function _M.get_gossip_flag()
	local shared_data = ngx.shared["static_cache"]
	local ip = shared_data:get("gossip_peer")
	local mtime = shared_data:get("gossip_mtime")
	return ip, mtime
end

function _M.delete_gossip_flag()
	local shared_data = ngx.shared["static_cache"]
	shared_data:delete("gossip_peer")
	shared_data:delete("gossip_mtime")
end

--是否正在进行进行配置更新
function _M.is_in_config_update()
	local shared_data = ngx.shared.fault
	local res = shared_data:get(_M["lock_key"])
	if res then
		return true
	end
	return false
end

function _M.config_update_lock()
	local shared_data = ngx.shared.fault
	local key = _M["lock_key"]
	--利用add函数，加锁，需要设置一个超时时间，设置为1min
	--这里即使出现竞争也不会有bug，但是万一tracebacek 锁的时间太长会有问题
	local ok, err = shared_data:add(key, 1, 60)
	if not ok then
		if err == "exists" then
			--该锁已经被占用
			ngx.log(ngx.WARN, string.format("woker pid is: %d, gossip try to lock failed, error is: %s", ngx.worker.pid(), err))
			return false
		else
			--其它错误导致占锁失败，认为占锁成功
			ngx.log(ngx.WARN, string.format("woker pid is: %d, gossip try to lock success, error is: %s", ngx.worker.pid(), err))
			return true
		end
	else
		--占锁成功
		return true
	end
end

function _M.config_update_unlock()
	local shared_data = ngx.shared.fault
	local key = _M["lock_key"]
	shared_data:delete(key)
end

--获取peer的全局mtime
function _M.get_global_mtime_from_peer(ip, httpc)
	local res, err = _M.get_peer_config(ip, httpc, "mtime", "mtime")
	if not err then
		res = tonumber(res)
	end
	return res, err
end

--获取配置cnt
function _M.get_config_cnt_from_peer(ip, httpc, config_type)
	local res, err = _M.get_peer_config(ip, httpc, config_type, "cnt")
	if not err then
		res = tonumber(res)
	end
	return res, err
end

--获取config name
function _M.get_config_name_from_peer(ip, httpc, config_type, i)
	return _M.get_peer_config(ip, httpc, config_type, tostring(i))
end

--获取config mtime
function _M.get_config_mtime_from_peer(ip, httpc, config_type, config_name)
	local res, err = _M.get_peer_config(ip, httpc, config_type, config_name .. "-mtime")
	if not err then
		res = tonumber(res)
	end
	return res, err
end

--获取config content
function _M.get_config_content_from_peer(ip, httpc, config_type, config_name)
	return _M.get_peer_config(ip, httpc, config_type, config_name)
end

--请求peer的batch 接口
function _M.batch_get_peer_config(ip, httpc, config_type, keys)
	local params = {}
	local ok, err, res, body, response
	local uri = string.format("/gossip?cmd=get&config_type=%s&key=batch&token=%s",
	config_type, _M["token"])
	local headers = {}
	headers["http-x-isis-logid"] = ngx.time()
	headers["x-ufc-service-name"] = "pcs-ufc-agent"
	headers["x-ufc-self-service-name"] = "pcs-ufc-agent"
	--用post方法请求接口
	params["method"] = "POST"
	params["path"] = uri
	params["headers"] = headers
	params["body"] = keys
	res, err = httpc:request(params)
	if err and err ~= "closed" then
		return nil, err
	elseif err and err == "closed" then
		--如果是closed错误，说明是长连接最大请求次数到了重新连一下就行
		ok, err = httpc:connect({
			scheme = "http",
			host = ip,
			port = conf["ufc_port"],
		})
		if not ok then
			httpc:close()
			ngx.log(ngx.NOTICE, string.format("connect to peer %s failed, err is %s", ip, err))
			return nil, err
		end
		res, err = httpc:request(params)
		if err then
			return nil, err
		end
	end

	body, err = res:read_body()
	if err then
		return nil, err
	end
	response, err = cjson.decode(body)
	if err then
		ngx.log(ngx.WARN, string.format("peer response %s is not json", body))
		return nil, "body decode error"
	end

	if not response["token"] or response["token"] ~= _M["token"] then
		ngx.log(ngx.WARN, string.format("peer %s response token %s is invalid, broadcast failed", ip, response["token"]))
		return nil, "invalid token"
	end

	if not response["res"] or response["res"] == "invalid_args" or response["res"] =="NULL" then
		ngx.log(ngx.WARN, string.format("peer response %s is error", response["res"]))
		return nil, "response error"
	end
	return response["res"], nil
end

--通过初始化好的http连接请求peer gossip接口
function _M.get_peer_config(ip, httpc, config_type, key)
	local params = {}
	local ok, err, res, body, response
	key = ngx.escape_uri(key)
	local uri = string.format("/gossip?cmd=get&config_type=%s&key=%s&token=%s",
	config_type, key, _M["token"])
	local headers = {}
	headers["http-x-isis-logid"] = ngx.time()
	headers["x-ufc-service-name"] = "pcs-ufc-agent"
	headers["x-ufc-self-service-name"] = "pcs-ufc-agent"
	--请求本机ufc正常
	params["method"] = "GET"
	params["path"] = uri
	params["headers"] = headers
	res, err = httpc:request(params)
	if err and err ~= "closed" then
		return nil, err
	elseif err and err == "closed" then
		--如果是closed错误，说明是长连接最大请求次数到了重新连一下就行
		ok, err = httpc:connect({
			scheme = "http",
			host = ip,
			port = conf["ufc_port"],
		})
		if not ok then
			httpc:close()
			ngx.log(ngx.NOTICE, string.format("connect to peer %s failed, err is %s", ip, err))
			return nil, err
		end
		res, err = httpc:request(params)
		if err then
			return nil, err
		end
	end

	body, err = res:read_body()
	if err then
		return nil, err
	end
	response, err = cjson.decode(body)
	if err then
		ngx.log(ngx.WARN, string.format("peer response %s is not json", body))
		return nil, "body decode error"
	end

	if not response["token"] or response["token"] ~= _M["token"] then
		ngx.log(ngx.WARN, string.format("peer %s response token %s is invalid, broadcast failed", ip, response["token"]))
		return nil, "invalid token"
	end

	if not response["res"] or response["res"] == "invalid_args" or response["res"] =="NULL" then
		ngx.log(ngx.WARN, string.format("peer response %s is error", response["res"]))
		return nil, "response error"
	end
	return response["res"], nil
end

--获取同组n台机器
function _M.get_peers(rate)
	local prefix = conf.get_redis_prefix()
	local group_index = _M.get_peer_group_index()
	if not group_index then
		ngx.log(ngx.WARN, string.format("peer info upload, get group index failed"))
		return nil
	end
	local idc = ngx_worker_cache["local_idc"]

	local red, err = conf.get_redis()
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("peers get failed, get redis fail, err is %s", err))
		red:close()
		return nil
	end
	local key = string.format("%s-%s-%d",prefix, idc, group_index)
	local peers, err = red:smembers(key)
	if peers == ngx.null or err ~= nil then
		ngx.log(ngx.WARN, string.format("peers get failed, err is %s", err))
		red:close()
		return nil
	end

	if #peers == 0 then
		ngx.log(ngx.WARN, string.format("peers set is empty"))
		red:close()
		return nil
	end

	local len = #peers
	local n = math.ceil(len * rate)

	--随机获取n台机器
	--这个算法可能拿不到n台，
	local res = {}
	for i = 1, n do
		local index = math.random(len)
		if peers[index] ~= ngx_worker_cache["local_ip"] then
			--用map去重
			res[peers[index]] = 1
		end
	end
	return res
end

function _M.delete_peer(peer_ip, index)
	local red, err = conf.get_redis()
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("delete peer %s failed, get redis fail, err is %s", peer_ip, err))
		red:close()
		return
	end

	local idc = ngx_worker_cache["local_idc"]
	local prefix = conf.get_redis_prefix()
	local key = string.format("%s-%s-%d",prefix, idc, index)
	local res, err = red:srem(key, peer_ip)
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("delete peer %s failed, srem redis fail, err is %s", peer_ip, err))
		red:close()
		return
	end
	ngx.log(ngx.NOTICE, string.format("delete peer %s success", peer_ip))
	return
end

--获取机器分组信息
function _M.get_peer_group_index()
	local local_ip = ngx_worker_cache["local_ip"]
	if not local_ip then
		ngx.log(ngx.WARN, "local ip is nil, stop upload peer info")
		return
	end
	local num = _M.ipToInt(local_ip)
	
	if not ngx_worker_cache["static_cache"]["configs"] or not ngx_worker_cache["static_cache"]["configs"]["gossip"] then
		return
	end

	local gossip_config = ngx_worker_cache["static_cache"]["configs"]["gossip"]
	if not gossip_config["enable"] or not gossip_config["idc_group_num"]then
		return
	end
	--每个大机房有个默认的分组
	local group_num = gossip_config["idc_group_num"]["default"]
	local idc = ngx_worker_cache["local_idc"]
	if idc and gossip_config["idc_group_num"][idc] then
		group_num = gossip_config["idc_group_num"][idc]
	end

	if not group_num then
		return
	end
	local group_index = num % group_num

	--本次分组信息和上次不一样说明组数变了
	--把老的分组数据删掉
	if _M["last_index"] and _M["last_index"] ~= group_index then
		_M.delete_peer(local_ip, _M["last_index"])
	end
	_M["last_index"] = group_index
	--共享内存里面写一个，给配置延迟上报逻辑用
	shared_memory.static_data_set("static_cache", "group_index", group_index)
	return group_index
end

--peer信息上报
function _M.peer_upload()
	local ip = ngx_worker_cache["local_ip"]
	local group_index = _M.get_peer_group_index()
	if not group_index then
		ngx.log(ngx.WARN, string.format("peer info upload, get group index failed"))
		return conf["res"]["FAIL"]
	end
	local red, err = conf.get_redis()
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("peer info upload, get redis fail, err is %s", err))
		red:close()
		return conf["res"]["FAIL"]
	end

	local idc = ngx_worker_cache["local_idc"]
	local prefix = conf.get_redis_prefix()
	local key = string.format("%s-%s-%d",prefix, idc, group_index)
	local res, err = red:sadd(key, ip)
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("peer info upload, set redis fail, err is %s", err))
		red:close()
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

function _M.ipToInt( str )
	local num = 0
	if str and type(str) == "string" then
		local o1,o2,o3,o4 = str:match("(%d+)%.(%d+)%.(%d+)%.(%d+)")
		num = 2^24*o1 + 2^16*o2 + 2^8*o3 + o4
	end
	return num
end


--判断本机是否在小流量列表里面
function _M.is_in_small_flow(config_type, config_name, red)
    local local_ip = ngx_worker_cache["local_ip"]
    --如果本机ip没有获取到就默认更新
    if local_ip == nil then
		ngx.log(ngx.WARN, string.format("get local ip failed"))
        return true
    end

	local prefix = conf.get_redis_prefix()
	local small_flow_key = string.format("%s-%s-%s-ips", prefix, config_type, config_name)
	local is_exist, err = red:exists(small_flow_key)
    if err or is_exist == ngx.null or is_exist == 0 then
        --小流量key不存在，默认更新
		ngx.log(ngx.WARN, string.format("small flow key %s miss, err is %s, update the infomation", small_flow_key, err))
		return true
    end

    is_exist, err = red:hget(small_flow_key, local_ip)
    if err then
        --如果读取redis出错还是默认更新
        ngx.log(ngx.WARN, string.format("get small flow ips info failed, update the infomation, key is %s , err is %s", small_flow_key, err))
        return true
    end
    
    if is_exist == ngx.null then
        ngx.log(ngx.NOTICE, string.format("config_type:%s, config_name:%s, this machine is not in small flow iplist and stop update",
		config_type, config_name))
        return false
    end
    --走到这里说明本机在小流量机器集合中，可以更新
	ngx.log(ngx.NOTICE, string.format("config_type:%s, config_name:%s, this machine is in small flow iplist and start update",
	config_type, config_name))
	return true
end

return _M