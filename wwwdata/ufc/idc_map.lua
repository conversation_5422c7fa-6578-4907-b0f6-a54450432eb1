local conf = require "conf"
local cjson = require "cjson.safe"
local utils = require "utils"
local ngx_worker_cache = require "ngx_worker_cache"

local _M = {
    ["doc"] = "nginx worker config, idc map"
}

--[[
    @comment 获取 平台+ idcmap 数量
    @param string bns
    @param string backend
    @return table
]]
function _M.get_backends_cnt(bns, platforms_idc)
    local cnt = 0
    local bnss = ngx_worker_cache["static_cache"]["bnss"][bns]
    if not bnss then
        return cnt
    end

    local platforms_idcmaps = ngx.ctx.platforms_idcmaps
    if platforms_idc and platforms_idc ~= "all_idc" and platforms_idcmaps then
        for _, key in pairs(platforms_idcmaps) do
            if bnss[key] and #bnss[key] > 0 then
                cnt = cnt + #bnss[key]
            end
        end

        if cnt > 0 then
            return cnt
        end
    end

    local backends = bnss["bns_nodes"]
    return #backends
end

--[[
    @comment 获取backend所在 platformss信息
    @param table config
    @param string ufc_idc
    @return string  
]]

function _M.select_platformss(config, from_platform, from_service_name)
    if not from_platform or from_platform == "-" then
        return nil
    end 

    local platform_map_config = config["platform_map"]
    if not platform_map_config or platform_map_config["enable"] ~= 1 then
        return nil 
    end

    if platform_map_config["black_enable"] and platform_map_config["black_enable"] == 1 then
        if not from_service_name or from_service_name == "-" then
            return nil 
        end

        if utils.in_array(from_service_name, platform_map_config["black_list"]) then
            --开启黑名单，但是未在黑名单中，则默认走 platform map
            return nil
        end
    end

    --[[
        "platformmap": {
            "enable":1,
            "black_enable": 1, 
            "black_list": [
                "${to_service_name}"
            ]
            "config":{
                ["innerwhite"],
                ["smallflow"],
                ["default", "extend", "dataflow"],
                ["all"]
            }
        }
    ]]

    local config = platform_map_config["config"]
    if config and type(config) ~= "table" then
        return nil 
    end 

    for i, v in ipairs(config) do 
        if utils.in_array(from_platform, v) then
            local result = {}
            for j = i, #config do
                table.insert(result, config[j])
            end 
            -- 返回一个二维数组
            return result
        end
    end
    return nil 
end


--[[
    @comment 传入匹配的 platforms, 通过platforms 获取backend 所在的 logic-idc 拼 platforms，保存于 ctx。
             并返回一个字符串 -${platform1}-${platform2}-${platform3}-${logic_idc}, 供后续做封禁熔断 tag
    @param table to_platforms
    @param table config
    @param string ufc_idc
    @return string

]]
function _M.select_platforms_idc(config, from_platform, from_idc, from_service_name)
    local bns = config["bns"]
    if not bns then
        return nil
    end

    
    local bnss = ngx_worker_cache["static_cache"]["bnss"][bns]
    if not bnss then
        return nil
    end

    -- to_platformss 是一个二维数组
    local to_platformss = _M.select_platformss(config, from_platform, from_service_name)
    local logic_idc, to_idcs = _M.select_idcs(config, from_idc, from_service_name)



    if not to_platformss or #to_platformss == 0 then
        to_platformss = {{"dummy_platform"}}
    end 

    if not to_idcs or #to_idcs == 0 then
        to_idcs =  {"dummy_idc"}
    end 

    local platform_matched = false 
    local idc_matched = false

    local target_platforms_idcs = {}
    
    local target_to_platforms = {}

    local platform_idc_keys = {}
    local platform_keys = {}
    -- to_platformss 为二维数组
    for _, to_platforms in ipairs(to_platformss) do 
        -- 每次挑选一个一维数组，每个数组会匹配 platform-idc key列表和 platform 列表
        -- 匹配到就 break 
        platform_idc_keys, platform_keys = _M.get_platform_idc_keys(bns, to_platforms, to_idcs)
        if #platform_idc_keys > 0 or #platform_keys > 0 then 
            -- 用于打日志用
            target_to_platforms = to_platforms
            break
        end 
    end

    if #platform_idc_keys > 0 then
        target_platforms_idcs = platform_idc_keys
        platform_matched = true 
        idc_matched = true
    elseif #platform_keys > 0 then
        target_platforms_idcs = platform_keys
        platform_matched = true 
    end 

    -- 表示 platform_idc 和 platform 都未匹配到，单独获取idc的匹配结果
    if not target_platforms_idcs or #target_platforms_idcs == 0 then 
        local idc_keys = _M.get_idc_keys(bns, to_idcs)
        if #idc_keys > 0 then
            target_platforms_idcs = idc_keys
            idc_matched = true 
        end 
    end 

    ngx.ctx.ufc_ctxs["platformmap_enabled"] = platform_matched
    ngx.ctx.ufc_ctxs["idcmap_enabled"] = idc_matched

    -- 反馈的 key 是用于封禁计数建立map和清除封禁计数
    -- 如果业务改变该配置会导致封禁计数重新计算，即表现为改配置导致封禁熔断失效
    -- 返回 nil 则用 all_idc
    if #target_platforms_idcs > 0 then
        ngx.ctx.platforms_idcmaps = target_platforms_idcs 
        if platform_matched and idc_matched then 
            return table.concat(target_to_platforms, "-") .. "-" .. logic_idc
        elseif platform_matched then
            return table.concat(target_to_platforms, "-")
        elseif idc_matched then 
            return logic_idc
        end
    end    
    return nil 
end 



function _M.select_idcs(config, from_idc, from_service_name)
    --  logic_idc, to_idc, idcmap_enabled
    local idc_map_config = config["idc_map"]
    if not idc_map_config or idc_map_config["enable"] ~= 1 then
        return "", nil
    end

    if idc_map_config["white_enable"] and idc_map_config["white_enable"] == 1 then
        if not from_service_name or from_service_name == "-" then
            return "", nil
        end

        if not utils.in_array(from_service_name, idc_map_config["white_list"]) then
            --开启白名单，但是未在白名单中，则默认不走idc map
            return "", nil
        end
    end

    local logic_idc = conf["idc_map"][from_idc]

    if idc_map_config["smart_bns"] and idc_map_config["smart_bns"] == 1 then
        return logic_idc, {from_idc}
    end

    local logic_idcs_config = idc_map_config[logic_idc]
    if not logic_idcs_config then 
        return "", nil 
    end 
    local prefer = logic_idcs_config["prefer"]              
    if prefer and prefer ~= "all" then
        if type(prefer) == "table" then 
            return logic_idc, prefer 
        end         
        if type(prefer) == "string" then 
            return logic_idc, {prefer}
        end 
    end
    
    return "", nil
end

-- 匹配命中的 platform-idc key列表或 platform key列表 
function _M.get_platform_idc_keys(bns, to_platforms, to_idcs)
    local platform_idc_keys = {}
    local platform_keys = {}
    for _, to_platform in ipairs(to_platforms) do
        for _, idc in ipairs(to_idcs) do
            local platform_idc_key, platform_key = _M.get_platform_idc_key(bns, to_platform, idc)
            if platform_idc_key ~= "" then
                table.insert(platform_idc_keys, platform_idc_key)         
            end
            if platform_key ~= "" then
                table.insert(platform_keys, platform_key)
            end 
        end 
    end 

    return platform_idc_keys, platform_keys
end 

-- 只选择 platform-idc 或 platform key
function _M.get_platform_idc_key(bns, platform, idc)
    local bnss = ngx_worker_cache["static_cache"]["bnss"][bns]
    if not bnss then
        return "", ""
    end

    if (platform and platform ~= "dummy_platform") and (idc and idc ~= "dummy_idc") then 
        local key = platform .. "-" .. idc
        if key and bnss[key] and #bnss[key] > 0 then
            return key, ""
        end
    end 

    if platform and platform ~= "dummy_platform" then 
        if bnss[platform] and #bnss[platform] > 0 then
            return "", platform
        end
    end

    return "", ""
end 

-- 匹配 idc 列表
function _M.get_idc_keys(bns, to_idcs) 
    local bnss = ngx_worker_cache["static_cache"]["bnss"][bns]
    local idc_keys = {}
    for _, idc in ipairs(to_idcs) do
        if idc and idc ~= "dummy_idc" then 
            if bnss[idc] and #bnss[idc] > 0 then
                table.insert(idc_keys, idc)
            end
        end        
    end

    return idc_keys 
end 

return _M
