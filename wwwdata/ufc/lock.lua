local conf = require "conf"
local moniter = require "moniter"


local _M = {
	["doc"] = "stream utils",
}

--对stream共享内存加锁
--加锁的原则是读不用加，涉及到写得加
--只有http模块需要加锁，stream模块是不用加的，只需要保证ip分配的时候不会有冲突问题就行
function _M.stream_lock()
	local shared_data 
	--其实只有http模块会调用这个函数
	if conf["stream"] == 0 then
		shared_data = ngx.shared.http_ip
	else
		shared_data = ngx.shared.stream_ip
	end
	--利用add函数，加锁，需要设置一个超时时间，设置为10s
	local ok, err = shared_data:add("lock", 1, 10)
	if not ok then
		if err == "exists" then
			--该锁已经被占用
			ngx.log(ngx.WARN, string.format("woker pid is: %d, try to lock stream data failed, error is: %s", ngx.worker.pid(), err))
			return false
		else
			--其它错误导致占锁失败，认为占锁成功，要不然会导致一直动态分配index失败
			ngx.log(ngx.WARN, string.format("woker pid is: %d, try to lock stream data failed, error is: %s", ngx.worker.pid(), err))
			return true
		end
	else
		--占锁成功
		return true
	end
		
end

--对stream共享内存解锁
function _M.stream_unlock()
	local shared_data 
	--其实只有http模块会调用这个函数
	if conf["stream"] == 0 then
		shared_data = ngx.shared.http_ip
	else
		shared_data = ngx.shared.stream_ip
	end
	shared_data:delete("lock")
end

--对stream共享内存加锁
--加锁的原则是读不用加，涉及到写得加
--只有http模块需要加锁，stream模块是不用加的，只需要保证ip分配的时候不会有冲突问题就行
function _M.stream_lock()
	local shared_data 
	--其实只有http模块会调用这个函数
	if conf["stream"] == 0 then
		shared_data = ngx.shared.http_ip
	else
		shared_data = ngx.shared.stream_ip
	end
	--利用add函数，加锁，需要设置一个超时时间，设置为10s
	local ok, err = shared_data:add("lock", 1, 10)
	if not ok then
		if err == "exists" then
			--该锁已经被占用
			ngx.log(ngx.WARN, string.format("woker pid is: %d, try to lock stream data failed, error is: %s", ngx.worker.pid(), err))
			return false
		else
			--其它错误导致占锁失败，认为占锁成功，要不然会导致一直动态分配index失败
			ngx.log(ngx.WARN, string.format("woker pid is: %d, try to lock stream data failed, error is: %s", ngx.worker.pid(), err))
			return true
		end
	else
		--占锁成功
		return true
	end
		
end

--对stream共享内存解锁
function _M.stream_unlock()
	local shared_data 
	--其实只有http模块会调用这个函数
	if conf["stream"] == 0 then
		shared_data = ngx.shared.http_ip
	else
		shared_data = ngx.shared.stream_ip
	end
	shared_data:delete("lock")
end

--对service加锁
function _M.lock_backend(service_name, backend)
	local shared_data = ngx.shared.fault
	local key = service_name .. "|" .. backend
	--利用add函数，加锁，需要设置一个超时时间，设置为1s
	--都是lua计算逻辑，这个锁住的时间不可能太长，1s够了
	local ok, err = shared_data:add(key, 1, 1)
	if not ok then
		if err == "exists" then
			--该锁已经被占用
			ngx.log(ngx.WARN, string.format("woker pid is: %d, service %s, backend %s try to lock failed, error is: %s", ngx.worker.pid(), service_name, backend, err))
			return false
		else
			--其它错误导致占锁失败，认为占锁成功
			ngx.log(ngx.WARN, string.format("woker pid is: %d, service %s, backend %s try to lock sucess, error is: %s", ngx.worker.pid(), service_name, backend, err))
			return true
		end
	else
		--占锁成功
		return true
	end
		
end

--对service解锁
function _M.unlock_backend(service_name, backend)
	local shared_data = ngx.shared.fault
	local key = service_name .. "|" .. backend
	shared_data:delete(key)
end

--对配置加锁
function _M.lock_config(config_name)
	local shared_data = ngx.shared.fault
	local key = "config_write|" .. config_name
	--利用add函数，加锁，需要设置一个超时时间，设置为1s
	--都是lua计算逻辑，这个锁住的时间不可能太长，1s够了
	local ok, err = shared_data:add(key, 1, 2)
	if not ok then
		if err == "exists" then
			--该锁已经被占用
			ngx.log(ngx.WARN, string.format("woker pid is: %d, config %s, try to lock failed, error is: %s", ngx.worker.pid(), config_name, err))
			return false
		else
			--其它错误导致占锁失败，认为占锁成功
			ngx.log(ngx.WARN, string.format("woker pid is: %d, config %s, try to lock sucess, error is: %s", ngx.worker.pid(), config_name, err))
			return true
		end
	else
		--占锁成功
		return true
	end
end

--对配置解锁
function _M.unlock_config(config_name)
	local shared_data = ngx.shared.fault
	local key = "config_write|" .. config_name
	shared_data:delete(key)
end

--定时器加锁
function _M.timer_lock(name, timeNow, interval)
	if not moniter["timer_lock"][name] then
		moniter["timer_lock"][name]  = {}
	end
	local locks = moniter["timer_lock"][name]
	--找到锁里面最大的时间戳
	local max_time = -1
	local num = 0
	for k, v in pairs(locks) do
		num = num + 1
		if k > max_time then
			max_time = k
		end
	end
	--如果距离上次加锁时间小于两个间隔时间，说明还有定时器在跑，加锁失败，
	if max_time > 0 and timeNow - max_time < 2 * interval then
		ngx.log(ngx.WARN, string.format("lock failed, timer name:%s, last lock time:%f, lock num:%d", name, max_time, num))
		return false
	end

	if max_time > 0 then
		--距离上次加锁时间大于两个时间间隔加的锁
		ngx.log(ngx.WARN, string.format("lock success, timer name:%s, last lock time:%f, lock num:%d", name, max_time, num))
	end

	--加锁
	locks[timeNow] = 1
	return true
end

--定时器解锁
function _M.timer_unlock(name, timeNow)
	if not moniter["timer_lock"][name] then
		moniter["timer_lock"][name]  = {}
	end
	local locks = moniter["timer_lock"][name]
	locks[timeNow] = nil
end

return _M

