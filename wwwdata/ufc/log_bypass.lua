local utils = require "utils"
local trace = require "trace"

--local cjson = require "cjson.safe"
local ngx_worker_cache = require "ngx_worker_cache"

local conf = require "conf"
--local dynamic_conf = require "dynamic_conf"
local tonumber = tonumber
local ngx_wroker_process_cron = require "ngx_worker_process_cron"
--local table = table

--[[
local ufc_service_name = ngx.ctx.ufc_service_name or "-"
local ori_ufc_service_name = ngx.ctx.ori_ufc_service_name or "-"
local from_service_name = ngx.ctx.from_service_name or "-"
--]]
local ctxs = ngx.ctx.ufc_ctxs or {}
local ufc_service_name = ctxs["ufc_service_name"] or "-"
local ori_ufc_service_name = ctxs["ori_ufc_service_name"] or "-"
local from_service_name = ctxs["from_service_name"] or "-"

local callid = ngx.var.ufc_callid
local shared_memory = require "shared_memory"

--[[
local bns = ngx.ctx.bns or "-"
local backend = ngx.ctx.backend or "-"
local status = ngx.ctx.status or "200"
--]]
local bns = ctxs["bns"] or "-"
local backend = ctxs["backend"] or "-"
local status = ctxs["status"] or "200"

local last_update_time = shared_memory.get("static_cache", "last_update_time")
local update_lantency = nil
--gossip协议相关请求不触发update_lantency日志
if last_update_time and not ctxs["gossip"] then 
	local time_now = ngx.time()
	update_lantency = time_now - last_update_time
	ngx_wroker_process_cron.update_lantency_upload(update_lantency, time_now)
	--默认5分钟配置没有更新就报警
	if update_lantency >= utils["max_update_interval"] then
		utils.update_error_log(update_lantency)
	end
end

--local method = ngx.ctx.method or "-"
local method = ctxs["method"] or "-"
local from_logic_idc = "-"
--local from_idc = ngx.ctx.from_idc
local from_idc = ctxs["from_idc"] or "-"
if from_idc and conf["idc_map"][from_idc] then
	from_logic_idc = conf["idc_map"][from_idc]
end

local from_platform = ctxs["from_platform"] or "-"
if from_platform and from_platform ~= "-" then 
	if from_logic_idc == "-" then
		from_logic_idc = from_platform
	else 
		from_logic_idc = from_platform .. from_logic_idc
	end
end 


--local to_logic_idc = ngx.ctx.idc or "-"
local to_physics_idc = ctxs["to_physics_idc"] or "-"

local ufc_cost_time = ctxs["ufc_cost_time"] or 0
ngx.var.ufc_cost_time = ufc_cost_time
ngx.var.ufc_service_name = ufc_service_name
ngx.var.update_lantency = update_lantency or "-"
ngx.var.ori_from_service_name = from_service_name
-- 如果有传递app和平台信息，from_service 打印app-platform
if type(ctxs["from_app"]) == "string" and ctxs["from_app"] ~= "" and type(ctxs["from_platform"]) == "string" and ctxs["from_platform"] ~= "" then
	ngx.var.from_service_name = ctxs["from_app"] .. "-" .. ctxs["from_platform"]
else
	ngx.var.from_service_name = from_service_name
end
ngx.var.ori_ufc_service_name = ori_ufc_service_name
ngx.var.method = method
if method ~= "-" then 
	ngx.var.ufc_uri = ngx.var.ufc_uri  .. "?method=" .. method
end 

ngx.var.from_logic_idc = from_logic_idc

--这里只用到2个table，剩下的是为了兼容
local ufc_log = ngx_worker_cache["ufc_log"]
ufc_log["ufc_log1"] = ""
ufc_log["ufc_log2"] = ""
ufc_log["ufc_log3"] = ""
ufc_log["ufc_log4"] = ""
ufc_log["ufc_log5"] = ""
ufc_log["ufc_log6"] = ""
ufc_log["ufc_log7"] = ""
ufc_log["ufc_log8"] = ""

ufc_log["ufc_log1"] = "[callid=" .. callid  .. " cost_time=" .. ngx.var.request_time .. " to_module=" .. ufc_service_name
ufc_log["ufc_log2"] = " to_bns=" .. bns .. " to_ip=" .. backend .. " to_idc=" .. to_physics_idc .. " upstream_status=" .. status .."]]"
ngx.var.ufc_log1 = ufc_log["ufc_log1"]
ngx.var.ufc_log2 = ufc_log["ufc_log2"]
ngx.var.ufc_log3 = ufc_log["ufc_log3"]
ngx.var.ufc_log4 = ufc_log["ufc_log4"]
ngx.var.ufc_log5 = ufc_log["ufc_log5"]
ngx.var.ufc_log6 = ufc_log["ufc_log6"]
ngx.var.ufc_log7 = ufc_log["ufc_log7"]
ngx.var.ufc_log8 = ufc_log["ufc_log8"]