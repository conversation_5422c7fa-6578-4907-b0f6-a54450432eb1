local ngx_worker_cache = require "ngx_worker_cache"
local shared_memory = require "shared_memory"

local _M = {
	["doc"] = "meta table function",
	["metatable"] = {},
	--过期时间一个小时
	["exptime"] = 3600,
}

function _M.set_dynamic_data(zone, key, value, expire)
	local exptime = _M["exptime"]
	--有些数据不会过期
	if expire == false then
		exptime = 0
	end
	--兼融talbe初始化逻辑
	if type(value) == "table" then
		shared_memory.dynamic_data_set(zone, key, "table", exptime)
		return
	end
	
	shared_memory.dynamic_data_set(zone, key, value, exptime)
	return
end

function _M.get_dynamic_data(zone, key, service_name, backend, forbid)
	local res = shared_memory.get(zone,key)
	

	if res == "table" then
		--table复用
		local meta_table = ngx_worker_cache["meta_table"]
		if not meta_table[service_name] then
			meta_table[service_name] = {}
		end

		if not meta_table[service_name][backend] then
			meta_table[service_name][backend] = {}
		end
		local tmp = meta_table[service_name][backend]
		if tmp[key] then
			return tmp[key]
		end

		--
		--没有缓存的table
		local res_table = {}
		res_table["shared_memmory_key"] = key
		res_table["zone"] = zone
		res_table["service_name"] = service_name
		res_table["backend_name"] = backend
		res_table["forbid_flag"] = forbid
		setmetatable(res_table, _M.metatable)
		tmp[key] = res_table
		return tmp[key]
	end
	--key的值不是个table直接返回res,这个res可能是nil
	return res
end

function _M.newindex(mytable, key, value)
	local shared_memmory_key = mytable["shared_memmory_key"]
	local service_name = mytable["service_name"]
	local backend = mytable["backend_name"]
	
	if backend == "ready" then
		--backend key的名字
		backend = key
	end

	if shared_memmory_key == "start" then
		shared_memmory_key = key
		--dynamic_cache的第一级key一定是 service name
		service_name = key
	else
		shared_memmory_key = shared_memmory_key .. "&" .. key
	end

	local zone = mytable["zone"]
	if key == "service" then
		zone = "dynamic_cache-service"
	end
	if key == "backend" then
		zone = "dynamic_cache-backend"
	end
	if key == "breaker" then
		zone = "dynamic_cache-breaker"
	end

	--写数据的时候共享内存中哪些backend有数据, 做个记录
	--为了节省内存不用详细记录有哪些key
	if backend ~= "start" and backend ~= "ready" then
		if not ngx_worker_cache["backend_keys"][service_name] then
			ngx_worker_cache["backend_keys"][service_name] = {}
		end
		ngx_worker_cache["backend_keys"][service_name][backend] = 1
	end

	--key是如下几种数据不放到共享内存里面
	if key == "current_index" or key == "response_times" then
		ngx_worker_cache["not_shared_dynamic_cache"][service_name][key] = value
		return
	end

	--对于table类型的数据还有封禁相关数据不会过期（写比较少）
	local expire = true
	if type(value) == "table" or mytable["forbid_flag"] == true  or zone == "dynamic_cache-breaker" then
		expire = false
	end

	_M.set_dynamic_data(zone, shared_memmory_key, value, expire)
end

function _M.index(mytable, key)
	local shared_memmory_key = mytable["shared_memmory_key"]
	local service_name = mytable["service_name"]
	local backend = mytable["backend_name"]
	if shared_memmory_key == "start" then
		shared_memmory_key = key
		--dynamic_cache的第一级key一定是 service name
		service_name = key
	else
		shared_memmory_key = shared_memmory_key .. "&" .. key
	end

	if backend == "ready" then
		--backend key的名字
		backend = key
	end

	local zone = mytable["zone"]
	if key == "service" then
		zone = "dynamic_cache-service"
	end
	if key == "backend" then
		zone = "dynamic_cache-backend"
		--下一个key一定是backend名字
		backend = "ready"
	end
	if key == "breaker" then
		zone = "dynamic_cache-breaker"
	end

	--key是如下几种数据不放到共享内存里面
	if key == "current_index" or key == "response_times" then
		return ngx_worker_cache["not_shared_dynamic_cache"][service_name][key]
	end

	local forbid = false
	--单机封禁标志位不会过期
	if key == "forbidden" then
		forbid = true
	end
	return _M.get_dynamic_data(zone, shared_memmory_key, service_name, backend, forbid)
end

function _M.set_meta_table()
	_M.metatable.__index = _M.index
	_M.metatable.__newindex = _M.newindex
	setmetatable(ngx_worker_cache["dynamic_cache"], _M.metatable)
end

return _M