
local cjson = require "cjson.safe"
local conf = require "conf"
local shared_memory = require "shared_memory"

local _M = {
	["doc"] = "moniter module",

	["timeout"] = {
		["backend"] = {},
		["service"] = {},
		["bug_service"] = {},
		["online_isolation"] = {},
		["nonstandard_from_service"] = {},
		["backend_quota"] = 0,
		["service_quota"] = 0,
		["bug_service_quota"] = 0,
		["online_isolation_quota"] = 0,
		["nonstandard_from_service_quota"] = 0
	},

	["quota"] = {
		["backend"] = 0,
		["service"] = 0,
		["bug_service"] = 0,
		["online_isolation"] = 0,
		["nonstandard_from_service"] = 0
	},

	["forbid"] = { -- 上报
		["backend"] = {},
		["service"] = {}
	},
	["bug"] = { -- 上报
		["service"] = {}
	},

	["bp_hash_err_service"] = {}, -- 上报
	
	["invalid_timer"] = {}, -- 上报
	["timer_lock"] = {},

	["worker_check_time"] = {},
	["timer_interval"] ={},

	["request"] = {},
 
	["instance_service"] = {}, -- 上报

	["request_cnt_timeout"] = 60,

	["warn_cache"] = {},
}

--[[
	ufc 定时器检查时间初始化
]]--
function _M.timer_check_init()
	local time_now = ngx.now()
	_M["worker_check_time"]["last_moniter_time"] = time_now
	_M["worker_check_time"]["last_reload_config_time"] = time_now
	_M["worker_check_time"]["last_clear_invalid_time"] = time_now
	_M["worker_check_time"]["last_fault_data_update_time"] = time_now
end


--[[
@comment backend请求相关数据统计 
@param string service
]]
function _M.backend_request_cnt(service, cnt_type, backend)
	local ctxs = ngx.ctx.ufc_ctxs

	--超时时间
	if not _M["request"]["timeout"] then
		_M["request"]["timeout"] = ngx.now()
	end

	if _M["request"]["timeout"] + _M["request_cnt_timeout"] <= ngx.now() then
		_M["request"] = {}
	end

	if not _M["request"][service] then
		_M["request"][service] = {}
	end

	if not _M["request"][service]["backend"] then
		_M["request"][service]["backend"] = {}
	end

	local request = _M["request"][service]["backend"]

	local m = "api"
	if ctxs["bypass"] and ctxs["bypass"] == 1 or ctxs["iscallback"] then
		m = "bypass"
	end

	if not request[m] then
		request[m] = {}
	end

	local info = request[m]
	if not info[backend] then
		info[backend] = {}
		info[backend]["fail_cnt"] = 0
		info[backend]["request_cnt"] = 0
	end

	local backend_info = info[backend]
	if not backend_info[cnt_type] then
		backend_info[cnt_type] = 0
	end

	backend_info[cnt_type] = backend_info[cnt_type] + 1
	if cnt_type ~= "request_cnt" then
		if ctxs["iscallback"] then
			return
		end

		if not backend_info["request_cnt"] then
			backend_info["request_cnt"] = 0
		end

		backend_info["request_cnt"] = backend_info["request_cnt"] + 1
	end

	--ngx.log(ngx.DEBUG, string.format("moniter service %s backend %s type %s type_cnt %s request_cnt %s", service, backend, cnt_type, backend_info[cnt_type], backend_info["request_cnt"]))
end

--[[
@comment 相关数据统计 
@param string service
]]
function _M.rate_api(service, size)
	local data = {}
	local info = _M["request"][service]
	if not info then
		return data
	end

	local service_rate = info["service"]
	if service_rate then
		data["service"] = service_rate
	end

	local backend_requests = info["backend"]

	if not data["backend"] then
		data["backend"] = {}
	end

	if backend_requests["bypass"] then
		local info = _M.get_top_request_cnt(backend_requests["bypass"], size)
		if info then
			data["backend"]["bypass"] = info
		end
	end

	if backend_requests["api"] then
		local info = _M.get_top_request_cnt(backend_requests["api"], size)
		if info then
			data["backend"]["api"] = info
		end
	end

	return data
end


--[[
@comment 按类型排序
@param string service
]]
function _M.get_top_request_cnt(backend_list, size)
	local items = {}
	for k, v in pairs(backend_list) do
		local info = {}
		info["fail_cnt"] = v["fail_cnt"]
		info["request_cnt"] = v["request_cnt"]
		info["backend"] = k

		table.insert(items, info)
	end

	if not next(items) then
		return {}
	end

	table.sort(items, function(a, b) return (tonumber(a["fail_cnt"]) > tonumber(b["fail_cnt"])) end)

	local i = 0
	local rates = {}
	for k, v in pairs (items) do
		if i >= size then
			break
		end

		table.insert(rates, v)
		i = i + 1
	end

	return rates
end


--[[
@comment backend请求相关数据统计 
@param string service
]]
function _M.service_request_cnt(service, cnt_type)
	local ctxs = ngx.ctx.ufc_ctxs

	--超时时间
	if not _M["request"]["timeout"] then
		_M["request"]["timeout"] = ngx.now()
	end

	if _M["request"]["timeout"] + _M["request_cnt_timeout"] <= ngx.now() then
		_M["request"] = {}
	end

	if not _M["request"][service] then
		_M["request"][service] = {}
	end

	if not _M["request"][service]["service"] then
		_M["request"][service]["service"] = {}
	end

	local request = _M["request"][service]["service"]

	local m = "api"
	if ctxs["bypass"] and ctxs["bypass"] == 1 or ctxs["iscallback"] then
		m = "bypass"
	end

	if not request[m] then
		request[m] = {}
	end

	local info = request[m]
	if not info[cnt_type] then
		info[cnt_type] = 0
	end

	if ctxs["bypass"] == 1 or ctxs["iscallback"] then
		info[cnt_type] = info[cnt_type] + 1
		return
	end

	info[cnt_type] = info[cnt_type] + 1
	if not info["request_cnt"] then
		info["request_cnt"] = 0
	end

	info["request_cnt"] = info["request_cnt"] + 1

	--ngx.log(ngx.DEBUG, string.format("moniter service %s type %s type_cnt %s request_cnt %s", service, cnt_type, info[cnt_type], info["request_cnt"]))
end

--[[
@comment proxy上部署service上报 
@param string service
]]
function _M.service_proxy_regiester(service, idc)
	if not _M["instance_service"][service] then
		_M["instance_service"][service] = {}
	end

	local idcTmp = idc or "all_idc"
	local info = _M["instance_service"][service]
	if not info[idcTmp] then
		info[idcTmp] = 0
	end

	--ngx.log(ngx.DEBUG, string.format("moniter service proxy register %s idc %s, cnt %s", service, idcTmp, info[idcTmp]))

	info[idcTmp] = info[idcTmp] + 1
end


--[[
@comment 单机故障上报 
@param int status
@param string backend
@param string bns
@param string idc
@param string service
]]
function _M.backend_collect(config, service, bns, idc, backend, status, reason)
	if not config or not config["moniter"] then
		return false
	end

	if not service or not bns or not backend then
		return false
	end

	local idc = idc or "all"

	--是否可以上报
	local ok = _M.check_enable(config, "backend")
	--ngx.log(ngx.DEBUG, string.format("moniter backend, enable %s", ok))
	if not ok then
		return false
	end

	--如果service故障，则取消上报
	if _M.check_service_timeout(service, bns, idc) then
		--ngx.log(ngx.DEBUG, string.format("moniter backend, service %s is lawine, bns %s, idc %s", service, bns, idc))
		return false
	end

	--backend上报频率检查
	if _M.check_backend_timeout(service, bns, idc, backend) then
		--ngx.log(ngx.DEBUG, string.format("moniter backend, service %s bns %s, idc %s backend %s reach max", service, bns, idc, backend))
		return false
	end

	local forbid_bakcend = _M["forbid"]["backend"]
	local item = {}
	item["backend"] = backend
	item["status"] = status
	item["service"] = service
	if reason ~= nil then
		item["reason"] = reason
	end

	table.insert(forbid_bakcend, item)

	--设置超时
	local timeout = config["backend_timeout"] or 300
	_M.set_backend_collect_timeout(service, bns, idc, backend, timeout)

	_M["quota"]["backend"] = _M["quota"]["backend"] + 1

	--ngx.log(ngx.DEBUG, string.format("moniter backend, service %s bns %s, idc %s backend %s, timeout %s, quota %s", service, bns, idc, backend, timeout, _M["quota"]["backend"]))
	return true
end

--[[
@comment 服务故障上报 
@param table config
@param string service
@param string bns
@param string idc
]]
function _M.service_collect(config, service, bns, idc)
	if not config or not config["moniter"] then
		return false
	end

	if not service or not bns then
		return false
	end

	local idc = idc or "all"

	--是否可以上报
	local ok = _M.check_enable(config, "service")
	--ngx.log(ngx.DEBUG, string.format("service moniter enable %s", ok))
	if not ok then
		return false
	end

	local timeout = config["moniter"]["service_timeout"] or 180

	--上报频率修改
	if _M.check_service_timeout(service, bns, idc) then
		--ngx.log(ngx.DEBUG, string.format("moniter service %s bns %s, idc %s reach max", service, bns, idc))
		return false
	end

	local forbid_service = _M["forbid"]["service"]
	local content = service .. ":" .. idc .. ":" .. bns
	table.insert(forbid_service, content)

	--设置超时
	_M.set_service_collect_timeout(service, idc, bns, timeout)

	_M["quota"]["service"] = _M["quota"]["service"] + 1

	--ngx.log(ngx.DEBUG, string.format("moniter service %s bns %s, idc %s, service_quota %d", service, bns, idc, _M["quota"]["service"]))
	return true
end

--[[
@comment 封禁bug服务上报 
@param table config
@param string service
@param string bns
@param string idc
]]
function _M.bug_service_collect(config, service, bns, idc, content)
	if not config or not config["moniter"] then
		return false
	end

	if not service or not bns then
		return false
	end

	local idc = idc or "all_idc"

	--是否可以上报
	local ok = _M.check_enable(config, "bug_service")
	--ngx.log(ngx.DEBUG, string.format("service moniter enable %s", ok))
	if not ok then
		return false
	end

	local timeout = config["moniter"]["bug_service_timeout"] or 180

	--上报频率修改
	if _M.check_bug_service_timeout(service, bns, idc) then
		--ngx.log(ngx.DEBUG, string.format("moniter service %s bns %s, idc %s reach max", service, bns, idc))
		return false
	end

	local bug_service = _M["bug"]["service"]
	local content = service .. ":" .. idc .. ":" .. bns .. ":" .. content
	table.insert(bug_service, content)

	--设置超时
	_M.set_bug_service_collect_timeout(service, idc, bns, timeout)

	_M["quota"]["bug_service"] = _M["quota"]["bug_service"] + 1

	--ngx.log(ngx.DEBUG, string.format("moniter service %s bns %s, idc %s, service_quota %d", service, bns, idc, _M["quota"]["service"]))
	return true
end
--[[
@comment backend频率控制 
@param string service
@param string bns
@param string idc
@param string backend
]]
function _M.check_backend_timeout(service, bns, idc, backend)
	local key = service .. "-" .. bns .. "-" .. idc .."-" .. backend

	if not _M["timeout"]["backend"][key] then
		return false
	end

	if _M["timeout"]["backend"][key] < ngx.now() then
		--过期
		return false
	end

	return true
end

--[[
@comment backend收集上报超时设置 
@param string service
@param string idc
@param string bns
@param string backend
@param string timeout
]]
function _M.set_backend_collect_timeout(service, bns, idc, backend, timeout)
	local key = service .. "-" .. bns .. "-" .. idc .."-" .. backend
	if not _M["timeout"]["backend"] then
		_M["timeout"]["backend"] = {}
	end

	_M["timeout"]["backend"][key] = ngx.now() + timeout
end

--[[
@comment service频率控制 
@param string service
@param string bns
@param string idc
]]
function _M.check_service_timeout(service, bns, idc)
	local key = service .. "-" .. bns .. "-" .. idc

	if not _M["timeout"]["service"][key] then
		return false
	end

	if _M["timeout"]["service"][key] < ngx.now() then
		--过期
		return false
	end

	return true
end

--[[
@comment service收集上报超时设置 
@param string service
@param string idc
@param string bns
@param string timeout
]]
function _M.set_service_collect_timeout(service, idc, bns, timeout)
	local key = service .. "-" .. bns .. "-" .. idc
	if not _M["timeout"]["service"] then
		_M["timeout"]["service"] = {}
	end

	_M["timeout"]["service"][key] = ngx.now() + timeout
end

--[[
@comment bug_service上报频率控制 
@param string service
@param string bns
@param string idc
]]
function _M.check_bug_service_timeout(service, bns, idc)
	local key = service .. "-" .. bns .. "-" .. idc

	if not _M["timeout"]["bug_service"][key] then
		return false
	end

	if _M["timeout"]["bug_service"][key] < ngx.now() then
		--过期
		return false
	end

	return true
end

--[[
@comment bug_service上报超时设置 
@param string service
@param string idc
@param string bns
@param string timeout
]]
function _M.set_bug_service_collect_timeout(service, idc, bns, timeout)
	local key = service .. "-" .. bns .. "-" .. idc
	if not _M["timeout"]["bug_service"] then
		_M["timeout"]["bug_service"] = {}
	end

	_M["timeout"]["bug_service"][key] = ngx.now() + timeout
end

--[[
@comment 开关 
@param table config
config example
{
"enable": 1,
"api": "ufc-moniter",
"uri": "/api",
"backend_timeout": 180,
"service_timeout": 60,
"backend_quota": 3,
"service_quota": 2
}
]]
function _M.check_enable(config, ac)
	--关闭
	if not config["moniter"]["enable"] or config["moniter"]["enable"] < 1 then
		return false
	end

	--check quota
	local now = ngx.now()
	local quota = config["moniter"][ac.."_quota"] or 10
	if _M["quota"][ac] + 1 == quota then
		--达到quota限制
		_M["timeout"][ac.."_quota"] = now + 60
		return true
	elseif _M["quota"][ac] < quota then
		--ngx.log(ngx.DEBUG, string.format("moniter %s quota is ok, %s < %s", ac, _M["quota"][ac], quota))
		return true
	end

	local timeout = _M["timeout"][ac.."_quota"]
	if now < timeout then
		--未过期
		--ngx.log(ngx.DEBUG, string.format("moniter %s quota not timeout", ac))
		return false
	end

	--重置上报次数
	_M["quota"][ac] = 1 

	--重置每分钟允许上报quota过期时间
	_M["timeout"][ac.."_quota"] = 0
	return true
end

--[[
@comment 报警收集 
@param string backend
@param string bns
@param string idc
@param string service
]]
function _M.warn_collect(config, action, key, message)
	if not config or not config["moniter"] then
		return false
	end

	--是否可以上报
	local ok = _M.check_enable(config, action)
	ngx.log(ngx.DEBUG, string.format("moniter enable %s", ok))
	if not ok then
		return false
	end

	--频率检查
	if _M.check_action_timeout(action, key) then
		ngx.log(ngx.DEBUG, string.format("moniter action %s key %s reach max", action, key))
		return false
	end

	if not _M["warn_cache"] then
		_M["warn_cache"] = {}
	end

	local data = _M["warn_cache"]
	if not data[action] then
		data[action] = {}
	end
	
	table.insert(data[action], message)

	--设置超时
	local timeout = config[action .. "_timeout"] or 300
	_M.set_action_timeout(action, key, timeout)

	if not _M["quota"] then
		_M["quota"] = {}
	end

	if not _M["quota"][action] then
		_M["quota"][action] = 0
	end

	_M["quota"][action] = _M["quota"][action] + 1

	ngx.log(ngx.DEBUG, string.format("moniter action %s, message %s", action, message))
	return true
end


--[[
@comment 频率控制 
@param string action
@param string key
]]
function _M.check_action_timeout(action, key)
	if not _M["timeout"] then
		return true
	end

	if not _M["timeout"][action] then
		_M["timeout"][action] = {}
		return false
	end

	if not _M["timeout"][action][key] then
		return false
	end

	if _M["timeout"][action][key] < ngx.now() then
		--过期
		return false
	end

	return true
end

--[[
@comment 收集上报超时设置 
@param string action
@param string key
@param string timeout
]]
function _M.set_action_timeout(action, key, timeout)
	if not _M["timeout"] then
		_M["timeout"] = {}
	end

	if not _M["timeout"][action] then
		_M["timeout"][action] = {}
	end

	_M["timeout"][action][key] = ngx.now() + timeout
end

--[[
@comment 清理过期
@param
]]
function _M.cleanup_timer(config)
	--关闭
	
	if not config["enable"] or config["enable"] < 1 then
		_M["forbid"]["backend"] = {}
		_M["forbid"]["service"] = {}
		_M["instance_service"] = {}
		_M["timeout"]["backend"] = {}
		_M["timeout"]["service"] = {}
		_M["timeout"]["backend_quota"] = 0
		_M["timeout"]["service_quota"] = 0
		_M["warn_cache"] = {}
		return false
	end

	--重置计数超时时间
	_M["request_cnt_timeout"] = config["request_cnt_timeout"] or 60

	
	--先缓存数据至共享内存，由共享内存统一发送
	_M.save_config_in_shared_memmory(config)

	local services = _M["timeout"]["service"]
	for k, v in pairs(services) do
		if v < ngx.now() then
			_M["timeout"]["service"][k] = nil
			--ngx.log(ngx.DEBUG, string.format("moniter timer %s is clean", k))
		end
	end

	local backends = _M["timeout"]["backend"]
	for k, v in pairs(backends) do
		if v < ngx.now() then
			_M["timeout"]["backend"][k] = nil
			--ngx.log(ngx.DEBUG, string.format("moniter timer %s is clean", k))
		end
	end
end

--[[
@comment 数据采集保存至共享内存
@param table logs
]]
function _M.save_config_in_shared_memmory(config)
	if not config then
		return
	end

	--组装数据
	local data = {}
	data["forbid"] = _M["forbid"]
	data["instance_service"] = _M["instance_service"]
	data["bug"] = _M["bug"]
	data["warn_list"] = _M["warn_cache"]
	data["invalid_timer"] = _M["invalid_timer"]
	data["bp_hash_err_service"] = _M["bp_hash_err_service"]
	data["ufc_version"] = conf["version"]

	shared_memory.merge_moniter_data(data)

	_M["forbid"]["backend"] = {}
	_M["forbid"]["service"] = {}
	_M["bug"]["service"] = {}
	_M["instance_service"] = {}
	_M["warn_cache"] = {}
	_M["invalid_timer"] = {}
	_M["bp_hash_err_service"] = {}
end

function _M.bp_hash_err_service_collect(from_service, to_service)
	local key = from_service .. "#" .. to_service
	if not _M["bp_hash_err_service"] then
		_M["bp_hash_err_service"] = {}
	end 
	table.insert(_M["bp_hash_err_service"], key)
end


return _M
