local ngx_worker_cache = require "ngx_worker_cache"
local global_running_cnt_balancer = require "global_running_cnt_balancer"
local math = require "math"
local process = require "ngx.process"
local moniter = require "moniter"
local ngx_worker_process_cron = require "ngx_worker_process_cron"
local ngx_privileged_process_cron = require "ngx_privileged_process_cron"
local conf = require "conf"
local privileged_process_cache = require "privileged_process_cache"
local worker_process_cache = require "worker_process_cache"
local meta_table = require "meta_table"
local utils = require "utils"
local shared_memory = require "shared_memory"
local gossip_load_config = require "gossip_load_config"
local _M = {
	["doc"] = "crontab reload config and report version"
}

--获取本机数字ip
function _M.get_local_ip()
	ngx.log(ngx.NOTICE, string.format("now begin to get local ip"))
	local f = io.popen("hostname -i", 'r')
	local s = f:read('*a')
	f:close()
	s = string.gsub(s, '^%s+', '')
	s = string.gsub(s, '%s+$', '')
	s = string.gsub(s, '[\n\r]+', ' ')
	ngx_worker_cache["local_ip"] = s
	ngx.log(ngx.NOTICE, string.format("now local ip is %s", s))
end

--定时器逻辑
function _M.main()
	--特权进程和worker进程公共逻辑
	--目前特权进程暂时用不到本机ip，idc等信息
	global_running_cnt_balancer.init_script()           -- TODO 这是做啥的
	math.randomseed(tonumber(string.format("%d%d", ngx.worker.pid(), ngx.crc32_short(utils.get_hostname()) + ngx.time())))
	ngx.log(ngx.NOTICE, string.format("load config main begin once"))
	-- 获取本地idc
	ngx_worker_cache.init_local_idc()
	--读area文件
	ngx_worker_cache.init_area()
	--获取本机ip
	ngx.timer.at(0, _M.get_local_ip)
	--workr进程不止一个设置dynamic_cache metatable
	if ngx.worker.count() > 1 then
		meta_table.set_meta_table()
		conf["meta_table"] = true
	end

	--特权进程独有逻辑
	if process.type() == "privileged agent" then
		--启动的时候把这个时间戳初始化一下
		shared_memory.static_data_set("static_cache", "last_update_time", ngx.time())
		--每隔一分种check一下共享内存容量
		ngx.timer.at(60, ngx_privileged_process_cron.check_shared_memmory_regularly)
		ngx.timer.at(60, ngx_privileged_process_cron.upload_moniter_data_regularly)
		--立刻读取本地配置,失败则读取远端配置
		ngx.timer.at(0, privileged_process_cache.load_config, false)
		--定时加载云端配置
		ngx.timer.at(math.random(conf["cache_reload_interval"] * 1), ngx_privileged_process_cron.reload_config_regularly)
		--异步检查封禁后端的连通性
		if conf["meta_table"] then
			ngx.timer.at(10, ngx_privileged_process_cron.check_forbid_by_connect_regularly)
		end
		--5s看一次有没有gossip传播请求过来
		ngx.timer.every(5, gossip_load_config.gossip_cron)
		--汇报自己的版本
		ngx.timer.at(60 + math.random(conf["cache_reload_interval"] * 5), ngx_privileged_process_cron.report_version_regularly)
	end

	--worker进程独有逻辑
	if process.type() == "worker" then
		if not conf["meta_table"] then
			ngx.timer.at(10, ngx_privileged_process_cron.check_forbid_by_connect_regularly)
		end
		--设置ufc定时器检查初始时间
		moniter.timer_check_init()
		--立刻读取本地配置,失败则读取远端配置
		ngx.timer.at(0, worker_process_cache.load_config, false)
		--定时加载云端配置
		ngx.timer.at(math.random(conf["cache_reload_interval"] * 1), ngx_worker_process_cron.reload_config_regularly)
		--清除无效数据
		ngx.timer.at(300, ngx_worker_process_cron.clear_invalid_backend_regularly)
		--报警信息上报
		ngx.timer.at(60, ngx_worker_process_cron.moniter_timer_regularly)
		--间断读取共享内存里面的故障注入配置信息
		ngx.timer.at(5, ngx_worker_process_cron.fault_data_update_regularly)
		--间断读取共享内存里面的dump配置信息
		ngx.timer.at(5, ngx_worker_process_cron.dump_data_update_regularly)
	end
	--目前分配ip之前会同步一下共享内存不需要这个逻辑了，
	--ngx.timer.at(5, _M.ip_data_update)
end

_M.main()
