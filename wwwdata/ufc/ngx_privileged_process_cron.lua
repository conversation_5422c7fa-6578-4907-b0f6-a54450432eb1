local conf = require "conf"
local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local moniter = require "moniter"
local global_running_cnt_balancer = require "global_running_cnt_balancer"
local cjson = require "cjson.safe"
local fault = require "fault"
local stream_utils = require("stream_utils")
local lock = require "lock"
local check_forbid_list = require "check_forbid_list"
local math = require "math"
local privileged_process_cache = require "privileged_process_cache"
local shared_memory = require "shared_memory"
local gossip_utils = require "gossip_utils"
local cjson = require "cjson"
local conf = require "conf"
local http = require "ufclib.Http"
local ufc   = require "ufclib.ufc"
local pcall = pcall
local _M = {
	["doc"] = "crontab reload config and report version"
}

--每一分钟检查一下共享内存的容量
--如果用了超过80%就报警，还打日志
function _M.check_shared_memmory_regularly()
	--默认一分钟检查一次
	local interval = shared_memory["check_interval"]
	local config = _M.get_timer_config("check_timer")
	if config then
		interval = config["interval"] or shared_memory["check_interval"]
	end
	--第一次手动执行要不然就得等到下个时间间隔
	shared_memory.check_shared_memmory()
	ngx.timer.every(interval, shared_memory.check_shared_memmory)
end

function _M.upload_moniter_data_regularly()
	--默认一分钟上报一次，第一次执行将在 60s 后，此时配置更新应该都 ready 了
	local interval = shared_memory["upload_moniter_interval"]
	-- 采用和 worker 进程一样的配置
	local config = _M.get_timer_config("moniter_timer")
	if config then
		interval = config["interval"] or 60
	end
	moniter["timer_interval"]["moniter_interval"] = interval
	
	--第一次手动执行要不然就得等到下个时间间隔
	_M.upload_moniter_data(false, interval)

	ngx.timer.every(interval, _M.upload_moniter_data, interval)
end 


-- 只有特权进程会执行该函数
function _M.upload_moniter_data(premature, interval)

	ngx.log(ngx.NOTICE, "upload moniter data begin")
	local timeNow = ngx.now()
	if not lock.timer_lock("upload_moniter_data", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:upload_moniter_data, worker id:%d, try to get lock failed"))
		return
	end

	local moniter_data = shared_memory.get_moniter_data()
	if not moniter_data then 
		moniter_data = {}
	end 
	
	
	local mtime = shared_memory.get_global_mtime_update_timestamp() or -1
	local privileged_latency = shared_memory.get_privilged_latency() or -1 
	local worker_latency = shared_memory.get_worker_latency() or -1
	moniter_data["mtime"] = mtime
	moniter_data["privileged_latency"] = privileged_latency
	moniter_data["worker_latency"] = worker_latency
	moniter_data["no_memory_cache"] = shared_memory["no_memory_cache"] or {}

	-- 上传数据
	local ufc_headers = {["x-ufc-self-service-name"] = "pcs-ufc-agent"}
	local uri = "/rest/2.0/ufcagent"

	local service = "ufc-admin"
	local body = cjson.encode(moniter_data)
	

	local upload_headers = {
		["Content-Type"] = "text/plain",
		["Content-Length"] = string.len(body)
	}

	local logid = ngx.time()
	local upload_err, res, re  
	local ip, port 
	for i=1, 2 do
		re, upload_err = ufc.bypass(service, logid, ufc_headers, true)
		if not upload_err and re and re.host and re.port then
			local addr = {domain = re.host, port = re.port}
			ip = re.host
			port = re.port
			res, upload_err = http.put(addr, uri, upload_headers, body)
			if not upload_err then
				break
			end

			ufc.callback_fail(re)
		end
	end

	body = nil

	if upload_err then 
		ngx.log(ngx.WARN, string.format("upload moniter data failed, ip: %s, port: %s, err %s",ip, port, upload_err))
	else
		ngx.log(ngx.WARN, string.format("upload moniter data success, ip: %s, port: %s", ip, port)) 
	end 


	-- 无论上传失败与否都需要清除数据，避免数据无限增大
	shared_memory.clear_moniter_data()
	-- 重置 worker_latency 为 -1.privileged_latency 不用重置
	shared_memory.set_worker_latency(-1)
	lock.timer_unlock("upload_moniter_data", timeNow)	

	ngx.log(ngx.NOTICE, "upload moniter data end")

end 

function _M.reload_config_regularly()
	local base_interval = 120
	local random_interval = 360
	local config = _M.get_timer_config("config_timer")
	if config then
		base_interval = config["base_interval"] or 120
		random_interval = config["random_interval"] or 360 
	end

	local reload_interval = base_interval + math.random(random_interval)
	moniter["timer_interval"]["reload_config_interval"] = reload_interval
	--第一次手动执行要不然就得等到下个时间间隔
	--这里用pcall执行，否则如果这里traceback后面every 函数执行不了
	local ok, err = pcall(_M.reload_config, false, reload_interval)
	if not ok then
		ngx.log(ngx.WARN, string.format("load config failed ,err %s", err))
	end
	ngx.timer.every(reload_interval, _M.reload_config, reload_interval)
end

--[[
@comment 定期更新配置
@return bool
]]
function _M.reload_config(premature, interval)
	local timeNow = ngx.now()
	if not lock.timer_lock("reload_config", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:reload_config, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	ngx.log(ngx.NOTICE, "load config begin now")
	moniter["worker_check_time"]["last_reload_config_time"] = timeNow
	----ngx.log(ngx.DEBUG, "timer: reload_config interval="..reload_interval.." base_interval="..base_interval.." random_interval="..random_interval)
	local res = privileged_process_cache.load_config(true, true)
	lock.timer_unlock("reload_config", timeNow)
	ngx.log(ngx.NOTICE, string.format("load config end, res:%s", res))
	return res
end

function _M.check_forbid_by_connect_regularly()
	local interval = 60
	local config = _M.get_timer_config("forbid_timer")
	if config then
		interval = config["interval"] or 60
	end
	moniter["timer_interval"]["check_forbid_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	local ok, err = pcall(_M.check_forbid_by_connect, false, interval)
	if not ok then
		ngx.log(ngx.WARN, string.format("check forbid by connect failed ,err %s", err))
	end
	ngx.timer.every(interval, _M.check_forbid_by_connect, interval)
end


function _M.report_version_regularly()
	local base_interval = 8
	local random_interval = 1800
	local config = _M.get_timer_config("version_timer")

	local enable = false
	if config then
		if config["enable"] and config["enable"] > 0 then
			enable = true
		end

		base_interval = config["base_interval"] or 8
		random_interval = config["random_interval"] or 1800
	end

	local report_interval = base_interval + math.random(random_interval)
	moniter["timer_interval"]["report_version_interval"] = report_interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.report_version(false, enable, report_interval)
	ngx.timer.every(report_interval, _M.report_version, enable, report_interval)
end


--[[
@comment 获取定时器配置 
@param string timer
@return table
]]
function _M.get_timer_config(timer)
	if not ngx_worker_cache["static_cache"] then
		return nil
	end
	
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return nil
	end

	if not configs["timer"] then
		return nil
	end

	local config = configs["timer"][timer]
	if timer == "config_timer" and config then
		local idc = conf.get_local_location()
		return config[idc]
	end

	return config
end

--[[
@comment 获取定时器配置 
@param string timer
@return table
]]

function _M.report_version(premature, enable, interval)
	local timeNow = ngx.now()
	if not lock.timer_lock("report_version", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:report_version, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_report_version_time"] = ngx.now()
	local red, err
	local prefix
	local my_version, latest_version

	if enable == false then
		ngx.log(ngx.NOTICE, "timer: report version close")
		lock.timer_unlock("report_version", timeNow)
		return
	end

	my_version = conf.version
	red, err = conf.get_redis()
	if err then
		lock.timer_unlock("report_version", timeNow)
		return conf["res"]["FAILED"]
	end

	prefix = conf.get_redis_prefix()
	latest_version = red:get(string.format("%s-latest-version", prefix))   -- p-3ufc-nanjing01-latest-version
	if latest_version == nil then
		red:close()
		lock.timer_unlock("report_version", timeNow)
		return conf["res"]["FAILED"]
	end

	local versions_key = string.format("%s-versions", prefix)  -- p-3ufc-nanjing01-versions
	if my_version ~= latest_version then
		red:hset(versions_key, utils.get_hostname(), my_version)
	end
	
	red:close()
	--上报gossip分组数据
	ngx.log(ngx.NOTICE, "upload gossip peer info start")
	local res = gossip_utils.peer_upload()
	ngx.log(ngx.NOTICE, string.format("upload gossip peer info end, res is %s", res))
	-----
	
	lock.timer_unlock("report_version", timeNow)
	return conf["res"]["SUCCESS"]
end

-- 检查连通性, 连不上的backend, 仍然保持在封禁列表中
function _M.check_forbid_by_connect(premature, interval)
	local timeNow = ngx.now()
	if not lock.timer_lock("check_forbid", timeNow , interval) then
		ngx.log(ngx.WARN, string.format("timer:check_forbid, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_check_forbid_time"] = timeNow 

	check_forbid_list.check_forbid()
	lock.timer_unlock("check_forbid", timeNow)
end

return _M