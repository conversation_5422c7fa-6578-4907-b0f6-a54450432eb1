local conf = require "conf"
local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local moniter = require "moniter"
local cjson = require "cjson.safe"
local stream_utils = require("stream_utils")

local math = require "math"
local _M = {
	["doc"] = "crontab reload config and report version"
}

--定时器加锁
function _M.lock(name, timeNow, interval)
	if not moniter["timer_lock"][name] then
		moniter["timer_lock"][name]  = {}
	end
	local locks = moniter["timer_lock"][name]
	--找到锁里面最大的时间戳
	local max_time = -1
	local num = 0
	for k, v in pairs(locks) do
		num = num + 1
		if k > max_time then
			max_time = k
		end
	end
	--如果距离上次加锁时间小于两个间隔时间，说明还有定时器在跑，加锁失败，
	if max_time > 0 and timeNow - max_time < 2 * interval then
		ngx.log(ngx.WARN, string.format("lock failed, timer name:%s, last lock time:%f, lock num:%d", name, max_time, num))
		return false
	end

	if max_time > 0 then
		--距离上次加锁时间大于两个时间间隔加的锁
		ngx.log(ngx.WARN, string.format("lock success, timer name:%s, last lock time:%f, lock num:%d", name, max_time, num))
	end

	--加锁
	locks[timeNow] = 1
	return true
end

--定时器解锁
function _M.unlock(name, timeNow)
	if not moniter["timer_lock"][name] then
		moniter["timer_lock"][name]  = {}
	end
	local locks = moniter["timer_lock"][name]
	locks[timeNow] = nil
end


function _M.reload_config_regularly()
	local base_interval = 120
	local random_interval = 360
	local config = _M.get_timer_config("config_timer")
	if config then
		base_interval = config["base_interval"] or 120
		random_interval = config["random_interval"] or 360 
	end

	local reload_interval = base_interval + math.random(random_interval)
	moniter["timer_interval"]["reload_config_interval"] = reload_interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.reload_config(false, reload_interval)
	ngx.timer.every(reload_interval, _M.reload_config, reload_interval)
end

function _M.check_forbid_by_connect_regularly()
	local interval = 60
	local config = _M.get_timer_config("forbid_timer")
	if config then
		interval = config["interval"] or 60
	end
	moniter["timer_interval"]["check_forbid_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.check_forbid_by_connect(false, interval)
	ngx.timer.every(interval, _M.check_forbid_by_connect, interval)
end

function _M.clear_invalid_backend_regularly()
	local interval = 600 
	local config = _M.get_timer_config("trash_timer")
	if config then
		interval = config["interval"] or 600	
	end
	moniter["timer_interval"]["clear_invalid_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.clear_invalid_backend(false, interval)
	ngx.timer.every(interval, _M.clear_invalid_backend, interval)
end

function _M.moniter_timer_regularly()
	local interval = 60
	local config = _M.get_timer_config("moniter_timer")
	if config then
		interval = config["interval"] or 60
	end
	moniter["timer_interval"]["moniter_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.moniter_timer(false, interval)
	ngx.timer.every(interval, _M.moniter_timer, interval)
end

--定时将共享内存中的动态ip映射数据同步到内存中
function _M.ip_data_update()
	local interval = 5
	local config = _M.get_timer_config("fault_timer")
	if config then
		interval = config["interval"] or 5
	end
	ngx_worker_cache.load_ip_mapping_data()
	ngx.timer.at(interval, _M.ip_data_update)
	return
end

--[[
@comment 获取定时器配置 
@param string timer
@return table
]]
function _M.get_timer_config(timer)
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return nil
	end

	if not configs["timer"] then
		return nil
	end

	local config = configs["timer"][timer]
	if timer == "config_timer" and config then
		local idc = conf.get_local_location()
		return config[idc]
	end

	return config
end

--[[
@comment moniter 
@return bool
]]
function _M.moniter_timer(premature, interval)
	local timeNow = ngx.now()
	if not _M.lock("moniter", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:moniter, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_moniter_time"] = timeNow

	if not ngx_worker_cache["static_cache"] then
		_M.unlock("moniter", timeNow)
		return 
	end

	local configs = ngx_worker_cache["static_cache"]["configs"]
	if configs and configs["moniter"] then
		ngx.log(ngx.NOTICE, "moniter timer begin")
		moniter.cleanup_timer(configs["moniter"])
		ngx.log(ngx.NOTICE, "moniter timer end")
	end
	_M.unlock("moniter", timeNow)
end

--[[
@comment 定期更新配置
@return bool
]]
function _M.reload_config(premature, interval)
	local timeNow = ngx.now()
	if not _M.lock("reload_config", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:reload_config, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	ngx.log(ngx.NOTICE, "load config begin now")
	moniter["worker_check_time"]["last_reload_config_time"] = timeNow
	--ngx.log(ngx.DEBUG, "timer: reload_config interval="..reload_interval.." base_interval="..base_interval.." random_interval="..random_interval)
	local res = ngx_worker_cache.load_config(true)
	_M.unlock("reload_config", timeNow)
	ngx.log(ngx.NOTICE, string.format("load config end, res:%s", res))
	return res
end

-- 检查连通性, 连不上的backend, 仍然保持在封禁列表中
function _M.check_forbid_by_connect(premature, interval)
	local timeNow = ngx.now()
	if not _M.lock("check_forbid", timeNow , interval) then
		ngx.log(ngx.WARN, string.format("timer:check_forbid, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_check_forbid_time"] = timeNow 
	ngx_worker_cache.check_forbid()
	_M.unlock("check_forbid", timeNow)
end

-- 清除无效backend数据
function _M.clear_invalid_backend(premature, interval)
	local timeNow = ngx.now()
	if not _M.lock("clear_invalid_backend", timeNow , interval) then
		ngx.log(ngx.WARN, string.format("clear_invalid_backend, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_clear_invalid_time"] = timeNow
	ngx.log(ngx.NOTICE, "clear invalid backend begin")
	ngx_worker_cache.clear_invalid_backend()
	ngx.log(ngx.NOTICE, "clear invalid backend end")
	_M.unlock("clear_invalid_backend", timeNow)
end


--获取本机数字ip
function _M.get_local_ip()
	ngx.log(ngx.NOTICE, string.format("now begin to get local ip"))
	local f = io.popen("hostname -i", 'r')
	local s = f:read('*a')
	f:close()
	s = string.gsub(s, '^%s+', '')
	s = string.gsub(s, '%s+$', '')
	s = string.gsub(s, '[\n\r]+', ' ')
	ngx_worker_cache["local_ip"] = s
	ngx.log(ngx.NOTICE, string.format("now local ip is %s", s))
end

-- 入口
function _M.main()
	conf["stream"] = 1
	math.randomseed(tonumber(string.format("%d%d", ngx.worker.pid(), ngx.crc32_short(utils.get_hostname()) + ngx.time())))
	ngx.log(ngx.NOTICE, string.format("load config main begin once"))
	ngx.timer.at(0, ngx_worker_cache.load_config, false)                                    -- 立刻读取本地配置,失败则读取远端配置
	--设置ufc定时器检查初始时间
	moniter.timer_check_init()
	ngx.timer.at(math.random(conf["cache_reload_interval"] * 1), _M.reload_config_regularly)    -- 间断读取redis配置 2-8min

	-- 异步检查封禁后端的连通性
	ngx.timer.at(10, _M.check_forbid_by_connect_regularly)

	--清除无效数据
	ngx.timer.at(300, _M.clear_invalid_backend_regularly)

	-- log moniter
	ngx.timer.at(60, _M.moniter_timer_regularly)

	-- 获取本地idc
	ngx_worker_cache.init_local_idc()

	--读area文件
	ngx_worker_cache.init_area()

	--获取本机ip
	ngx.timer.at(0, _M.get_local_ip)

	--目前分配ip之前会同步一下共享内存不需要这个逻辑了，
	--ngx.timer.at(5, _M.ip_data_update)
	--定时上报的逻辑http模块里面有，这里也不需要了
end

_M.main()
