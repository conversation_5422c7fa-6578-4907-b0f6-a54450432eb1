local conf = require "conf"
local ngx_worker_cache = require "ngx_worker_cache"
local utils = require "utils"
local moniter = require "moniter"
local global_running_cnt_balancer = require "global_running_cnt_balancer"
local cjson = require "cjson.safe"
local fault = require "fault"
local stream_utils = require("stream_utils")
local lock = require "lock"
local worker_process_cache = require "worker_process_cache"
local math = require "math"
local shared_memory = require "shared_memory"
local gossip_utils = require "gossip_utils"
local dump = require "dump"
local string = string
local pcall = pcall
local _M = {
	["doc"] = "crontab reload config and report version"
}

function _M.reload_config_regularly()
	local base_interval = 8
	local random_interval = 20
	local config = _M.get_timer_config("config_timer")
	if config then
		base_interval = config["worker_base_interval"] or 8
		random_interval = config["worker_random_interval"] or 20 
	end
	--worker进程大概15 s检查一次共享内存
	local reload_interval = base_interval + math.random(random_interval)
	moniter["timer_interval"]["reload_config_interval"] = reload_interval
	--第一次手动执行要不然就得等到下个时间间隔
	--这里用pcall执行，否则如果这里traceback后面every 函数执行不了
	local ok ,err = pcall(_M.reload_config, false, reload_interval)
	if not ok then
		ngx.log(ngx.WARN, string.format("load config failed ,err %s", err))
	end
	ngx.timer.every(reload_interval, _M.reload_config, reload_interval)
end

function _M.clear_invalid_backend_regularly()
	local interval = 600 
	local config = _M.get_timer_config("trash_timer")
	if config then
		interval = config["interval"] or 600	
	end
	moniter["timer_interval"]["clear_invalid_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.clear_invalid_backend(false, interval)
	ngx.timer.every(interval, _M.clear_invalid_backend, interval)
end

function _M.moniter_timer_regularly()
	local interval = 60
	local config = _M.get_timer_config("moniter_timer")
	if config then
		interval = config["interval"] or 60
	end
	moniter["timer_interval"]["moniter_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.moniter_timer(false, interval)
	ngx.timer.every(interval, _M.moniter_timer, interval)
end

function _M.fault_data_update_regularly()
	local interval = 5
	local config = _M.get_timer_config("fault_timer")
	if config then
		interval = config["interval"] or 5
	end
	moniter["timer_interval"]["fault_data_update_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.fault_data_update(false, interval)
	ngx.timer.every(interval, _M.fault_data_update, interval)
end

function _M.dump_data_update_regularly()
	local interval = 5
	local config = _M.get_timer_config("dump_timer")
	if config then
		interval = config["interval"] or 5
	end
	moniter["timer_interval"]["dump_data_update_interval"] = interval
	--第一次手动执行要不然就得等到下个时间间隔
	_M.dump_data_update(false, interval)
	ngx.timer.every(interval, _M.dump_data_update, interval)
end

--定时将共享内存中的动态ip映射数据同步到内存中
function _M.ip_data_update()
	local interval = 5
	local config = _M.get_timer_config("fault_timer")
	if config then
		interval = config["interval"] or 5
	end
	ngx_worker_cache.load_ip_mapping_data()
	ngx.timer.at(interval, _M.ip_data_update)
	return
end

function _M.fault_data_update(premature, interval)
	local timeNow = ngx.now()
	moniter["worker_check_time"]["last_fault_data_update_time"] = timeNow
	if not lock.timer_lock("fault_data_update", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:fault_data_update, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end

	if not lock.lock_config("fault") then 
		lock.timer_unlock("fault_data_update", timeNow)
		ngx.log(ngx.WARN, string.format("timer:fault_data_update, worker id:%d, try to get fault lock failed", ngx.worker.pid()))
		return 
	end

	local fault_data = worker_process_cache.load_fault_config()
	if fault_data == nil then
		lock.timer_unlock("fault_data_update", timeNow)
		lock.unlock_config("fault")
		return
	end

	-- 上述从共享内存中读取配置，到清理共享内存后的过期数据后重新写入这个时间间隔里，即本注释所在的代码行运行时刻中
	-- 可能会出现调用 fault 接口的情形，接入显示成功写入但实际被覆盖，因此，需要加锁 lock_config

	--清理内存中过期数据
	fault.fault_data_cleanup(fault_data)
	--更新共享内存中的故障信息
	local body = cjson.encode(fault_data)
	local shared_fault_data = ngx.shared.fault

	shared_fault_data:set("fault", body)
	lock.unlock_config("fault")
	lock.timer_unlock("fault_data_update", timeNow)
	return
end

function _M.dump_data_update(premature, interval)
	local timeNow = ngx.now()
	moniter["worker_check_time"]["last_dump_data_update_time"] = timeNow
	if not lock.timer_lock("dump_data_update", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:dump_data_update, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end

	if not lock.lock_config("dump") then 
		lock.timer_unlock("dump_data_update", timeNow)
		ngx.log(ngx.WARN, string.format("timer:dump_data_update, worker id:%d, try to get dump lock failed", ngx.worker.pid()))
		return 
	end

	local dump_data = worker_process_cache.load_dump_config()
	if dump_data == nil then
		lock.timer_unlock("dump_data_update", timeNow)
		lock.unlock_config("dump")
		return
	end

	--清理内存中过期数据
	dump.dump_data_cleanup(dump_data)
	--更新共享内存中的故障信息
	local body = cjson.encode(dump_data)
	local shared_dump_data = ngx.shared.dump

	shared_dump_data:set("dump", body)
	lock.unlock_config("dump")
	lock.timer_unlock("dump_data_update", timeNow)
	return
end

--[[
@comment 获取定时器配置 
@param string timer
@return table
]]
function _M.get_timer_config(timer)
	if not  ngx_worker_cache["static_cache"] then
		return nil
	end
	
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return nil
	end

	if not configs["timer"] then
		return nil
	end

	local config = configs["timer"][timer]
	if timer == "config_timer" and config then
		local idc = conf.get_local_location()
		return config[idc]
	end

	return config
end

--[[
@comment moniter 
@return bool
]]
function _M.moniter_timer(premature, interval)
	local timeNow = ngx.now()
	if not lock.timer_lock("moniter", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:moniter, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_moniter_time"] = timeNow

	if not ngx_worker_cache["static_cache"] then
		lock.timer_unlock("moniter", timeNow)
		return 
	end

	local configs = ngx_worker_cache["static_cache"]["configs"]
	if configs and configs["moniter"] then
		ngx.log(ngx.NOTICE, "moniter timer begin")
		moniter.cleanup_timer(configs["moniter"])
		ngx.log(ngx.NOTICE, "moniter timer end")
	end
	lock.timer_unlock("moniter", timeNow)
end



--[[
@comment 定期更新配置
@return bool
]]
function _M.reload_config(premature, interval)
	local timeNow = ngx.now()
	if not lock.timer_lock("reload_config", timeNow, interval) then
		ngx.log(ngx.WARN, string.format("timer:reload_config, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	ngx.log(ngx.NOTICE, "load config begin now")
	moniter["worker_check_time"]["last_reload_config_time"] = timeNow
	----ngx.log(ngx.DEBUG, "timer: reload_config interval="..reload_interval.." base_interval="..base_interval.." random_interval="..random_interval)
	local res = worker_process_cache.load_config(true, true)
	lock.timer_unlock("reload_config", timeNow)
	ngx.log(ngx.NOTICE, string.format("load config end, res:%s", res))
	return res
end

-- 清除无效backend数据
function _M.clear_invalid_backend(premature, interval)
	local timeNow = ngx.now()
	if not lock.timer_lock("clear_invalid_backend", timeNow , interval) then
		ngx.log(ngx.WARN, string.format("clear_invalid_backend, worker id:%d, try to get lock failed", ngx.worker.pid()))
		return
	end
	moniter["worker_check_time"]["last_clear_invalid_time"] = timeNow
	ngx.log(ngx.NOTICE, "clear invalid backend begin")
	ngx_worker_cache.clear_invalid_backend()
	ngx.log(ngx.NOTICE, "clear invalid backend end")
	lock.timer_unlock("clear_invalid_backend", timeNow)
end

--update lantency定时上报
function _M.update_lantency_upload(update_lantency, timenow)
	local lantency_upload_interval = _M.get_timer_config("lantency_upload_timer")
	if not lantency_upload_interval then
		return
	end

	local last_upload_time = shared_memory.get("static_cache", "last_upload_lantency_time")
	if last_upload_time and timenow - last_upload_time <= lantency_upload_interval then
		return
	end
	shared_memory.static_data_set("static_cache", "last_upload_lantency_time", timenow)
	ngx.timer.at(0, _M.update_lantency_upload_timer, update_lantency)
	return
end

--获取本机所在分组，优先拿gossip逻辑算好的index
function _M.get_peer_group_index()
	--gossip协议上报的时候会把index信息写到共享内存里面
	--先看看共享内存里面有没有，没有就自己算
	local group_index = shared_memory.get("static_cache", "group_index")
	if group_index then
		return group_index
	end

	local local_ip = ngx_worker_cache["local_ip"]
	if not local_ip then
		ngx.log(ngx.WARN, "local ip is nil, stop upload update lantency")
		return
	end
	local num = gossip_utils.ipToInt(local_ip)
	
	if not ngx_worker_cache["static_cache"]["configs"] or not ngx_worker_cache["static_cache"]["configs"]["gossip"] then
		return
	end

	local gossip_config = ngx_worker_cache["static_cache"]["configs"]["gossip"]
	if not gossip_config["enable"] or not gossip_config["idc_group_num"]then
		return
	end
	--每个大机房有个默认的分组
	local group_num = gossip_config["idc_group_num"]["default"]
	local idc = ngx_worker_cache["local_idc"]
	if idc and gossip_config["idc_group_num"][idc] then
		group_num = gossip_config["idc_group_num"][idc]
	end

	if not group_num then
		return
	end
	group_index = num % group_num
	shared_memory.static_data_set("static_cache", "group_index", group_index)
	return group_index
end

function _M.update_lantency_upload_timer(premature, update_lantency)
	local index = _M.get_peer_group_index()
	if not index then
		return
	end

	local prefix = conf.get_redis_prefix()
	local idc = ngx_worker_cache["local_idc"]
	local key = string.format("%s-%s-%d-lantency",prefix, idc, index)
	local ip = ngx_worker_cache["local_ip"]
	local red, err = conf.get_redis()
	if err ~= nil then
		red:close()
		ngx.log(ngx.WARN, string.format("upload update lantency get redis failed, error is %s", err))
		return
	end

	local res, err  = red:hset(key, ip, update_lantency)
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("upload update lantency hset redis failed, error is %s", err))
		red:close()
		return
	end
	red:close()
end

return _M
