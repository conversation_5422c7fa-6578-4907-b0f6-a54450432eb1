local conf = require "conf"
local lock = require "lock"
local cjson = require "cjson.safe"
local utils = require "utils"
local shared_memory = require "shared_memory"
local ngx_worker_cache = require "ngx_worker_cache"
local string = string
local tonumber = tonumber
local tostring = tostring
local table = table
local pairs = pairs
local gossip_utils = require "gossip_utils"
local gossip_load_config = require "gossip_load_config"

local _M = {
	["doc"] = "ngx_worker_cache function in privileged process",
	--要写到磁盘的数据先放到缓存中，最后一起写磁盘
	--避免磁盘满特权进程写磁盘挂掉之后后面的配置无法更新
	["cache_table"] = table.new(0, 1000)
}

--特权进程对 ngx_worker_cache 独有的函数操作抽离出来放到这里
--一些公共的操作还是放在ngx_worker_cache.lua里面
--这个文件里面仅仅只是函数操作，数据还是放在ngx_worker_cache里面
function _M.load_config(premature, latest)
	local r1, r2
	r1 = ngx_worker_cache._load_local_config()
	if not latest then
		if r1 == conf["res"]["SUCCESS"] then
			--如果redis配置中有idc相关配置，替换conf
			ngx_worker_cache.update_idc_conf()
			ngx_worker_cache._init()
			return conf["res"]["SUCCESS"]
		end
	end

	--如果正在配置更新过程中就停止从redis拉配置
	if not gossip_utils.config_update_lock() then
		ngx.log(ngx.WARN, "is in gossip process, stop load config from redis")
		return conf["res"]["NONEED"]
	end

	r2 = _M._load_cloud_config()
	if r2 ~= conf["res"]["FAIL"] then
		--更新配置更新时间戳
		shared_memory.static_data_set("static_cache", "last_update_time", ngx.time())
	end



	--云端配置更新完成之后需要保存meta信息（io操作尽量在特权进程做）
	if r2 == conf["res"]["SUCCESS"] then
		--如果redis配置中有idc相关配置，替换conf
		ngx_worker_cache.update_idc_conf()
		ngx_worker_cache.save_ufc_meta_info(_M["cache_table"])
		--之前的写磁盘操作其实只把数据写到cache_table里面，在这里把他们全部写到磁盘上面
		--即使磁盘满了特权进程挂掉，共享内存里面的数据也是新的不会影响到worker进程
		ngx_worker_cache.save_cache_table(_M["cache_table"])
		--更新成功开始向其他peer扩散配置
		gossip_load_config.broadcast_conifg()
	end
	gossip_utils.config_update_unlock()
	return r2
end


--特权进程从云端更新配置信息
function _M._load_cloud_config()
    local red, err
    local res
    local perfix
    local cached_global_mtime, global_mtime

    res = conf["res"]["SUCCESS"]
    red, err = conf.get_redis()
    if err ~= nil then
        ngx.log(ngx.WARN, string.format("load config, get redis fail, err is %s", err))
        return conf["res"]["FAIL"]
    end

    local prefix = conf.get_redis_prefix()

    global_mtime, err = red:get(string.format("%s-mtime", prefix))
    if err ~= nil or global_mtime == ngx.null then
        red:close()
        ngx.log(ngx.WARN, string.format("load config, global mtime get fail, key:%s-mtime, err:%s",
            prefix, err))
        return conf["res"]["FAIL"]
    end


    cached_global_mtime = shared_memory.get_global_mtime()
    if cached_global_mtime and cached_global_mtime == global_mtime then
        red:close()
        ngx.log(ngx.NOTICE, string.format("load config, global mtime unchange %s, stop loading", global_mtime))
        return conf["res"]["NONEED"]
    end
    ngx.log(ngx.NOTICE, string.format("load config, global mtime changed, local:%s, cloud:%s", cached_global_mtime, global_mtime))

	--这里我们先加载bns数据再加载service数据，因为service里面bns算auto配置要用到bns数据
	--同时idc和平台相关数据也要先加载，要不然服务切bns的时候会出现service配置先更新了bns数据没更新的问题
	--之前在redis配置更新和gossip协议里面顺序不对没问题是因为最后才更新总的mtime，现在worker进程会主动拉一次配置这里要做一下修复
	res = _M._load_one_type_cloud_config(red, "bns")
    ngx.log(ngx.NOTICE, string.format("load config, cloud bnss res is %s", res))
    if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
    end

	--load idc maps
    res = _M._load_one_type_cloud_config(red, "idcmap")
    ngx.log(ngx.NOTICE, string.format("load config, cloud idcmap res is %s", res))
	if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
    end
    
	--load platform map
	res = _M._load_one_type_cloud_config(red, "platformmap")
	ngx.log(ngx.NOTICE, string.format("load config, cloud platformmap res is %s", res))
	if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
    end

	--load platform idc maps
	res = _M._load_one_type_cloud_config(red, "platformidcmap")
	ngx.log(ngx.NOTICE, string.format("load config, cloud platformidcmap res is %s", res))
	if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
    end


    res = _M._load_one_type_cloud_config(red, "service")
    ngx.log(ngx.NOTICE, string.format("load config, cloud services res is %s", res))

    if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
    end

	res = _M._load_one_type_cloud_config(red, "config")
	--整体配置保存在本地
	if res == conf["res"]["SUCCESS"] then
		ngx_worker_cache.save_config_local("configs", ngx_worker_cache["static_cache"]["configs"], _M["cache_table"])
	end
    ngx.log(ngx.NOTICE, string.format("load config, cloud configs res is %s", res))
    if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
    end

	--为了保证stream模块和http模块可以使用同一份本地配置文件，它们需要加载两份映射配置
	--这两份映射正好是相对的
	res = _M._load_one_type_cloud_config(red, "http_ip")
	--整体配置保存在本地
	if res == conf["res"]["SUCCESS"] then
		ngx_worker_cache.save_config_local("http_ips", ngx_worker_cache["static_cache"]["http_ips"], _M["cache_table"])
	end


	ngx.log(ngx.NOTICE, string.format("load config, cloud http_ip res is %s", res))
    if res == conf["res"]["FAIL"] then
        red:close()
        return conf["res"]["FAIL"]
	end

	res = _M._load_one_type_cloud_config(red, "stream_ip")
	--整体配置保存在本地
	if res == conf["res"]["SUCCESS"] then
		ngx_worker_cache.save_config_local("stream_ips", ngx_worker_cache["static_cache"]["stream_ips"], _M["cache_table"])
	end
    ngx.log(ngx.NOTICE, string.format("load config, cloud stream_ip res is %s", res))
    if res == conf["res"]["FAIL"] then
		red:close()
        return conf["res"]["FAIL"]
    end
	red:close()
	--global mtime写到共享内存中
	shared_memory.set_global_mtime(global_mtime)

	--把当前时间戳写到共享内存，供后续 worker 进程更新 worker_latency
	local now_timestamp = ngx.time()
	shared_memory.set_global_mtime_update_timestamp(now_timestamp)
	--更新 privileged_latency 时间戳
	local privileged_latency = now_timestamp - global_mtime
	shared_memory.set_privileged_latency(privileged_latency)

    ngx_worker_cache["static_cache"]["mtime"] = global_mtime
    return conf["res"]["SUCCESS"]
end

function _M._load_one_type_cloud_config(red, config_type)
    local res, err
    local config_cnt_key, config_cnt
	local prefix
	local needed_update_config_names, needed_update_config_mtimes
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end

    prefix = conf.get_redis_prefix()

    config_cnt_key = string.format("%s-%ss-cnt", prefix, config_type)
    config_cnt, err = red:get(config_cnt_key)
    if err or config_cnt == ngx.null then
        ngx.log(ngx.WARN, string.format("config %s cnt miss, err is %s", config_type, err))
        return conf["res"]["FAIL"]
	end

	--config的cnt数据写到共享内存中去
	--idcmap不用写，它的数据和bns放在一起
	if config_type ~= "idcmap" and config_type ~= "platformmap" and config_type ~= "platformidcmap" then
		shared_memory.set_config_cnt(config_type, config_cnt)
	end
	
	--批量获取需要更新的config名字以及mtime
	needed_update_config_names, needed_update_config_mtimes, err = _M._batch_get_needed_update_config_names(red, config_type, config_cnt)
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("get %s needed update config names fail, err is %s", config_type, err))
		return conf["res"]["FAIL"]
	end

	--bns 和idcmap value过大，不进行批量加载
	if config_type ~= "bns" and config_type ~= "idcmap" and config_type ~= "platformmap" and config_type ~= "platformidcmap" then
		res = _M._batch_load_cloud_config(red, config_type, needed_update_config_names, needed_update_config_mtimes)
		if res == conf["res"]["FAIL"] then
			return conf["res"]["FAIL"]
		end
	else
		for _, config_name in ipairs(needed_update_config_names) do
			res = _M._load_one_cloud_config(red, config_type, config_name, needed_update_config_mtimes)
			if res == conf["res"]["FAIL"] then
				return conf["res"]["FAIL"]
			end


		end
	end
	return conf["res"]["SUCCESS"]
end

function _M._batch_load_cloud_config(red, config_type, config_names, config_mtimes)
	local prefix = conf.get_redis_prefix()
	local batch_size = _M._get_batch_size(red)
	ngx.log(ngx.NOTICE, string.format("now batch size is %d", batch_size))

	local config_cnt = #config_names
	local last_index = 1
	local configs = {}
	local err = nil

	--批量获取所有config的内容
	while true do
		local stop = 0
		local config_keys = {}
		for i = 1, batch_size do 
			if last_index > config_cnt then
				stop = 1
				break
			end
			local config_key = string.format("%s-%s-%s", prefix, config_type, config_names[last_index])
			table.insert(config_keys, config_key)
			last_index = last_index + 1 
		end

		local res = {}
		if #config_keys > 0 then
			res, err = red:mget(unpack(config_keys))
			if err ~= nil or res == ngx.null then
				ngx.log(ngx.WARN, string.format("batch get configs failed, config_type is %s err is %s",config_type, err))
				return conf["res"]["FAIL"]
			end
			local len = #config_keys
			for j = 1, len do
				configs[config_names[last_index - len -1 +j]] = res[j]
			end
		end

		if stop == 1 then
			break
		end
	end

	--对云端配置进行处理
	return _M._cloud_config_content_handle(config_type, configs, config_mtimes)
end

--批量获取需要更新的configs名字
function _M._batch_get_needed_update_config_names(red, config_type, config_cnt)
	config_cnt = tonumber(config_cnt)
	local batch_size = _M._get_batch_size(red)
	ngx.log(ngx.NOTICE, string.format("now batch size is %d", batch_size))

	local configs = {}
	local config_mtimes = {}
	local last_index = 0
	local prefix = conf.get_redis_prefix()
	local needed_update_config = {}
	local err = nil
	--获取所有的service name
	while true do
		local stop = 0
		local config_name_keys = {}
		for i = 1, batch_size do 
			if last_index >= config_cnt then
				stop = 1
				break
			end
			local config_name_key = string.format("%s-%ss-%d", prefix, config_type, last_index)
			table.insert(config_name_keys, config_name_key)
			last_index = last_index + 1 
		end

		local res = {}
		if #config_name_keys > 0 then
			res, err = red:mget(unpack(config_name_keys))
			if err ~= nil or res == ngx.null then
				ngx.log(ngx.WARN, string.format("batch get config names failed, config_type is %s err is %s",config_type, err))
				return nil, nil, "batch get config names failed"
			end
		end
		
		for _, v in ipairs(res) do
			--更新ufc meta信息
			if config_type == "service" or config_type == "bns" then
				if not ngx_worker_cache["ufc_meta_info"] then
					ngx_worker_cache["ufc_meta_info"] = {}
				end

				if not ngx_worker_cache["ufc_meta_info"][config_type .. "s"] then
					ngx_worker_cache["ufc_meta_info"][config_type .. "s"] = {}
				end
				ngx_worker_cache["ufc_meta_info"][config_type .. "s"][v] = 1
			end
			table.insert(configs, v)
		end

		if stop == 1 then
			break
		end
	end

	last_index = 1
	--获取所有的mtime
	while true do
		local stop = 0
		local config_mtime_keys = {}
		for i = 1, batch_size do 
			if last_index > config_cnt then
				stop = 1
				break
			end
			local config_mtime_key = string.format("%s-%s-%s-mtime", prefix, config_type, configs[last_index])
			table.insert(config_mtime_keys, config_mtime_key)
			last_index = last_index + 1 
		end

		local res = {}
		if #config_mtime_keys > 0 then
			res, err = red:mget(unpack(config_mtime_keys))
			
			if err or res == ngx.null then
				ngx.log(ngx.WARN, string.format("batch get config mtimes failed, config_type is %s err is %s",config_type, err))
				return nil, nil, "batch get config mtimes failed"
			end
		end
		
		for _, v in ipairs(res) do
			table.insert(config_mtimes, v)
		end
		
		if stop == 1 then
			break
		end
	end

	--对比获取需要更新的config name
	local mtime_res = {}

	for i = 1, config_cnt do
		if _M._is_config_changed(red, config_type, configs[i], config_mtimes[i]) == true then
			table.insert(needed_update_config, configs[i])
			--把相应config 云端的mtime存起来，后面会用到
			mtime_res[configs[i]] = config_mtimes[i]
		end
		--配置名index信息写到共享内存, mtime在确定配置内容写入共享内存成功之后再写入
		if config_type ~= "idcmap" and config_type ~= "platformmap" and config_type ~= "platformidcmap" then
			shared_memory.set_config_name(config_type, i - 1, configs[i])
		end
	end
	
	return needed_update_config, mtime_res, nil
end

--对比config是否更新
function _M._is_config_changed(red, config_type, config_name, config_mtime)
	--特权进程配置更新不用考虑性能问题
	--没有不同数据更新频率不同这种策略
	local cached_config, cached_mtime, cached_service_config

	--如果相应的config content在共享内存里面没有就直接更新
	--在service配置更新小流量阶段或者service刚注册的时候如果ufc内存里面没有这个service会出现这种情况
	--会导致如果这个service mtime一直不更新，共享内存里面会缺数据，worker进程一直更新配置失败
	--除了config content之外其他数据在配置更新的时候都会再写一遍，不会有这种问题
	local config_content = shared_memory.get_config_content(config_type, config_name)
	--共享内存中idcmap 和bns数据是组装在一起的，后面也会有逻辑判断这个idcmap数据有没有，这里把idcmap 数据排除掉
	if config_content == nil and config_type ~= "idcmap" and config_type ~= "platformmap" and config_type ~= "platformidcmap" then
		ngx.log(ngx.WARN, string.format("config_type:%s, config_name:%s miss in shared_memory, force update", config_type, config_name))
		return true
	end

	--idcmap 和 bns有特殊结构单独判断
	--idcmap和bns数据在内存中也有，按原来的逻辑来就行

	if config_type == "idcmap" then
		local idcmap_config = ngx_worker_cache["static_cache"]["bnss"][config_name]
		if not idcmap_config or not idcmap_config["idcmap_mtime"] or idcmap_config["idcmap_mtime"] ~= config_mtime then
			--ngx.log(ngx.DEBUG, "idcmap config is update, bns is "..config_name)
			return true
		end

		if not idcmap_config["ip2idc"] then
			ngx.log(ngx.WARN, "idcmap config force update, bns is "..config_name)
			--新上线数据结构，强制更新
			return true
		end

		return false
	end

	if config_type == "platformmap" then
		local platformmap_config = ngx_worker_cache["static_cache"]["bnss"][config_name]
		if not platformmap_config or not platformmap_config["platform_mtime"] or platformmap_config["platform_mtime"] ~= config_mtime then
			--ngx.log(ngx.DEBUG, "idcmap config is update, bns is "..config_name)
			return true
		end

		if not platformmap_config["ip2platform"] then
			ngx.log(ngx.WARN, "ip2platform config force update, bns is "..config_name)
			--新上线数据结构，强制更新
			return true
		end 

		return false
	end

	if config_type == "platformidcmap" then
		local platformmap_config = ngx_worker_cache["static_cache"]["bnss"][config_name]
		if not platformmap_config or not platformmap_config["platformidc_mtime"] or platformmap_config["platformidc_mtime"] ~= config_mtime then
			--ngx.log(ngx.DEBUG, "idcmap config is update, bns is "..config_name)
			return true
		end

		return false
	end

	if config_type == "bns" then	
		cached_service_config = ngx_worker_cache["static_cache"][config_type .. "s"][config_name]
		if not cached_service_config or not cached_service_config["backend_map"] then
			--新上线数据结构，强制更新
			return true
		end
	end

	--config不能用共享内存中的时间戳判断，特权进程需要用到里面的配置信息
	--如果特权进程挂掉，可能出现共享内存里面数据是新的，特权进程数据是老的情况，但是配置一直不更新
	if config_type == "config" then
		cached_config = ngx_worker_cache["static_cache"][config_type .. "s"][config_name]
		if not cached_config or cached_config["mtime"] ~= config_mtime then
			if gossip_utils.is_in_small_flow(config_type, config_name, red) == false then
				return false
			end
			return true
		end
	end

	--通用逻辑，判断云端本地mtime是否一样
	cached_mtime = shared_memory.get_config_mtime(config_type, config_name)

	if not cached_mtime or cached_mtime ~= config_mtime then
		--如果更新的是config或者service就判断一下本机是否在小流量列表里面
		--这个确定mtime变了之后才会走这个逻辑，减少和redis交互次数
		if config_type == "service" then
			if gossip_utils.is_in_small_flow(config_type, config_name, red) == false then
				return false
			end
		end
		return  true
	end

	return false
end


function _M._load_one_cloud_config(red, config_type, config_name, config_mtimes)
	--目前的更新策略，能够走到这里的只有bns和idcmap、platformmap和platformidcmap
	local config_decode, err
    config_decode, err = _M._real_load_one_cloud_config(red, config_type, config_name)
    if not config_decode or err then
        return conf["res"]["FAIL"]
    end
	
	--对于每种config_type处理方式均不同
	--这里即使某一个数据共享内存写入失败也不返回失败否则影响后面数据更新，等下次更新就好了
	if config_type == "idcmap" then
		_M.idcmap_content_handle(config_type, config_name, config_decode, config_mtimes)
	elseif config_type == "service" then
		_M.service_content_handle(config_type, config_name, config_decode, config_mtimes)
	elseif config_type == "bns" then
		_M.bns_content_handle(config_type, config_name, config_decode, config_mtimes)
	elseif config_type == "http_ip" or config_type == "stream_ip" then
		_M.ip_content_handle(config_type, config_name, config_decode, config_mtimes)
	elseif config_type == "platformmap" then
		_M.platformmap_content_handle(config_type, config_name, config_decode, config_mtimes)
	elseif config_type == "platformidcmap" then
		_M.paltformidcmap_content_handle(config_type, config_name, config_decode, config_mtimes)
	else 
		_M.config_content_handle(config_type, config_name, config_decode, config_mtimes)
	end
	return conf["res"]["SUCCESS"]
end

--处理从云端获取的配置，每种config_type处理方式不一样
function _M._cloud_config_content_handle(config_type, configs, configs_mtimes)
	for config_name, config_encode in pairs(configs) do
		local config_decode, err = cjson.decode(config_encode)
		if config_decode == nil or err ~= nil then
			ngx.log(ngx.WARN, string.format("config type %s, config name %s, decode json failed err is %s", config_type, config_name, err))
			return conf["res"]["FAIL"]
		end
		--对于每种config_type处理方式均不同
		--这里即使某一个数据共享内存写入失败也不返回失败，否则影响后面数据更新，等下次更新就好了
		if config_type == "idcmap" then
			_M.idcmap_content_handle(config_type, config_name, config_decode, configs_mtimes)
		elseif config_type == "service" then
			_M.service_content_handle(config_type, config_name, config_decode, configs_mtimes)
		elseif config_type == "bns" then
			_M.bns_content_handle(config_type, config_name, config_decode, configs_mtimes)
		elseif config_type == "http_ip" or config_type == "stream_ip" then
			_M.ip_content_handle(config_type, config_name, config_decode, configs_mtimes)
		elseif config_type == "platformmap" then
			_M.platformmap_content_handle(config_type, config_name, config_decode, configs_mtimes)
		elseif config_type == "platformidcmap" then
			_M.paltformidcmap_content_handle(config_type, config_name, config_decode, configs_mtimes)
		else 
			_M.config_content_handle(config_type, config_name, config_decode, configs_mtimes)
		end
	end
	return conf["res"]["SUCCESS"]
end

--特权进程只需要把http_ip和stream_ip的数据写到共享内存就行
--冲突检测的工作由worker进程在加载配置的时候做
--这个数据放一份到内存中（需要写磁盘）
function _M.ip_content_handle(config_type, config_name, config, config_mtimes)
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end
	config["mtime"] = config_mtimes[config_name]
	ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = config
	--确认配置内容写入共享内存成功之后才在共享内存中写入mtime，这样能保证共享内存中的配置内容永远比mtime更新
	--即使我们的代码里面有一些非预期的bug，也能保证经过多轮配置更新之后，共享内存中的配置数据是最新的
	if _M.save_config_in_shared_memmory(config_type, config_name, config) then
		shared_memory.set_config_mtime(config_type, config_name, config_mtimes[config_name])
	else
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--从动态生成的映射信息中删除某些key
--因为动态生成某些映射关系之后可能又会在云端重复分配导致冲突,需要云端更新的时候做一下判断
function _M.delete_ip_mapping(key, stream)
	--先把内存里面的数据删掉
	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, "ngx_worker_cache[static_cache] is nil")
		return false
	end

	local http_ips = static_cache["http_ips"]
	if not http_ips then
		ngx.log(ngx.WARN, "ngx_worker_cache[static_cache][http_ips] is nil")
		return false
	end

	local stream_ips = static_cache["stream_ips"]
	if not stream_ips then
		ngx.log(ngx.WARN, "ngx_worker_cache[static_cache][stream_ips] is nil")
		return false
	end
	local ip = nil
	local service = nil
	--stream等于1，key是ip，等于0key是from_service|to_service
	if stream == 1 then
		ip = key
		if stream_ips[ip] and stream_ips[ip]["content"] then
			service = stream_ips[ip]["content"]
		end
	else
		service = key
		if http_ips[service] and http_ips[service]["content"] then
			ip = http_ips[service]["content"] 
		end
	end

	if ip == nil or service == nil then
		return false
	end

	--先删除stream模块的映射
	stream_ips[ip] = nil
	--http模块的映射正好是相反的
	http_ips[service] = nil

	--从共享内存里面把映射关系删掉
	local shared_memory_name
	local shared_ip_data 
	if conf["stream"] == 0 then
		shared_memory_name = "http_ip"
		shared_ip_data = ngx.shared.http_ip
	else
		shared_memory_name = "stream_ip"
		shared_ip_data = ngx.shared.stream_ip
	end
	
	local ip_str = shared_ip_data:get(shared_memory_name)
	local ip_json = {}
	local err = nil
	if ip_str ~= nil then
		ip_json, err = cjson.decode(ip_str)
	end

	if err ~= nil then
		ip_json = {}
	end

	if not ip_json["http_ips"] then
		ip_json["http_ips"] = {}
	end

	if not ip_json["stream_ips"] then
		ip_json["stream_ips"] = {}
	end

	ip_json["stream_ips"][ip] = nil
	ip_json["http_ips"][service] = nil

	ip_str = cjson.encode(ip_json)
	shared_ip_data:set(shared_memory_name, ip_str)
	return true
end

function _M.paltformidcmap_content_handle(config_type, config_name, config, configs_mtimes) 
	local old_bns_config = ngx_worker_cache["static_cache"]["bnss"][config_name]
    --如果在配置更新期间注册了带idcmap的service idc_maps 有可能为nil
    if old_bns_config == nil then
        return conf["res"]["SUCCESS"]
    end

	local platformidc_maps = {}
	for key, value in pairs(old_bns_config) do
		platformidc_maps[key] = value
	end 

	platformidc_maps["platformidc_mtime"] = configs_mtimes[config_name]

	for platform, items in pairs(config) do
		local nodes = {}
		for _, backend in ipairs(items) do
			local host, port = utils.split_backend(backend)
			table.insert(nodes, {["host"] = host, ["port"] = port})
		end
		platformidc_maps[platform] = nodes
	end
	--用 bns、idcmap、 platform_map 合并的数据覆盖原来的老数据
	ngx_worker_cache["static_cache"]["bnss"][config_name] = platformidc_maps

	--加了idcmap信息的bns数据写到共享内存和磁盘上
	ngx_worker_cache.save_item_local("bns", config_name, platformidc_maps, _M["cache_table"])
	if _M.save_config_in_shared_memmory("bns", config_name, platformidc_maps) then
		shared_memory.set_config_mtime(config_type, config_name, configs_mtimes[config_name])
	else
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end

	return conf["res"]["SUCCESS"]

end


-- 数据内容包含 platform_map 字段
function _M.platformmap_content_handle(config_type, config_name, config, configs_mtimes)
    local old_bns_config = ngx_worker_cache["static_cache"]["bnss"][config_name]
    --如果在配置更新期间注册了带idcmap的service idc_maps 有可能为nil
    if old_bns_config == nil then
        return conf["res"]["SUCCESS"]
    end

	local platform_maps = {}
	for key, value in pairs(old_bns_config) do
		platform_maps[key] = value
	end

	platform_maps["ip2platform"] = {}
	platform_maps["platform_mtime"] = configs_mtimes[config_name]

	for platform, items in pairs(config) do
		local nodes = {}
		for _, backend in ipairs(items) do
			local host, port = utils.split_backend(backend)
			table.insert(nodes, {["host"] = host, ["port"] = port})
			platform_maps["ip2platform"][backend] = platform
		end
		platform_maps[platform] = nodes
	end
	--用bns、idcmap、 platform_map合并的数据覆盖原来的老数据
	ngx_worker_cache["static_cache"]["bnss"][config_name] = platform_maps

	--加了platformmap信息的bns数据写到共享内存和磁盘上
	ngx_worker_cache.save_item_local("bns", config_name, platform_maps, _M["cache_table"])
	if _M.save_config_in_shared_memmory("bns", config_name, platform_maps) then
		
		shared_memory.set_config_mtime(config_type, config_name, configs_mtimes[config_name])
	else
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end

	return conf["res"]["SUCCESS"]
end

function _M.idcmap_content_handle(config_type, config_name, config, configs_mtimes)
    local idc_maps_temp = ngx_worker_cache["static_cache"]["bnss"][config_name]
    --如果在配置更新期间注册了带idcmap的service idc_maps 有可能为nil
    if idc_maps_temp == nil then
        return conf["res"]["SUCCESS"]
    end

	local idc_maps = {}
	--先把bns独有的数据复制过来
	idc_maps["bns_nodes"] = idc_maps_temp["bns_nodes"]
	idc_maps["mtime"] = idc_maps_temp["mtime"]
	idc_maps["backend_map"] = idc_maps_temp["backend_map"]
	--添加idcmap相关数据
	idc_maps["idcmap_mtime"] = configs_mtimes[config_name]
	idc_maps["ip2idc"] = {} 
	for idc, items in pairs(config) do
		local nodes = {}
		for _, backend in ipairs(items) do
			local host, port = utils.split_backend(backend)
			table.insert(nodes, {["host"] = host, ["port"] = port})
			idc_maps["ip2idc"][backend] = idc
		end
		idc_maps[idc] = nodes
	end
	--覆盖原来的老数据
	ngx_worker_cache["static_cache"]["bnss"][config_name] = idc_maps

	--加了idcmap信息的bns数据写到共享内存和磁盘上
	ngx_worker_cache.save_item_local("bns", config_name, idc_maps, _M["cache_table"])
	if _M.save_config_in_shared_memmory("bns", config_name, idc_maps) then
		shared_memory.set_config_mtime(config_type, config_name, configs_mtimes[config_name])
	else
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--bns数据需要放一份在static_cache[bnss]中
--如果这个bns有idcmap配置会用到
function _M.bns_content_handle(config_type, config_name, config, configs_mtimes)
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end

	if not ngx_worker_cache["static_cache"][config_type .. "s"][config_name] then
		ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = {}
	end
	
	--这里直接把从redis获取的配置写到共享内存中会导致worker进程有概率拿到没有idcmap信息的bns数据
	--如果worker进程从共享内存中拿配置发生在特权进程写bns和写idcmap配置直接就会触发bug
	--现在这种处理方式仍有一定概率导致worker进程在两次配置更新间隔之间拿到的数据idcmap和bns不完全匹配
	--但是这个根因是ufc 把两种数据分开储存，也不会导致bug，可以等同为配置更新延迟
	local res = _M._deal_config_if_necessary(config, config_type, config_name)
	local bns_data = ngx_worker_cache["static_cache"][config_type .. "s"][config_name]
	bns_data["mtime"] = configs_mtimes[config_name]
	bns_data["bns_nodes"] = res["bns_nodes"]
	bns_data["backend_map"] = res["backend_map"]

	--存到磁盘上和共享内存中
	ngx_worker_cache.save_item_local(config_type, config_name, bns_data, _M["cache_table"])
	--确认配置内容写入共享内存成功之后才在共享内存中写入mtime，这样能保证共享内存中的配置内容永远比mtime更新
	--即使我们的代码里面有一些非预期的bug，也能保证经过多轮配置更新之后，共享内存中的配置数据是最新的
	if _M.save_config_in_shared_memmory(config_type, config_name, bns_data) then
		shared_memory.set_config_mtime(config_type, config_name, configs_mtimes[config_name])
	else 
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--service数据啥都不用管，处理好之后写到磁盘和共享内存就行了
function _M.service_content_handle(config_type, config_name, config, configs_mtimes)
	config["mtime"] = configs_mtimes[config_name]
	_M._set_default_for_config(config, config_type)
	--这个处理bns_list中的auto的逻辑之前在_M._init_static_cache()里面
	--放在这里好一点
	_M._set_service_auto_bns_percent(config)

	if not _M._is_config_legal(config, config_type) then
		--service配置由ufc admin写入理论上不可能非法，即使出现非法的情况打个日志就行了，仍然返回success
		--否则在手动处理之前，后面的配置都更新不了
		ngx.log(ngx.WARN, string.format("%s %s config illegal, skip it", config_type, config_name))
		return conf["res"]["SUCCESS"]
	end

	ngx_worker_cache.save_item_local(config_type, config_name, config, _M["cache_table"])
	--确认配置内容写入共享内存成功之后才在共享内存中写入mtime，这样能保证共享内存中的配置内容永远比mtime更新
	--即使我们的代码里面有一些非预期的bug，也能保证经过多轮配置更新之后，共享内存中的配置数据是最新的
	if _M.save_config_in_shared_memmory(config_type, config_name, config) then
		shared_memory.set_config_mtime(config_type, config_name, configs_mtimes[config_name])
	else
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

--configs数据需要放一份在static_cache[configs]中
--获取定时器配置需要用到这个
function _M.config_content_handle(config_type, config_name, config, config_mtimes)
	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end

	config["mtime"] = config_mtimes[config_name]
	ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = config
	--确认配置内容写入共享内存成功之后才在共享内存中写入mtime，这样能保证共享内存中的配置内容永远比mtime更新
	--即使我们的代码里面有一些非预期的bug，也能保证经过多轮配置更新之后，共享内存中的配置数据是最新的
	if _M.save_config_in_shared_memmory(config_type, config_name, config) then
		shared_memory.set_config_mtime(config_type, config_name, config_mtimes[config_name])
	else
		ngx.log(ngx.WARN, string.format("%s %s, write config content to shared memmory failed", config_type, config_name))
		return conf["res"]["FAIL"]
	end
	return conf["res"]["SUCCESS"]
end

function _M._is_config_legal(config, config_type)
    if config_type ~= "service" then
        return true
    end

    if type(config) ~= type({}) then
        return false
    end

    for k, v in pairs(config) do
        if not conf["legal_config_format"][k] then
            goto done
        end
        if conf["legal_config_format"][k](v) == false then
            return false
        end
        :: done ::
    end
    return true
end

function _M._real_load_one_cloud_config(red, config_type, config_name)
    local err
    local prefix
    local config_key
    local config_encode, config
    prefix = conf.get_redis_prefix()

    config_key = string.format("%s-%s-%s", prefix, config_type, config_name)
    config_encode, err = red:get(config_key)
    if err or config_encode == ngx.null then
        ngx.log(ngx.WARN, string.format("%s conf miss, err is %s, index is %s",
            config_type, err, config_key))
        return nil, err or "config null"
    end

    config, err = cjson.decode(config_encode)
    if not config or err then
        return nil, err or "decode fail"
    end
    return config, nil
end

function _M._set_default_for_config(config, config_type)
    --ngx.log(ngx.DEBUG, string.format("before set default, config is %s", cjson.encode(config)))
    if config_type ~= "service" then
        return
    end

    local defaults = conf["defaults"]
    for k, v in pairs(defaults) do
        if not config[k] then
            config[k] = v
        end
    end
    if config["small_flow"] then
        for i,v in pairs(config["small_flow"]) do
            _M._set_default_for_config(v, config_type)
        end
    end
    --ngx.log(ngx.DEBUG, string.format("after set default, config is %s", cjson.encode(config)))
end

function _M._deal_config_if_necessary(config, config_type, config_name)
    if config_type == "service" then
        return config
    end

    if config_type == "bns" then
        local tmp_service = {
            ["bns_nodes"] = {},
			["backend_map"] = {},
        }

        for _, backend in ipairs(config) do
            local host, port = utils.split_backend(backend)
            table.insert(tmp_service["bns_nodes"], {["host"] = host, ["port"] = port})
			tmp_service["backend_map"][backend] = 1
        end

        return tmp_service
    end
end

--获取batch size， 先从redis里面拿，redis里面拿不到从conf里面拿
function _M._get_batch_size(red)
	local prefix = conf.get_redis_prefix()
	local batch_size_key = string.format("%s-batch_size", prefix)
	local is_exist, err = red:exists(batch_size_key)
	if err or is_exist == ngx.null or is_exist == 0 then
        --batch size key不存在，用本地的
        ngx.log(ngx.WARN, string.format("batch size is not exit in cloud storage"))
		return conf["batch_size"]
	end

	local batch_size_str, batch_size_num
	batch_size_str, err = red:get(batch_size_key)
	if err then
        --读redis出错返回本地的
        ngx.log(ngx.WARN, string.format("get %s failed, error is %s", batch_size_key, err))
		return conf["batch_size"]
	end
	
	batch_size_num = tonumber(batch_size_str)
    if batch_size_num == nil then
        ngx.log(ngx.WARN, string.format("batch size %s in cloud storage is not an number", batch_size_str))
		return conf["batch_size"]
	end
	return batch_size_num
end

--把处理好的配置信息写到共享内存中去
function _M.save_config_in_shared_memmory(config_type, config_name, config_content)
	return shared_memory.set_static_data(config_type, config_name, cjson.encode(config_content))
end

--处理service bns_list 中的auto配置
function _M._set_service_auto_bns_percent(service_config)
    if service_config["bns_list"] then
        _M._set_auto_bns_percent(service_config["bns_list"])
    end
    if service_config["small_flow"] then
        for _, small_flow_config in pairs(service_config["small_flow"]) do
            if small_flow_config["bns_list"] then
                _M._set_auto_bns_percent(small_flow_config["bns_list"])
            end
        end
    end
end

function _M._set_auto_bns_percent(config)
    --ngx.log(ngx.DEBUG, string.format("load config, before set auto percent, config is %s", cjson.encode(config)))
	-- 所有auto的bns 有多少实例
    local all_auto_backends = 0
	-- 手动指定实例百分比总数
    local manual_percent = 0
    local real_percent = {}
    local all_real_bns_percent = 0
	-- 还剩下百分比多少给auto指定的bns做分配
    local more_percent = 0

    for bns, percent in pairs(config) do
        --ngx.log(ngx.DEBUG, string.format("load config, bns %s percent is %s", bns, percent))
        if tonumber(percent) then
            manual_percent = manual_percent + tonumber(percent)
        end

        if percent == "auto" then
			if not ngx_worker_cache["static_cache"]["bnss"] or not ngx_worker_cache["static_cache"]["bnss"][bns] then
				-- bns数据没有把这个bns当0%处理
				goto continue
			end
            local bns_config = ngx_worker_cache["static_cache"]["bnss"][bns]
            if bns_config["bns_nodes"] then
                all_auto_backends = all_auto_backends + #bns_config["bns_nodes"]
            end
			:: continue ::
        end
    end
	more_percent = 100 - manual_percent
	if more_percent <= 0 then
		--防止配置百分比搞错，手动指定的加起来超过100
		more_percent = 0
	end
    --ngx.log(ngx.DEBUG, string.format("load config, manual_percent is %s, all_auto_backends is %s", manual_percent, all_auto_backends))

    if all_auto_backends == 0 then
		-- 对all_auto_backends等于0的情况做处理，auto的可能都没有实例
        for bns, percent in pairs(config) do
            if percent == "auto" then
                config[bns] = 0
            end
        end
    else
        for bns, percent in pairs(config) do
            if percent == "auto" then
                local bns_config = ngx_worker_cache["static_cache"]["bnss"][bns]
				if not ngx_worker_cache["static_cache"]["bnss"] or not ngx_worker_cache["static_cache"]["bnss"][bns] then
					-- bns数据没有把这个bns当0%处理
					config[bns] = 0
					goto continue
				end
                if bns_config and bns_config["bns_nodes"] then
                    local real_bns_percent = math.floor(#bns_config["bns_nodes"] * more_percent / all_auto_backends)
                    config[bns] = real_bns_percent
                    --ngx.log(ngx.DEBUG, string.format("load config, bns %s real percent is %s", bns, real_bns_percent))
                end
				:: continue ::
            end
        end
    end
    --ngx.log(ngx.DEBUG, string.format("load config, at last, real bns percent is %s", cjson.encode(config)))
end



return _M