local ngx = ngx
local cjson = require "cjson.safe"

local _M = {
	["doc"] = "shared memmory operate function",
	--共享内存写满错误，每5s钟打一次日志
	["error_log_interval"] = 5,
	["dynamic_last_log_time"] = nil,
	["static_last_log_time"] = nil,
	["response_time_zone"] = "dynamic_cache-backend-response_times",
	["forbid_backends_zone"] = "dynamic_cache-forbid_backends",
	["zone"] = {"static_cache",
				"static_cache-services",
				"static_cache-bnss",
				"static_cache-idcmaps",
				"static_cache-platformmaps",
				"static_cache-platformidcmaps",
				"static_cache-configs",
				"static_cache-http_ips",
				"static_cache-stream_ips",
				"dynamic_cache",
				"dynamic_cache-service",
				"dynamic_cache-moniter",
				"dynamic_cache-backend",
				"dynamic_cache-breaker",
				"dynamic_cache-forbid_backends",
				"dynamic_cache-backend-response_times",				
				},
	["check_interval"] = 60,
	["upload_moniter_interval"] = 60,
	["alarm_threshold"] = 95,
	["backend_keys"] = {
						"request_cnt",
						"success_cnt",
						"fail_cnt",
						"last_mid_response_times",
						"first_request_time",
						"1xx",
						"2xx",
						"3xx",
						"4xx",
						"5xx",
						"other_status",
						"ratio_request_cnt",
						"last_ratio_request_cnt",
						"last_1xx",
						"last_2xx",
						"last_3xx",
						"last_4xx",
						"last_5xx",
						"last_other_status",
						"forbidden",
						"ctime",

					},
	-- 监控数据上报，为了解决循环依赖，把该字段放到这里
	["no_memory_cache"] = {}
}

function _M.check_shared_memmory()
	ngx.log(ngx.NOTICE, "check shared memmory begain")
	for _, zone in ipairs(_M["zone"]) do
		local zone_data = ngx.shared[zone]
		local capacity = zone_data:capacity()
		local free_space = zone_data:free_space()
		--超过80%报警
		if ((capacity - free_space) / capacity ) * 100 > _M["alarm_threshold"] then
			_M["no_memory_cache"][zone] = 1
			ngx.log(ngx.WARN, string.format("stack traceback zone %s free space is too low, capacity %dM, free_space %dM", zone, capacity/1048576, free_space/1048576))
		else
		--就算没超过阈值也打日志
			ngx.log(ngx.NOTICE, string.format("zone %s is ok, capacity %dM, free_space %dM", zone, capacity/1048576, free_space/1048576))
		end
	end
	ngx.log(ngx.NOTICE, "check shared memmory end")
end



--错误日志频控，默认5s钟一次
function _M.dynamic_err_log(zone, key, value, err)
	if not _M["dynamic_last_log_time"] then
		_M["no_memory_cache"][zone] = 1
		ngx.log(ngx.WARN, string.format("stack traceback zone:%s key:%s value:%s error:%s, set dynamic_cache failed"
		, zone, key, value, err))
		_M["dynamic_last_log_time"] = ngx.now()
		return
	end
	local now = ngx.now()
	if now - _M["dynamic_last_log_time"] > _M["error_log_interval"] then
		_M["no_memory_cache"][zone] = 1
		ngx.log(ngx.WARN, string.format("stack traceback zone:%s key:%s value:%s error:%s, set dynamic_cache failed"
		, zone, key, value, err))
		_M["dynamic_last_log_time"] = now
		return
	end
end

function _M.static_err_log(zone, key, value, err)
	if not _M["static_last_log_time"] then
		_M["no_memory_cache"][zone] = 1
		ngx.log(ngx.WARN, string.format("stack traceback zone:%s key:%s value:%s error:%s, set static_cache failed"
		, zone, key, value, err))
		_M["static_last_log_time"] = ngx.now()
		return
	end
	local now = ngx.now()
	if now - _M["static_last_log_time"] > _M["error_log_interval"] then
		_M["no_memory_cache"][zone] = 1
		ngx.log(ngx.WARN, string.format("stack traceback zone:%s key:%s value:%s error:%s, set static_cache failed"
		, zone, key, value, err))
		_M["static_last_log_time"] = now
		return
	end
end

--对于动态数据的插入用可以淘汰的set，并且设置过期时间
function _M.dynamic_data_set(zone, key, value, exptime)
	local zone_data = ngx.shared[zone]
	local ok, err, forcible = zone_data:set(key, value, exptime)
	if forcible then
		--删除了一个没有过期的key
		--这个错误不需要做频控
		ngx.log(ngx.WARN, string.format("stack traceback zone:%s key:%s value:%s, delete a not-yet-expired key"
		,zone, key, value))
	end
	--不管是啥错误这里都要报警
	if not ok then
		_M.dynamic_err_log(zone, key, value, err)
	end
end

--静态数据永远不会过期，也不会淘汰
function _M.static_data_set(zone, key, value)
	local zone_data = ngx.shared[zone]
	local ok, err = zone_data:safe_set(key, value)
	--不管是啥错误这里都要报警
	if not ok then
		_M.static_err_log(zone, key, value, err)
	end
	return ok
end

function _M.get(zone, key)
	local zone_data = ngx.shared[zone]
	--get永远不会失败
	return zone_data:get(key)
end


function _M.set_static_data(config_type, key, value)
	local zone = "static_cache-" .. config_type .. "s"
	return _M.static_data_set(zone, key, value)
end

function _M.get_static_data(config_type, key)
	local zone = "static_cache-" .. config_type .. "s"
	local zone_data = ngx.shared[zone]
	return zone_data:get(key)
end

function _M.get_global_mtime()
	return _M.get("static_cache", "mtime")
end

-- 获取 特权进程 mtime 时间戳
function _M.get_global_mtime_update_timestamp()
	return _M.get("static_cache", "mtime_update_timestamp")
end 

-- 设置 特权进程更新 mtime 时的时间戳，供获取 worker_latency 
function _M.set_global_mtime_update_timestamp(timestamp)
	return _M.static_data_set("static_cache", "mtime_update_timestamp", timestamp)
end

function _M.get_privilged_latency()
	return _M.get("static_cache", "privilged_latency")
end 


function _M.set_privileged_latency(privileged_latency)
	return _M.static_data_set("static_cache", "privilged_latency", privileged_latency)
end 

-- 获取 worker 进程配置更新延迟时间
function _M.get_worker_latency()
	return _M.get("static_cache", "worker_latency")
end 

-- 设置 worker 进程配置更新延迟时间
function _M.set_worker_latency(worker_latency)	
	return _M.static_data_set("static_cache", "worker_latency", worker_latency)
end

function _M.get_config_cnt(config_type)
	return _M.get_static_data(config_type, "cnt")
end

function _M.get_config_name(config_type, index)
	return _M.get_static_data(config_type, index)
end

function _M.get_config_content(config_type, config_name)
	return _M.get_static_data(config_type, config_name)
end

function _M.get_config_mtime(config_type, config_name)
	return _M.get_static_data(config_type, config_name .. "-mtime")
end


function _M.set_global_mtime(mtime)
	return _M.static_data_set("static_cache", "mtime", mtime)
end

function _M.set_config_cnt(config_type, cnt)
	return _M.set_static_data(config_type, "cnt", cnt)
end

function _M.set_config_name(config_type, index, config_name)
	return _M.set_static_data(config_type, index, config_name)
end

function _M.set_config_content(config_type, config_name, config_content)
	return _M.set_static_data(config_type, config_name, config_content)
end

function _M.set_config_mtime(config_type, config_name, mtime)
	return _M.set_static_data(config_type, config_name .. "-mtime", mtime)
end

function _M.insert_backend_response_time(to_service, backend, response_time)
	local zone = _M["response_time_zone"]
	local zone_data = ngx.shared[zone]
	local key = string.format("%s&backend&%s&response_times", to_service, backend)
	local length ,err = zone_data:lpush(key, response_time)
	if err  then
		_M.dynamic_err_log(zone, key, response_time, err)
	end
end

function _M.get_backend_response_time(to_service, backend)
	local zone = _M["response_time_zone"]
	local zone_data = ngx.shared[zone]
	local key = string.format("%s&backend&%s&response_times", to_service, backend)
	local len, err = zone_data:llen(key)
	if len == 0 then
		return nil, 0
	end
	local res = {}
	for i = 1,len do
		local v = zone_data:lpop(key)
		table.insert(res, v)
	end
	return res, len
end

function _M.clear_backend_response_time(to_service, backend)
	local zone = _M["response_time_zone"]
	local zone_data = ngx.shared[zone]
	local key = string.format("%s&backend&%s&response_times", to_service, backend)
	--delete不会失败
	zone_data:delete(key)
end

--worker进程触发单机封禁的时候把封禁实例信息告诉特权进程
function _M.append_node(ufc_service_name, bns, forbid_time, check_time, backend, forbid_timeout, idc, check_ok_times)
	local zone = "dynamic_cache-forbid_backends"
	local zone_data = ngx.shared[zone]
	local data = string.format("%s&%s&%s&%s&%s&%s&%s&%s", ufc_service_name, bns,
	forbid_time, check_time, backend, forbid_timeout, idc, check_ok_times)
	local length, err = zone_data:lpush("forbid_backends", data)
	if err  then
		_M.dynamic_err_log(zone, "forbid_backends", data, err)
	end
end

--特权进程在遍历封禁链表的时候将封禁实例信息取出来
function _M.get_forbid_backends()
	local zone = "dynamic_cache-forbid_backends"
	local zone_data = ngx.shared[zone]
	local len, err = zone_data:llen("forbid_backends")
	if len == 0 then
		return nil, 0
	end
	local res = {}
	for i = 1,len do
		-- pop 
		local v = zone_data:lpop("forbid_backends")
		table.insert(res, v)
	end
	return res, len
end

function _M.delete_invalid_backend_data(service, backend, bns_list)
	--每个后端在共享内存有哪些数据key是固定的,删掉它们就行
	local zone_data = ngx.shared["dynamic_cache-backend"]
	for _, v in ipairs(_M["backend_keys"]) do
		local key = string.format("%s&backend&%s&%s", service, backend, v)
		zone_data:delete(key)
	end
	local key = string.format("%s&backend&%s", service, backend)
	zone_data:delete(key)
	--删除封禁相关数据
	for _, bns in ipairs(bns_list) do
		local key = string.format("%s&backend&%s&forbidden&%s", service, backend, bns)
		zone_data:delete(key)
	end
end


function _M.delete_invalid_table(table)
	local zone = table["zone"]
	local key = table["shared_memmory_key"]
	local zone_data = ngx.shared[zone]
	zone_data:delete(key)
end

--对于一些嵌套特别深的table操作使用metatable特别耗性能
--此时用这个函数代替
function _M.get_dynamic_data5(v1,v2,v3,v4,v5)
	local zone = "dynamic_cache"

	if v2 == "service" then
		zone = "dynamic_cache-service"
	end
	if v2 == "backend" then
		zone = "dynamic_cache-backend"

	end
	if v2 == "breaker" then
		zone = "dynamic_cache-breaker"
	end
	local key = v1 .. "&".. v2 .. "&".. v3 .. "&".. v4 .. "&" .. v5
	return _M.get(zone, key)
end

function _M.get_dynamic_data6(v1,v2,v3,v4,v5,v6)
	local zone = "dynamic_cache"

	if v2 == "service" then
		zone = "dynamic_cache-service"
	end
	if v2 == "backend" then
		zone = "dynamic_cache-backend"
	end
	if v2 == "breaker" then
		zone = "dynamic_cache-breaker" -- 服务全局熔断
	end
	local key = v1 .. "&".. v2 .. "&".. v3 .. "&".. v4 .. "&" .. v5 .. "&" .. v6
	return _M.get(zone, key)
end

function _M.add_backend_cnt(v1, v2, v3, v4, value)
	local zone = "dynamic_cache-backend"
	local key = v1 .. "&".. v2 .. "&".. v3 .. "&".. v4
	--init参数是0，表示key没有直接置0
	ngx.shared[zone]:incr(key, value, 0)
end


function _M.add_service_cnt(v1, v2, v3, v4, v5, value)
	local zone = "dynamic_cache-service"
	local key = v1 .. "&".. v2 .. "&".. v3 .. "&".. v4 .. "&".. v5
	--init参数是0，表示key没有直接置0
	ngx.shared[zone]:incr(key, value, 0)
end



-- 监控数据上报, 需要注意及时清理内存，每次上报后必须清空
function _M.merge_moniter_data(new_uploader_data)
	local zone = "dynamic_cache-moniter"
	local key = "uploader_data" 
	local ori_uploader_data_encode = _M.get(zone, key)
	if not ori_uploader_data_encode then 
		local new_uploader_data_encode = cjson.encode(new_uploader_data)
		_M.dynamic_data_set(zone, key, new_uploader_data_encode, 0)
		return 
	end 

	local ori_uploader_data, err = cjson.decode(ori_uploader_data_encode)
	if err ~= nil then 
		local new_uploader_data_encode = cjson.encode(new_uploader_data)
		_M.dynamic_data_set(zone, key, new_uploader_data_encode, 0)
		return 
	end 

	-- 初始化相关的数据结构，防止 traceback
	if not ori_uploader_data["forbid"] then 
		ori_uploader_data["forbid"] = {}						
	end 
	if not ori_uploader_data["forbid"]["backend"] then
		ori_uploader_data["forbid"]["backend"] = {}
	end 
	if not ori_uploader_data["forbid"]["service"] then
		ori_uploader_data["forbid"]["service"] = {}
	end 

	-- merge 上述数据

	if not ori_uploader_data["bug"] then
		ori_uploader_data["bug"] = {}
	end 
	if not ori_uploader_data["bug"]["service"] then
		ori_uploader_data["bug"]["service"] = {}
	end 
	if not ori_uploader_data["bp_hash_err_service"] then 
		ori_uploader_data["bp_hash_err_service"] = {}
	end 
	if not ori_uploader_data["invalid_timer"] then 
		ori_uploader_data["invalid_timer"] = {}
	end

	if new_uploader_data["forbid"] then 
		if new_uploader_data["forbid"]["backend"] and next(new_uploader_data["forbid"]["backend"]) then 
			for _, v in ipairs(new_uploader_data["forbid"]["backend"]) do 
				table.insert(ori_uploader_data["forbid"]["backend"], v)
			end 
		end 
	
		if new_uploader_data["forbid"]["service"] and next(new_uploader_data["forbid"]["service"]) then 
			for _, v in ipairs(new_uploader_data["forbid"]["service"]) do 
				table.insert(ori_uploader_data["forbid"]["service"], v)
			end 
		end 
	end 
	
	if new_uploader_data["bug"] and new_uploader_data["bug"]["service"] and next(new_uploader_data["bug"]["service"])then 
		for _, v in ipairs(new_uploader_data["bug"]["service"]) do 
			table.insert(ori_uploader_data["bug"]["service"], v)
		end 
	end 
	
	-- 合并 warn_list
	if new_uploader_data["warn_list"] and next(new_uploader_data["warn_list"]) then 
		local ori_warn_list = ori_uploader_data["warn_list"] or {}
		for action, v in pairs(new_uploader_data["warn_list"]) do 
			if not ori_warn_list[action] then
				ori_warn_list[action] = {}
			end
			table.insert(ori_warn_list[action], v)
		end
		ori_uploader_data["warn_list"] = ori_warn_list
	end 

	if new_uploader_data["invalid_timer"] and next(new_uploader_data["invalid_timer"]) then 
		for _, v in pairs(new_uploader_data["invalid_timer"]) do 
			table.insert(ori_uploader_data["invalid_timer"], v)
		end 
	end 

	if new_uploader_data["ufc_version"] then
		-- 不同 worker 进程的版本号是一致的，直接用传入的覆盖即可
		ori_uploader_data["ufc_version"] = new_uploader_data["ufc_version"]
	end

	ori_uploader_data_encode = cjson.encode(ori_uploader_data)
	_M.dynamic_data_set(zone, key, ori_uploader_data_encode, 0)		

end 


function _M.get_moniter_data()
	local zone = "dynamic_cache-moniter"
	local key = "uploader_data"
	local value = _M.get(zone, key)
	if not value then
		return nil 
	end
	local uploader_data, err = cjson.decode(value)
	if err ~= nil then
		ngx.log(ngx.WARN, string.format("get_moniter_data failed, key: %s, value: %s decode error: %s",key, value, err))
		return nil 
	end 
	return uploader_data
end 

function _M. clear_moniter_data()
	local zone = "dynamic_cache-moniter"
	local key = "uploader_data"

	-- 删除共享内存的数据
	local zone_data = ngx.shared[zone]
	zone_data:delete(key)

	-- 清空本模块内存的数据
	_M["no_memory_cache"] = {}
end 

return _M