local math = require "math"
local balancer = require "ngx.balancer"

local dynamic_conf = require "dynamic_conf"
local conf = require "conf"
local utils = require "utils"
local ngx_worker_cache = require "ngx_worker_cache"

local _M = {
	["doc"] = "main flow process for ufc stream mode",
}


function _M.main()
	local ctxs = ngx.ctx.ufc_ctxs
	local start_time =  os.clock()

	local ufc_service_name = ctxs["ufc_service_name"]

	local headers = {}

	local service_meta
	local bns, host, port, backend
	local connect_timeout, send_timeout, read_timeout, try_times, is_divided

	if not ctxs["tries"] then
		ctxs["tries"] = 1
		ctxs["bnss"] = {}
		balancer.set_more_tries(conf["defaults"]["backends_max_tries"] + 1)
	else
		ctxs["tries"] = ctxs["tries"] + 1
	end

	local pass
	local is_bypass = 0
	service_meta, host, port, pass = dynamic_conf.select_backend(ufc_service_name, headers, is_bypass)

	if pass then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "service is lawine"))
		return conf.fail("E_DEGRADE_4XX")
	end

	if not service_meta then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "service_meta is null"))
		return conf.fail("E_NO_SERVICE")
	end

	if ctxs["tries"] > service_meta["backends_max_tries"] then
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s server %s message %s", logid, ufc_service_name, "no backup"))
		return conf.fail("E_NO_BACKEND")
	end

	bns = service_meta["bns"]
	if not host or not port then
		if host == nil then
			host = "-"
		end

		if port == nil then
			port = "-"
		end

		if not bns then
			bns = "-"
		end

		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s bns %s host %s port %s server %s message %s", logid, bns, host, port, ufc_service_name, "select backend faild"))
		return conf.fail("E_NO_BACKEND")
	end

	table.insert(ctxs["bnss"], bns)

	backend = host .. ":" .. port

	local platforms_idc = ctxs["idc"] or "-"
	dynamic_conf.request_before(ufc_service_name, bns, platforms_idc, backend, is_bypass)   -- 做一些计数处理

	local end_time = os.clock()
	ctxs["ufc_cost_time"] = math.floor((end_time - start_time) * 1000 * 1000) * 0.001

	local re, err = balancer.set_current_peer(host, port)
	if re ~= true and type(err) == "string" then 
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s backend %s  set_current_peer_error %s", logid, backend, err))
	end
	--由于header里面没有耗时，这个耗时是redis配置里面的
	connect_timeout, send_timeout, read_timeout, try_times, is_divided = dynamic_conf.get_service_timeout(ufc_service_name, bns, platforms_idc)
	if ctxs["tries"] ~= 1 then 
		local last_tries = ctxs["tries"] - 1
		ctxs["request_timestamp"][last_tries]["end"] = ngx.now() * 1000
		ctxs["request_timestamp"][last_tries]["cost_time"] = ctxs["request_timestamp"][last_tries]["end"] - ctxs["request_timestamp"][last_tries]["start"]
		if not ctxs["remain_timeout"] then 
			if is_divided then 
				-- 如果是通过 header 传递，每次请求时间会被平均，计算总时间需要相乘
				ctxs["remain_timeout"] = read_timeout * try_times	
			else 
				-- 没有通过 header 覆盖超时时间，此时 read_timeout 就是总超时，没有被均分
				ctxs["remain_timeout"] = read_timeout
			end
		end 

		ctxs["remain_timeout"] = ctxs["remain_timeout"] - ctxs["request_timestamp"][last_tries]["cost_time"]

		local remain_tries = try_times - ctxs["tries"] + 1
		local new_send_timeout = math.floor(ctxs["remain_timeout"] / remain_tries)
		local new_read_timeout = math.floor(ctxs["remain_timeout"] / remain_tries)

		-- 重试时重新分配超时时间
		local is_time_reset = false 
		if send_timeout < new_send_timeout then 
			send_timeout = new_send_timeout
			is_time_reset = true 
		end 
		if read_timeout < new_read_timeout then 
			read_timeout = new_read_timeout
			is_time_reset = true 
		end 
		if is_time_reset then 
			local logid = ngx.var.ufc_logid or "-"
			ngx.log(ngx.WARN, string.format("logid %s backend %s ufc %s is_time_reset %s send_timeout %d read_timeout %d", logid, backend, ufc_service_name,is_time_reset, send_timeout, read_timeout))
		end 

	end 

	re, err = balancer.set_timeouts(connect_timeout/1000.0, send_timeout/1000.0, read_timeout/1000.0)
	if re ~= true and type(err) == "string" then 
		local logid = ngx.var.ufc_logid or "-"
		ngx.log(ngx.WARN, string.format("logid %s backend %s balancer_set_timeouts_err %s", logid, backend, err))
	end
end

_M.main()
