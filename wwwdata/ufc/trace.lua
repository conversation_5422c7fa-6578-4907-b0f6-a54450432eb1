local conf = require "conf"
local ngx_worker_cache = require "ngx_worker_cache"

local _M = {
    ["doc"] = "log trace module"
}

--[[
    @comment 获取采集的数据 
    @param table logs
]]
function _M.get_trace_log(logid, callid, bypass, to_ip, from_service, to_service, cost_time, status, ufc_time)
	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return nil
	end

	if not configs["trace"] then
		return nil
	end

	local services = configs["trace"]["service"]
	if not services then
		services = {}
	end

	local service_list = services["list"]
	if services["enable"] and services["enable"] > 0 then
		if service_list and not service_list[to_service] then
			return nil
		end
	end

	local log = {}
	log["logid"] = logid
	log["callid"] = callid
	log["logtime"] = ufc_time
	log["bypass"] = bypass
	--log["from_ip"] = ngx.var.remote_addr
	log["to_ip"] = to_ip
	log["from_service"] = service_list[from_service] or from_service
	log["to_service"] = service_list[to_service] or to_service 
	log["cost_time"] = cost_time
	log["status"] = status
    
	return log
end

--[[
    @comment 本地cache存储 
]]
function _M.set_local_logs(log)
	if not log then
		return
	end

	local logid = log["logid"]
	if not logid then
		return
	end

	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return
	end

	if not configs["trace"] or not configs["trace"]["local"]then
		return
	end

	local config = configs["trace"]["local"]
	local idc = conf.get_local_location()
	if not config["enable"] or not config["enable"][idc] or config["enable"][idc] < 1 then
		ngx_worker_cache["log_trace"]["local"] = {}
		return
	end

	local slot_num = config["slot_num"] or 3
	local slot_quota = config["slot_quota"] or 10000
	local caches = ngx_worker_cache["log_trace"]["local"]

	if not caches["slot_offset"] then
		caches["slot_offset"] = 1 
	end

	if not caches["logs_offset"] then
		caches["logs_offset"] = 0
	end

	local index = caches["slot_offset"]

	if not caches["slot_"..index] then
		caches["slot_"..index] = {}
	end

	if caches["logs_offset"] >= slot_quota then
		index = index + 1
		caches["slot_offset"] = caches["slot_offset"] + 1
		if index > slot_num then
			index = 1
			caches["slot_offset"] = 1 
		end

		caches["logs_offset"] = 0
		caches["slot_"..index] = {} 
	end

	if not caches["slot_"..index][logid] then
		caches["slot_"..index][logid] = {}
	end

	table.insert(caches["slot_"..index][logid], log)
	caches["logs_offset"] = caches["logs_offset"] + 1
end

--[[
	@comment 是否是vip请求
]]
function _M.is_vip(logid, pos)
	if not logid then
		return false
	end

	if pos < 1 then
		pos = 1
	end

	logid = math.floor(logid / (2 ^ (pos-1)))
	if logid % 2 == 1 then
		return true
	end

	return false
end

--[[
    @comment 是否trace 样品 
]]
function _M.is_simple(logs)
	if not logs then
		return
	end

	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return
	end

	if not configs["trace"] or not configs["trace"]["cloud"]then
		return
	end

	local config = configs["trace"]["cloud"]
	local idc = conf.get_local_location()
	if not config["enable"] or not config["enable"][idc] or config["enable"][idc] < 1 then
		ngx_worker_cache["log_trace"]["cloud"] = {}
		return
	end

	local simple_config = config["default"]
	local key = config["simple_key"] or "logid"
	local content = logs[key]

	local vip_config = config["vip"]
	if vip_config and key == "logid" and vip_config["enable"] and vip_config["enable"] > 0 then
		local pos = vip_config["check_pos"] or 28
		if not _M.is_vip(content, pos) then
			return false
		end

		simple_config = vip_config
	elseif not simple_config or simple_config["enable"] < 1 then
		return false
	end
	
	local simple = simple_config["simple"] or "0"
	local s = simple_config["start"] or 0
	local e = simple_config["end"] or 1 

	if string.sub(content, s, e) == simple then
		return true
	end

	return false
end
 
--[[
    @comment 本地cache存储 
]]
function _M.set_cloud_logs(log)
	if not log then
		return
	end

	local configs = ngx_worker_cache["static_cache"]["configs"]
	if not configs then
		return
	end

	if not configs["trace"] or not configs["trace"]["cloud"]then
		return
	end

	local config = configs["trace"]["cloud"]
	local limit = config["quota"] or 1000
	local caches = ngx_worker_cache["log_trace"]["cloud"]

	if #caches >= limit then
		return
	end

	table.insert(caches,log)
end

return _M
