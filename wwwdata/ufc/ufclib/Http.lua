--[[
=============================================================================
     FileName: Http.lua
       Author: lilei12
        Email: <EMAIL>
         Time: 2014-09-22 16:15:44
  Description: Encapsulate HTTP Request
=============================================================================
--]]

-- ngx func
local tcp = ngx.socket.tcp
local encode_args = ngx.encode_args

--lua func
local pairs = pairs
local string = string
local tonumber = tonumber
local table = table
local type = type
local print = print

--local CLog = require("pomsui.lib.CLog")

module(...)

local mt = { __index = _M }

_VERSION = '1.0'

--http method
local METHOD_GET  = "GET"
local METHOD_PUT  = "PUT"
local METHOD_POST = "POST"
local METHOD_HEAD = "HEAD"
local METHOD_DELETE = "DELETE"

local RETRY = "_RETRY_"
local REQ_ERROR = 8

--read response body max size 21mb
local MAX_BODY_SIZE = 1024*1024*21
--every read size 16*1024 16kb
local EVERY_READ_SIZE = 16384
--max every read size 256kb
local MAX_EVERY_READ_SIZE = 262144

--connect time out(ms)
local CONNECT_TIME_OUT = 1000

--read time out(ms)
local READ_TIME_OUT = 5000

local READ_WRITE_IDLE = 15000
local PROCESS_TIMEOUT = 60000

--format cookie
local function build_header_cookie(cookie, extra_header)
    if not cookie then
        return extra_header
    end
    if (not extra_header) or type(extra_header) ~= "table" then
        extra_header = {}
    end
    local ck_type = type(cookie)
    if ck_type == "table" then
        local ck_arr, str_tmp = nil, nil
        for k,v in pairs(cookie) do
            str_tmp = table.concat({k,v}, '=')
            table.insert(ck_arr, str_tmp)
        end
        if ck_arr then
            extra_header["cookie"] = table.concat(ck_arr, "; ")
        end
    elseif ck_type == "string" then
        extra_header["cookie"] = cookie .. "; "
    end
    return extra_header
end

-- build http request header
local function build_http_request_header(method, url, headers, addr)
    if type(headers) ~= "table" then
        return nil, "request headers is empty"
    end

    local req_headers, req_line = {}, nil

    --set first line header
    req_line = string.format("%s %s HTTP/1.1\r\n", method, url)
    table.insert(req_headers, req_line)

    --set host
    if nil == headers["Host"] and addr and addr.domain and addr.port then
      local host = string.format("Host: %s:%d\r\n", addr.domain, addr.port)
      table.insert(req_headers, host)
    end

    --don't need, copy request has not Content-Length
    --if method == "PUT" or method == "POST" then
    --    if nil == headers["Content-Length"] then
    --        return nil, "Content-Length not exists"
    --    end
    --end

    --set other header
    for key, val in pairs(headers) do
        req_line = string.format("%s: %s\r\n", key, val)
        table.insert(req_headers, req_line)
    end

    --close headers
    table.insert(req_headers, "\r\n")
    return req_headers
end

-- read response first line status
function read_status_line(socket)
    local data, err, partial = socket:receive()
    if not data then
        return nil, "(read status line failed) " .. (err or "")
    end

    local s_pos, e_pos, code = string.find(data, "HTTP/%d%.%d (%d%d%d)")

    return tonumber(code), data
end

-- read http response header
function read_response_headers(socket)
    local headers = {}
    repeat
        -- read header
        local line, err, partial = socket:receive()
        if err then
            return nil, err 
        end
        -- get field-name and value
        local __, __, name, value = string.find(line, "^([^:]+):%s*(.*)$")
        if name then
            name = string.lower(name)
            headers[name] = value or ''
        end
    until (line == '')
    return headers
end

local function read_body_data(socket, size, callback)
    local p_size = EVERY_READ_SIZE
    while size and size > 0 do
        if size < p_size then
            p_size = size
        end
        local data, err, partial = socket:receive(p_size)
        if not err then
            if data then
                callback(data, #data)
            end
        elseif err == "closed" then
            if partial then
                callback(partial, #partial)
            end
            return true, err -- 'closed'
        else
            return nil, err
        end
        size = size - p_size
    end
    return true
end

-- read response body
local function read_response_body(socket, headers)
    local transfer_encoding = headers["transfer-encoding"] -- shortcut
    local body = {}
    local read_length = 0
    local callback = function (data, data_len)
        table.insert(body, data)
        read_length = read_length + data_len
    end

    if transfer_encoding and transfer_encoding ~= "identity" then
        while true do
            --obtain chunked length
            local data, err, partial = socket:receive()
            if not err then
                if data == "0" then
                    return table.concat(body, '') -- end of chunk
                else
                    -- 16 to 10 integer, obtain current data length
                    local length = tonumber(data, 16)
                    local ok, err = read_body_data(socket, length, callback)
                    if not ok then
                        return nil, err
                    end
                end
            else
                return nil, err
            end
        end
    elseif headers["content-length"] ~= nil and headers["content-length"] ~= "0" then
        -- content length
        local length = tonumber(headers["content-length"])
        if length > MAX_BODY_SIZE then
            ngx.log(ngx.INFO, "content-length > MAX_BODY_SIZE ")
            length = MAX_BODY_SIZE
        end

        local ok, err = read_body_data(socket, length, callback)
        if not ok then
            return nil, err
        end
    --else
    --    -- connection close
    --    local ok, err = read_body_data(socket, MAX_BODY_SIZE, callback)
    --    if not ok then
    --        return nil, err
    --    end
    end
    return table.concat(body, '')
end

local function is_read_response_body(code)
    if code == 204 or code == 304 then 
        return nil 
    end
    if code >= 100 and code < 200 then 
        return nil 
    end
    return true
end

-- read response header & body
function read_response(socket)
    local headers, body, err, status = nil, nil, nil, nil
    local code, data = read_status_line(socket)
    if not code then
        socket:close()
        if not data then
            return nil, "read status line failed "
        else
            return nil, data
        end
    end

    -- ignore any 100-continue messages
    while code == 100 do
        headers, err = read_response_headers(socket)
        code, status = read_status_line(socket)
    end

    -- read headers
    headers, err = read_response_headers(socket)
    if err then
        socket:close()
        return nil, "read headers failed " .. err
    end

    -- read body
    if is_read_response_body(code) then
        body, err = read_response_body(socket, headers)
        if not err then
            socket:close()
            return {code=code, headers=headers, body=(body or '')}
        end
    end
   	
    socket:close()
    return nil , err or "unknown error" 
end

--connect backend, send request, receive response
local function send_request_and_receive_response(addr, req, connect_timeout, read_timeout)
    --connect & send http request
    local socket = tcp()
    connect_timeout = connect_timeout or CONNECT_TIME_OUT
    socket:settimeout(connect_timeout)
    local ret, err = socket:connect(addr.domain, addr.port)
    if not ret then
        return RETRY, "(connect error) " .. (err or "")
    end
    --NOTE: if need set timeout?
    ret, err = socket:send(req)
    if not ret then
        socket:close()
        return RETRY, "(send request error) " .. (err or "")
    end

    --receive response
    read_timeout = read_timeout or READ_TIME_OUT
    socket:settimeout(read_timeout)
    ret, err = read_response(socket)
    if err then
        return RETRY, "(read response error) " .. (err or "")
    end
    return ret
end

--get the socket handler
function get_socket(addr, connect_timeout)
    local socket = tcp()
    connect_timeout = connect_timeout or CONNECT_TIME_OUT
    socket:settimeout(connect_timeout)
    local ret, err = socket:connect(addr.domain, addr.port)
    if not ret then
        return nil, err
    end
    socket:settimeout(READ_WRITE_IDLE)
    return socket
end

--build request & send it to backend
function send_request(socket, method, url, headers, body)
    if not socket then
        return RETRY, "invalid socket"
    end

    --http request line & headers
    local req, err = build_http_request_header(method, url, headers, nil)
    if not req then
        socket:close()
        return REQ_ERROR, err
    end

    --request body(if has)
    if body and
        ("PUT" == method or "POST" == method) then
        table.insert(req, body)
    end

    local ret, err = socket:send(req)
    if not ret then
        socket:close()
        return RETRY, err
    end

    return 0
end

--send data to socket
function send_request_body(socket, body)
    if (not socket) or (not body) then
        return nil, "socket or body invalid"
    end

    local ret, err = socket:send(body)
    if not ret then
        socket:close()
        return nil, err
    end

    return 0
end

function read_status_line_and_response_headers(socket, process_timeout)
    local headers, body, err = nil, nil, nil
    local timeout = process_timeout or PROCESS_TIMEOUT
    socket:settimeout(timeout)
    local code, data = read_status_line(socket)
    if not code then
        socket:close()
        if not data then
            return nil, "read status line failed "
        else
            return nil, data
        end
    end
    socket:settimeout(READ_WRITE_IDLE)

    -- read headers
    headers, err = read_response_headers(socket)
    if err then
        socket:close()
        return nil, "read headers failed " .. err
    end

    return {code=code, headers=headers}
end

--[[
  @bref : read response body streaming for reading huge body
  @param socket : socket with backend
  @param is_chunked : chunked data or not
  @param left_len : if not chunked, left_len = Content-Length - read_length
  @return : read_data & is_finished for success
            nil & err_msg for error
--]]
function read_response_body_streaming(socket, is_chunked, left_len)
    local left_len = tonumber(left_len) or 0
    local body = {}
    local read_length = 0
    local callback = function (data, data_len)
        table.insert(body, data)
        read_length = read_length + data_len
    end
--    local callback =  function (data)
--            table.insert(body, data)
--    end

    local is_finished = false

    if 0 == is_chunked then
        if left_len > 0 then
            local len = left_len
            if len > MAX_EVERY_READ_SIZE then
                len = MAX_EVERY_READ_SIZE
            end
            local ok, err = read_body_data(socket, len, callback)
            if not ok then
                return nil, err
            end
            if (read_length == left_len) or (err == "closed") then
                is_finished = true
            end
        else
            is_finished = true
        end
    else
        local read_size = 0
        while true do
            --obtain chunked length
            local data, err, partial = socket:receive()
            if not err then
                if data == "0" then
                    --return table.concat(body, '') -- end of chunk
                    is_finished = true
                    break
                else
                    -- 16 to 10 integer, obtain current data length
                    local length = tonumber(data, 16)
                    local ok, err = read_body_data(socket, length, callback)
                    if not ok then
                        return nil, err
                    elseif err == "closed" then
                        --socket closed, reading finished
                        is_finished = true
                        break
                    end

                    if read_length >= MAX_EVERY_READ_SIZE then
                        break
                    end
                end
            else
                return nil, err
            end
        end
    end

    return {body=table.concat(body, ''), finished=is_finished}
end

--read response body with fixed length, used in reading huge body streaming
function read_fixed_length_body(socket, max_size, is_chunked)
    local body = {}
    local read_length = 0
    local callback = function (data, data_len)
        table.insert(body, data)
        read_length = read_length + data_len
    end

    if 0 == is_chunked then
        local ok, err = read_body_data(socket, max_size, callback)
        if not ok then
            return nil, err
        end
    else
        local read_size = 0
        while true do
            --obtain chunked length
            local data, err, partial = socket:receive()
            if not err then
                if data == "0" then
                    --return table.concat(body, '') -- end of chunk
                    break
                else
                    -- 16 to 10 integer, obtain current data length
                    local length = tonumber(data, 16)
                    local ok, err = read_body_data(socket, length, callback)
                    if not ok then
                        return nil, err
                    end
                    read_size = read_size + length
                    if read_size >= max_size then
                        break
                    end
                end
            else
                return nil, err
            end
        end
    end

    return table.concat(body, '')
end

--http get
function get(addr, url, headers, timeout)
    --http request line & headers
    local req, err = build_http_request_header(METHOD_GET, url, headers, addr)
    if not req then
        return nil, err
    end

    timeout = timeout or {}
    return send_request_and_receive_response(addr, req, timeout.connect_timeout, timeout.read_timeout)
end

--http head
function head(addr, url, headers, timeout)
    --http request line & headers
    local req, err = build_http_request_header(METHOD_HEAD, url, headers, addr)
    if not req then
        return nil, err
    end

    timeout = timeout or {}
    return send_request_and_receive_response(addr, req, timeout.connect_timeout, timeout.read_timeout)
end

--http put
function put(addr, url, headers, body, timeout)
    --check request
    if type(headers) ~= "table" then
        return nil, "request headers is empty"
    end
    local len = headers["Content-Length"]
    if not len then
        return nil, "Content-Length not exists"
    end
    len = tonumber(len)
    if body and len ~= #body then
        return nil, "Content-Length not equal to body"
    elseif not body and len ~= 0 then
        return nil, "Content-Length not equal to body"
    end

    --http request line & headers
    local req, err = build_http_request_header(METHOD_PUT, url, headers, addr)
    if not req then
        return nil, err
    end

    --request body
    table.insert(req, body)

    timeout = timeout or {}
    return send_request_and_receive_response(addr, req, timeout.connect_timeout, timeout.read_timeout)
end

--http post
function post(addr, url, headers, body, timeout)
    --check request
    if type(headers) ~= "table" then
        return nil, "request headers is empty"
    end
    if nil == headers["Content-Type"] then
        return nil, "Content-Type not exists"
    end
    local len = headers["Content-Length"]
    if not len then
        return nil, "Content-Length not exists"
    end
    len = tonumber(len)
    if body and len ~= #body then
        return nil, "Content-Length not equal to body"
    elseif not body and len ~= 0 then
        return nil, "Content-Length not equal to body"
    end

    --http request line & headers
    local req, err = build_http_request_header(METHOD_POST, url, headers, addr)
    if not req then
        return nil, err
    end

    --request body
    table.insert(req, body)

    timeout = timeout or {}
    return send_request_and_receive_response(addr, req, timeout.connect_timeout, timeout.read_timeout)
end

--http delete
function delete(addr, url, headers, timeout)
    --http request line & headers
    local req, err = build_http_request_header(METHOD_DELETE, url, headers, addr)
    if not req then
        return nil, err
    end

    timeout = timeout or {}
    return send_request_and_receive_response(addr, req, timeout.connect_timeout, timeout.read_timeout)

end
