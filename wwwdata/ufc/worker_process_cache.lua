local ngx_worker_cache = require "ngx_worker_cache"
local conf = require "conf"
local cjson = require "cjson.safe"
local shared_memory = require "shared_memory"
local moniter = require "moniter"
local utils = require "utils"

local _M = {
	["doc"] = "worker cache function in worker process"
}

--worker进程对 ngx_worker_cache 独有的函数操作抽离出来放到这里
--一些公共的操作还是放在ngx_worker_cache.lua里面
--这个文件里面仅仅只是函数操作，数据还是放在ngx_worker_cache里面

--latest is true, will try to sync latest cloud config even local config is ok
--latest is false, will just load local config if local config is ok, else will load cloud config
function _M.load_config(premature, latest)
    local r1, r2
    -- load local config only if lastest is false
    r1 = ngx_worker_cache._load_local_config()
    if not latest then
        if r1 == conf["res"]["SUCCESS"] then
			--如果redis配置中有idc相关配置，替换conf
			ngx_worker_cache.update_idc_conf()
            _M._init_loaded_cache()
            return conf["res"]["SUCCESS"]
        end
    end

	--初始化一下配置保存计数
	if not ngx_worker_cache["config_update_time"] then
		ngx_worker_cache["config_update_time"] = 0
	end
	ngx_worker_cache["config_update_time"] = ngx_worker_cache["config_update_time"] + 1

	r2 = _M._load_shared_memmory_config()

	if r2 == conf["res"]["SUCCESS"] then
		--如果redis配置中有idc相关配置，替换conf
		ngx_worker_cache.update_idc_conf()
		ngx_worker_cache.save_need_info()
		_M._init_loaded_cache()
	end

	if ngx_worker_cache["config_update_time"] and ngx_worker_cache["config_update_time"] >= conf["unimportant_config_update_interval"] then
		ngx_worker_cache["config_update_time"] = 0
	end
	return r2
end

function _M._init_loaded_cache()
	--有些老的流量可能没有传service name，需要靠这个兼容
	_M._init_old_version_compatible()
    --其它的配置从redis里面获取，故障注入配置从共享内存获取
    --stream模块就不用同步故障注入配置了
    if conf["stream"] == 0 then
        _M.load_fault_config()
    end
    --目前分配ip之前会同步一下共享内存不需要这个逻辑了，
    --_M.load_ip_mapping_data()
    --设置bns_list auto配置的逻辑在特权进程里面做了
    --_M._init_static_cache()
	--初始化负载均衡数据
    _M._init_dynamic_cache()
    ngx_worker_cache._init()
end

--这里不用初始化具体负载均衡index，这样会导致轮询策略刚启动的时候所有机器index 都是一样的
function _M._init_dynamic_cache()
    if ngx_worker_cache["not_shared_dynamic_cache"] == nil then
        ngx_worker_cache["not_shared_dynamic_cache"] = {}
    end
	if not ngx_worker_cache["static_cache"]["services"] then
		--除非刚启动手动把need_services文件删掉不然不可能走到这里
		ngx_worker_cache["static_cache"]["services"] = {}
	end
    for service_name, _ in pairs(ngx_worker_cache["static_cache"]["services"]) do
		if ngx_worker_cache["not_shared_dynamic_cache"][service_name] == nil then
			ngx_worker_cache["not_shared_dynamic_cache"][service_name] = {}
			local dy_config = ngx_worker_cache["not_shared_dynamic_cache"][service_name]
			if conf["meta_table"] then
				dy_config["current_index"] = {}
			end
		end
		if ngx_worker_cache["dynamic_cache"][service_name] == nil then
			ngx_worker_cache["dynamic_cache"][service_name] = {}
			local dy_config = ngx_worker_cache["dynamic_cache"][service_name]
			dy_config["backend"] = {}
			if not conf["meta_table"] then
				dy_config["current_index"] = {}
			end
		end
    end
end

function _M._init_old_version_compatible()
    local uri_and_host_services = {}
    local uri, host, use_host, old_service_name
    ngx_worker_cache["static_cache"]["old"] = uri_and_host_services -- use to store old style service_name(uri and host)
	if not ngx_worker_cache["static_cache"]["services"] then
		--除非刚启动手动把need_services文件删掉不然不可能走到这里
		ngx_worker_cache["static_cache"]["services"] = {}
	end
    for service_name, config in pairs(ngx_worker_cache["static_cache"]["services"]) do
        if config["bns_list"] and config["uri"] then
            uri = config["uri"]
            host = config["host"]
			use_host = false
			old_service_name = uri
			if host ~= nil then
				old_service_name = string.format("%s_%s", old_service_name, host)
				use_host = true
			end

			--ngx.log(ngx.DEBUG, string.format("old version compatibl dealing with uri %s", uri))
			if not config[uri] then
				uri_and_host_services[uri] = {}
			end
			uri_and_host_services[uri]["use_host"] = use_host

			if not config[old_service_name] then
				uri_and_host_services[old_service_name] = {}
			end
			uri_and_host_services[old_service_name]["service_name"] = service_name
			config["old_service_name"] = old_service_name
        end
    end
end

function _M.load_fault_config()
    local shared_fault_data = ngx.shared.fault
	local body = shared_fault_data:get("fault")

	if not body then
		return nil
	end

	local fault_data = cjson.decode(body)
	if not fault_data then
		shared_fault_data:set("fault", nil)
		ngx.log(ngx.WARN, string.format("inject data is not a json"))
		return
	end

	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		return nil
	end

	local configs = static_cache["configs"]
	if not configs then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache][configs] is nil"))
		return nil
	end

    configs["fault"] = fault_data
    return configs["fault"]
end

function _M.load_dump_config()
    local shared_dump_data = ngx.shared.dump
	local body = shared_dump_data:get("dump")

	if not body then
		return nil
	end

	local dump_data = cjson.decode(body)
	if not dump_data then
		shared_dump_data:set("dump", nil)
		ngx.log(ngx.WARN, string.format("inject dump data is not a json"))
		return
	end

	local static_cache = ngx_worker_cache["static_cache"]
	if not static_cache then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache] is nil"))
		return nil
	end

	local configs = static_cache["configs"]
	if not configs then
		ngx.log(ngx.WARN, string.format("ngx_worker_cache[static_cache][configs] is nil"))
		return nil
	end

    configs["dump"] = dump_data
    return configs["dump"]
end

--worker进程的配置全都是从共享内存里面拿
function _M._load_shared_memmory_config()
    local res
    local perfix
    local cached_global_mtime, global_mtime

    global_mtime = shared_memory.get_global_mtime()
    if global_mtime == nil then
        ngx.log(ngx.WARN, string.format("load config, global mtime get fail"))
        return conf["res"]["FAIL"]
    end

    cached_global_mtime = ngx_worker_cache["static_cache"]["mtime"]
    if cached_global_mtime and cached_global_mtime == global_mtime then
        ngx.log(ngx.NOTICE, string.format("load config, global mtime unchange %s, stop loading", global_mtime))
        return conf["res"]["NONEED"]
    end
    ngx.log(ngx.NOTICE, string.format("load config, global mtime changed, local:%s, cloud:%s", cached_global_mtime, global_mtime))

	--这里先加载bns数据再加载service数据，不然切流换bns这种情况可能会有service bns变了，但是bns数据还没有更新的问题
    res = _M._load_one_type_shared_memmory_config("bns")
    ngx.log(ngx.NOTICE, string.format("load config, cloud bnss res is %s", res))
    if res == conf["res"]["FAIL"] then
        return conf["res"]["FAIL"]
    end

    res = _M._load_one_type_shared_memmory_config("service")
    ngx.log(ngx.NOTICE, string.format("load config, cloud services res is %s", res))

    if res == conf["res"]["FAIL"] then
        return conf["res"]["FAIL"]
    end

	-- load system config
	res = _M._load_one_type_shared_memmory_config("config")
    ngx.log(ngx.NOTICE, string.format("load config, cloud configs res is %s", res))
    if res == conf["res"]["FAIL"] then
        return conf["res"]["FAIL"]
    end

	--为了保证stream模块和http模块可以使用同一份本地配置文件，它们需要加载两份映射配置
	--这两份映射正好是相对的
	res = _M._load_one_type_shared_memmory_config("http_ip")
	ngx.log(ngx.NOTICE, string.format("load config, cloud http_ip res is %s", res))
    if res == conf["res"]["FAIL"] then
        return conf["res"]["FAIL"]
	end
	
	res = _M._load_one_type_shared_memmory_config("stream_ip")
    ngx.log(ngx.NOTICE, string.format("load config, cloud stream_ip res is %s", res))
    if res == conf["res"]["FAIL"] then
        return conf["res"]["FAIL"]
    end

    if res == conf["res"]["FAIL"] then
        return conf["res"]["FAIL"]
    end

    ngx_worker_cache["static_cache"]["mtime"] = global_mtime
	-- 需要更新下 worker_latency
	local update_timestamp = shared_memory.get_global_mtime_update_timestamp()
	if update_timestamp then 
		-- 只有当特权进程更新时才计算 worker_latency，否则没必要更新
		local now_worker_latency = ngx.time() - update_timestamp
		local old_worker_latency = shared_memory.get_worker_latency() or -1
		if now_worker_latency > old_worker_latency then
			-- worker 进程里面取最大值写入，当上报后重置该值为 -1
			shared_memory.set_worker_latency(now_worker_latency)
		end
	end 
    return conf["res"]["SUCCESS"]
end

function _M._load_one_type_shared_memmory_config(config_type)
	local config_cnt, err = shared_memory.get_config_cnt(config_type)
	
    if config_cnt == nil then
        ngx.log(ngx.WARN, string.format("config %s cnt miss, err is %s",
		config_type, err))
        return conf["res"]["FAIL"]
	end

	local res
	for i=0, config_cnt-1 do
		res = _M._load_one_shared_memmory_config(config_type, i)
		if res == conf["res"]["FAIL"] then
			return conf["res"]["FAIL"]
		end
		--加载完一个配置之后切出去一次，要不进程会阻塞
		ngx.sleep(0)
	end
	return conf["res"]["SUCCESS"]
end

function _M._load_one_shared_memmory_config(config_type, i)
	local config_name, config, err, config_mtime
	config_name, err= shared_memory.get_config_name(config_type, i)
	if config_name == nil then
		ngx.log(ngx.WARN, string.format("config %s, index %d miss, err is %s",
		config_type, i, err))
		return conf["res"]["FAIL"]
	end

	--更新ufc meta信息
	if config_type == "service" or config_type == "bns" then
		if not ngx_worker_cache["ufc_meta_info"] then
			ngx_worker_cache["ufc_meta_info"] = {}
		end
	
		if not ngx_worker_cache["ufc_meta_info"][config_type .. "s"] then
			ngx_worker_cache["ufc_meta_info"][config_type .. "s"] = {}
		end
		ngx_worker_cache["ufc_meta_info"][config_type .. "s"][config_name] = 1
	end
	config_mtime, err = shared_memory.get_config_mtime(config_type, config_name)
	if config_mtime == nil then
		ngx.log(ngx.WARN, string.format("config %s mtime miss, err is %s", config_name, err))
		return conf["res"]["FAIL"]
	end
	if not _M._is_config_changed(config_type, config_name, config_mtime) then
		return conf["res"]["NONEED"]
	end

	config, err = _M._real_load_one_shared_memmory_config(config_type, config_name)
	if not config or err then
		return conf["res"]["FAIL"]
	end

	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		ngx_worker_cache["static_cache"][config_type .. "s"] = {}
	end

	ngx_worker_cache["static_cache"][config_type .. "s"][config_name] = config
	--对于service 配置需要额外处理一下高级路由策略配置
	if config_type == "service" then
		_M.advanced_route_service_config_add(config_name, config)
	end
	return conf["res"]["SUCCESS"]
end

function _M._real_load_one_shared_memmory_config(config_type, config_name)
	local err, config_encode, config
	config_encode, err = shared_memory.get_config_content(config_type, config_name)
	if err or config_encode == nil then
		ngx.log(ngx.WARN, string.format("config_type %s config_name %s miss, err is %s",
			config_type, config_name, err))
		return nil, err or "config null"
	end
	config, err = cjson.decode(config_encode)
	if not config or err then
        return nil, err or "decode fail"
    end
    return config, nil
end

--命中高级路由策略之后service name后面会加上:appName后缀
--此时static cache里面是没有这个service name相关的配置的
--后面一些逻辑会通过service name 从static cache获取service相关配置，需要手动加一下这个配置
--这个配置不会保存在磁盘上，也不会保存在共享内存里面，只会临时的存在worker 进程的内存里面
--因此重启之后配置重新从磁盘上加载的时候他不会被读到内存里面
function _M.advanced_route_service_config_add(service_name, service_config)
	if not service_config["advanced_route"] or not service_config["advanced_route"]["route_app"] then
		return
	end
	local route_app = service_config["advanced_route"]["route_app"]
	if type(route_app ) ~= "table" then
		return
	end
	
	for app, bns_list in pairs(route_app) do
		--原来的service 配置table做深拷贝
		local new_service_config = utils.deepcopy(service_config)
		--替换bns_list配置
		new_service_config["bns_list"] = bns_list
		--命中高级路由策略之后就不需要小流量配置了
		new_service_config["small_flow"] = nil
		local new_service_name = string.format("%s:%s", service_name, app)
		ngx_worker_cache["static_cache"]["services"][new_service_name] = new_service_config
	end
end

--高级路由策略service config不会存在磁盘上，重启的时候就没了
--有命中高级路由策略的流量过来，并且内存里面没有这个配置的时候需要额外添加一下这个配置
--这个配置后面在service 配置更新的时候也会被跟着更新
function _M.add_one_advanced_route_service_config(new_service_name, service_config, bns_list)
	if ngx_worker_cache["static_cache"]["services"][new_service_name] then
		--这个配置内存里面已经有了
		return
	end
	--原来的service 配置table做深拷贝
	local new_service_config = utils.deepcopy(service_config)
	--替换bns_list配置
	new_service_config["bns_list"] = bns_list
	--命中高级路由策略之后就不需要小流量配置了
	new_service_config["small_flow"] = nil
	ngx_worker_cache["static_cache"]["services"][new_service_name] = new_service_config
end

--对比config是否更新
function _M._is_config_changed(config_type, config_name, config_mtime)
	--判断一下是否在需要更新的配置列表里面
	if not _M.is_needed_config(config_type, config_name) then
		return false
	end

	local cached_mtime, cached_service_config

	if not ngx_worker_cache["static_cache"][config_type .. "s"] then
		return true
	end

	cached_service_config = ngx_worker_cache["static_cache"][config_type .. "s"][config_name]
	if cached_service_config then
		cached_mtime = cached_service_config["mtime"]
	end

	if config_type == "bns" and cached_service_config and not cached_service_config["backend_map"] then
		--这个目前应该是没用了，这个backend_map数据结构上了很久了
		--新上线数据结构，强制更新
		return true
	end

	if not cached_service_config or cached_mtime ~= config_mtime then
		return true
	end

	--idcmap配置需要额外判断一下
	if config_type == "bns" then
		local idcmap_mtime =shared_memory.get_config_mtime("idcmap", config_name)
		if not idcmap_mtime then
			return false
		end

		if not cached_service_config["idcmap_mtime"] then
			return false
		end

		if idcmap_mtime ~= cached_service_config["idcmap_mtime"] then
			return true
		end
	end

	return false
end

--只对bns配置使用没流量的每五次更新一次策略
function _M.is_needed_config(config_type, config_name)
	--沙盒bns更新频率较低这个策略容易导致数据长时间不更新
	--沙盒不需要考虑性能问题，
	--去除每五次更新一次这个逻辑
	if conf["sandbox"] == true then
		return true
	end
	local config_type_temp = config_type

	if config_type == "idcmap" then
		--这个老逻辑没有用，这个逻辑在在worker进程，config_type只会是bns
		config_type_temp = "bns"
	end

	if config_type_temp ~= "bns" then
		return true
	end
	--新注册内存里面没有的立即更新
	if not ngx_worker_cache["static_cache"] or not ngx_worker_cache["static_cache"][config_type_temp.."s"] or  not ngx_worker_cache["static_cache"][config_type_temp.."s"][config_name] then
		return true
	end


	local config_content = ngx_worker_cache["static_cache"][config_type_temp.."s"][config_name]

	if config_type_temp == "bns" and ngx_worker_cache["need_bnss"][config_name] ~= nil then
		return true
	end

	--不在需要更新的配置选项里面，每5次更新一次
	if not config_content["update_time"] then
		config_content["update_time"] = 0
	end

	config_content["update_time"] = config_content["update_time"] + 1
	if config_content["update_time"] >= conf["unimportant_config_update_interval"] then
		config_content["update_time"] = 0
		return true
	end

	--ngx.log(ngx.NOTICE, string.format("config type: %s, config name: %s, skip this update", config_type, config_name))
	return false
end


return _M
